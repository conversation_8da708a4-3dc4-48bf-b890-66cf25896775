import{A as K,B as ne,m as b,_ as T,C as f,P as N,L as e,a0 as ce,D as P,F as k,t as g,Q as F,X as S,Y as D,b as n,H as de,I as pe,J as Q,G as ue,N as W,ak as ve,S as me,al as fe,a3 as ge,w as he,a4 as $e,az as ye}from"./SpinnerAugment-B-W1rkU5.js";import"./design-system-init-CO3OATOl.js";import{c as w,W as A}from"./IconButtonAugment-Cdot7Te3.js";import{B as be}from"./ButtonAugment-BsoJM5iW.js";import{O as we}from"./OpenFileButton-DyxQXXwJ.js";import{C as Ee,R as Re,E as ze,T as Fe,a as B}from"./index-BZ1aQDkr.js";import{M as X,R as Y}from"./message-broker-DiyMl4p8.js";import{M as ke}from"./MarkdownEditor-BtN9HMBx.js";import{R as Me}from"./RulesModeSelector-CLcZ4jbN.js";import{C as Ce}from"./chevron-left-C7kz8ykV.js";import{T as xe}from"./CardAugment-6M90JowR.js";import{l as Le}from"./chevron-down-BvquqDa7.js";import"./chat-model-context-xODWxtdR.js";import"./index-C4gKbsWy.js";import"./index-Bmm_dv1C.js";import"./remote-agents-client-DbZSIpd6.js";import"./types-CGlLNakm.js";import"./ra-diff-ops-model-BYdL3GOo.js";import"./TextAreaAugment-DEiWzBPO.js";import"./BaseTextInput-NmI9DKfY.js";import"./async-messaging-h5fbTmxI.js";import"./focusTrapStack-wx6NNrdM.js";import"./isObjectLike-KnT2wtGt.js";var Te=k('<div class="c-rule-config svelte-1r8al3d"><div class="c-rule-field c-rule-field-full-width svelte-1r8al3d"><!> <!></div></div>'),Ne=k('<div class="l-file-controls svelte-1r8al3d" slot="header"><div class="l-file-controls-left svelte-1r8al3d"><div class="c-trigger-section svelte-1r8al3d"><!> <!> <!></div></div> <!></div>'),Se=k("<div>Loading...</div>"),De=k('<div class="c-rules-container svelte-1vbu0zh"><!></div>');ye(function(_,q){K(q,!1);const[U,j]=ge(),G=()=>$e(H,"$rule",U),O=new X(w),H=he(null),V={handleMessageFromExtension(s){const t=s.data;if(t&&t.type===A.loadFile&&t){const h=t.data.content;if(h!==void 0){const a=h.replace(/^\n+/,""),o=B.getDescriptionFrontmatterKey(a),E=B.getRuleTypeFromContent(a),r=B.extractContent(a);H.set({path:t.data.pathName,content:r,type:E,description:o})}}return!0}};ve(()=>{O.registerConsumer(V),w.postMessage({type:A.rulesLoaded})}),P();var I=De();me("message",fe,function(...s){var t;(t=O.onMessageFromExtension)==null||t.apply(this,s)});var Z=g(I),ee=s=>{(function(t,h){K(h,!1);const a=b(),o=b(),E=b();let r=ne(h,"rule",12),$=b(r().content),i=b(r().description);const J=new X(w),se=new Ee,ae=new ze(w,J,se),re=new Re(J),M=async(l,p)=>{r({...r(),type:l,description:p||e(i)}),p!==void 0&&f(i,p);try{await re.updateRuleContent({type:l,path:e(a),content:e($),description:p||e(i)})}catch(c){console.error("RulesMarkdownEditor: Error in rulesModel.updateRuleContent:",c)}},oe=Le.debounce(M,500),ie=()=>{w.postMessage({type:A.openSettingsPage,data:"guidelines"})};T(()=>N(r()),()=>{f(a,r().path)}),T(()=>N(r()),()=>{f(o,r().type)}),T(()=>(e(a),e(o),e($),e(i)),()=>{f(E,{path:e(a),type:e(o),content:e($),description:e(i)})}),ce(),P(),ke(t,{saveFunction:()=>M(e(o),e(i)),variant:"surface",size:2,resize:"vertical",class:"markdown-editor",get value(){return e($)},set value(l){f($,l)},children:(l,p)=>{var c=de(),R=pe(c),C=u=>{var y=Te(),x=g(y),z=g(x);S(z,{size:1,class:"c-field-label",children:(v,m)=>{var L=D("Description");n(v,L)},$$slots:{default:!0}});var d=F(z,2);Fe(d,{placeholder:"When should this rules file be fetched by the Agent?",size:1,get value(){return e(i)},set value(v){f(i,v)},$$events:{input:()=>oe(e(o),e(i))},$$legacy:!0}),n(u,y)};Q(R,u=>{e(o),N(Y),ue(()=>e(o)===Y.AGENT_REQUESTED)&&u(C)}),n(l,c)},$$slots:{default:!0,header:(l,p)=>{var c=Ne(),R=g(c),C=g(R),u=g(C);xe(u,{content:"Navigate back to all Rules & Guidelines",children:(d,v)=>{be(d,{size:1,variant:"ghost-block",color:"neutral",class:"c-back-button",$$events:{click:ie},$$slots:{iconLeft:(m,L)=>{Ce(m,{slot:"iconLeft"})}}})},$$slots:{default:!0}});var y=F(u,2);S(y,{size:1,class:"c-field-label",children:(d,v)=>{var m=D("Trigger:");n(d,m)},$$slots:{default:!0}});var x=F(y,2);Me(x,{onSave:M,get rule(){return e(E)}});var z=F(R,2);we(z,{size:1,get path(){return e(a)},onOpenLocalFile:async()=>(ae.openFile({repoRoot:"",pathName:e(a)}),"success"),$$slots:{text:(d,v)=>{S(d,{slot:"text",size:1,children:(m,L)=>{var le=D("Open file");n(m,le)},$$slots:{default:!0}})}}}),n(l,c)}},$$legacy:!0}),W()})(s,{get rule(){return G()}})},te=s=>{var t=Se();n(s,t)};Q(Z,s=>{G()!==null?s(ee):s(te,!1)}),n(_,I),W(),j()},{target:document.getElementById("app")});
