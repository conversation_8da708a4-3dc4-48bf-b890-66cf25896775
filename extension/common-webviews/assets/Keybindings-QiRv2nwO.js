var _l=Object.defineProperty;var $l=(r,e,t)=>e in r?_l(r,e,{enumerable:!0,configurable:!0,writable:!0,value:t}):r[e]=t;var b=(r,e,t)=>$l(r,typeof e!="symbol"?e+"":e,t);import{A as Ie,B as j,D as ze,F as Y,T as ve,Z as je,L as Q,t as ne,b as R,ab as Mn,N as _e,aD as Ge,w as Te,aA as ce,aH as Rl,u as Ye,ak as Pl,a6 as Wn,_ as Fe,m as Ct,C as Qe,P as T,a0 as vt,H as De,I as ie,J as U,a4 as it,a3 as bn,Y as St,a$ as Vr,z as Wo,v as Gs,a5 as zl,G as P,Q as le,K as Se,S as ke,M as Je,am as Co,f as bt,X as Vt,l as Fl,a as Bl,W as Vl,b3 as Hl}from"./SpinnerAugment-B-W1rkU5.js";import{e as Zs,i as Ys,a as ql,b as nt,h as jl}from"./IconButtonAugment-Cdot7Te3.js";import{C as Kl}from"./CalloutAugment-BjqqGsdn.js";import{E as Xs}from"./exclamation-triangle-teKEBgrZ.js";import{C as Jl,b as st,T as Wl}from"./CardAugment-6M90JowR.js";import{D as Lt,U as Ul,V as Gl,W as Ht,X as vo,Y as ci,Z as Qs,_ as ea,$ as ta,r as Zl,v as sr,y as ar,B as lr,z as cr,x as hr,w as dr,J as pr,K as ur,a0 as hi,G as fr,a1 as di,g as pi,h as ui}from"./index-BZ1aQDkr.js";import{t as Yl}from"./index-Bmm_dv1C.js";import{c as wo}from"./svelte-component-BFVtr7-z.js";import{P as Zn}from"./message-broker-DiyMl4p8.js";import{F as Ae}from"./Filespan-CTglRHBI.js";import{M as pn}from"./MaterialIcon-DOMySnAa.js";import{P as Xl}from"./pen-to-square-CZY7t64W.js";import{A as Ql}from"./augment-logo-DrXxAvzA.js";import{F as fi,b as ec}from"./folder-opened-DL5VRsqP.js";var tc=Y('<span class="c-keyboard-shortcut-hint__icon svelte-1txw16l"> </span>'),nc=Y("<span></span>");function wp(r,e){Ie(e,!1);let t=j(e,"class",8,""),n=j(e,"keybinding",24,()=>{}),o=j(e,"icons",24,()=>{var s;return((s=n())==null?void 0:s.split("-"))??[]});ze();var i=nc();Zs(i,5,o,Ys,(s,a)=>{var l=tc(),c=ne(l);ve(()=>je(c,Q(a))),R(s,l)}),ve(()=>Mn(i,1,`c-keyboard-shortcut-hint ${t()}`,"svelte-1txw16l")),R(r,i),_e()}function Ce(r){this.content=r}function na(r,e,t){for(let n=0;;n++){if(n==r.childCount||n==e.childCount)return r.childCount==e.childCount?null:t;let o=r.child(n),i=e.child(n);if(o!=i){if(!o.sameMarkup(i))return t;if(o.isText&&o.text!=i.text){for(let s=0;o.text[s]==i.text[s];s++)t++;return t}if(o.content.size||i.content.size){let s=na(o.content,i.content,t+1);if(s!=null)return s}t+=o.nodeSize}else t+=o.nodeSize}}function ra(r,e,t,n){for(let o=r.childCount,i=e.childCount;;){if(o==0||i==0)return o==i?null:{a:t,b:n};let s=r.child(--o),a=e.child(--i),l=s.nodeSize;if(s!=a){if(!s.sameMarkup(a))return{a:t,b:n};if(s.isText&&s.text!=a.text){let c=0,h=Math.min(s.text.length,a.text.length);for(;c<h&&s.text[s.text.length-c-1]==a.text[a.text.length-c-1];)c++,t--,n--;return{a:t,b:n}}if(s.content.size||a.content.size){let c=ra(s.content,a.content,t-1,n-1);if(c)return c}t-=l,n-=l}else t-=l,n-=l}}Ce.prototype={constructor:Ce,find:function(r){for(var e=0;e<this.content.length;e+=2)if(this.content[e]===r)return e;return-1},get:function(r){var e=this.find(r);return e==-1?void 0:this.content[e+1]},update:function(r,e,t){var n=t&&t!=r?this.remove(t):this,o=n.find(r),i=n.content.slice();return o==-1?i.push(t||r,e):(i[o+1]=e,t&&(i[o]=t)),new Ce(i)},remove:function(r){var e=this.find(r);if(e==-1)return this;var t=this.content.slice();return t.splice(e,2),new Ce(t)},addToStart:function(r,e){return new Ce([r,e].concat(this.remove(r).content))},addToEnd:function(r,e){var t=this.remove(r).content.slice();return t.push(r,e),new Ce(t)},addBefore:function(r,e,t){var n=this.remove(e),o=n.content.slice(),i=n.find(r);return o.splice(i==-1?o.length:i,0,e,t),new Ce(o)},forEach:function(r){for(var e=0;e<this.content.length;e+=2)r(this.content[e],this.content[e+1])},prepend:function(r){return(r=Ce.from(r)).size?new Ce(r.content.concat(this.subtract(r).content)):this},append:function(r){return(r=Ce.from(r)).size?new Ce(this.subtract(r).content.concat(r.content)):this},subtract:function(r){var e=this;r=Ce.from(r);for(var t=0;t<r.content.length;t+=2)e=e.remove(r.content[t]);return e},toObject:function(){var r={};return this.forEach(function(e,t){r[e]=t}),r},get size(){return this.content.length>>1}},Ce.from=function(r){if(r instanceof Ce)return r;var e=[];if(r)for(var t in r)e.push(t,r[t]);return new Ce(e)};class S{constructor(e,t){if(this.content=e,this.size=t||0,t==null)for(let n=0;n<e.length;n++)this.size+=e[n].nodeSize}nodesBetween(e,t,n,o=0,i){for(let s=0,a=0;a<t;s++){let l=this.content[s],c=a+l.nodeSize;if(c>e&&n(l,o+a,i||null,s)!==!1&&l.content.size){let h=a+1;l.nodesBetween(Math.max(0,e-h),Math.min(l.content.size,t-h),n,o+h)}a=c}}descendants(e){this.nodesBetween(0,this.size,e)}textBetween(e,t,n,o){let i="",s=!0;return this.nodesBetween(e,t,(a,l)=>{let c=a.isText?a.text.slice(Math.max(e,l)-l,t-l):a.isLeaf?o?typeof o=="function"?o(a):o:a.type.spec.leafText?a.type.spec.leafText(a):"":"";a.isBlock&&(a.isLeaf&&c||a.isTextblock)&&n&&(s?s=!1:i+=n),i+=c},0),i}append(e){if(!e.size)return this;if(!this.size)return e;let t=this.lastChild,n=e.firstChild,o=this.content.slice(),i=0;for(t.isText&&t.sameMarkup(n)&&(o[o.length-1]=t.withText(t.text+n.text),i=1);i<e.content.length;i++)o.push(e.content[i]);return new S(o,this.size+e.size)}cut(e,t=this.size){if(e==0&&t==this.size)return this;let n=[],o=0;if(t>e)for(let i=0,s=0;s<t;i++){let a=this.content[i],l=s+a.nodeSize;l>e&&((s<e||l>t)&&(a=a.isText?a.cut(Math.max(0,e-s),Math.min(a.text.length,t-s)):a.cut(Math.max(0,e-s-1),Math.min(a.content.size,t-s-1))),n.push(a),o+=a.nodeSize),s=l}return new S(n,o)}cutByIndex(e,t){return e==t?S.empty:e==0&&t==this.content.length?this:new S(this.content.slice(e,t))}replaceChild(e,t){let n=this.content[e];if(n==t)return this;let o=this.content.slice(),i=this.size+t.nodeSize-n.nodeSize;return o[e]=t,new S(o,i)}addToStart(e){return new S([e].concat(this.content),this.size+e.nodeSize)}addToEnd(e){return new S(this.content.concat(e),this.size+e.nodeSize)}eq(e){if(this.content.length!=e.content.length)return!1;for(let t=0;t<this.content.length;t++)if(!this.content[t].eq(e.content[t]))return!1;return!0}get firstChild(){return this.content.length?this.content[0]:null}get lastChild(){return this.content.length?this.content[this.content.length-1]:null}get childCount(){return this.content.length}child(e){let t=this.content[e];if(!t)throw new RangeError("Index "+e+" out of range for "+this);return t}maybeChild(e){return this.content[e]||null}forEach(e){for(let t=0,n=0;t<this.content.length;t++){let o=this.content[t];e(o,n,t),n+=o.nodeSize}}findDiffStart(e,t=0){return na(this,e,t)}findDiffEnd(e,t=this.size,n=e.size){return ra(this,e,t,n)}findIndex(e,t=-1){if(e==0)return Yn(0,e);if(e==this.size)return Yn(this.content.length,e);if(e>this.size||e<0)throw new RangeError(`Position ${e} outside of fragment (${this})`);for(let n=0,o=0;;n++){let i=o+this.child(n).nodeSize;if(i>=e)return i==e||t>0?Yn(n+1,i):Yn(n,o);o=i}}toString(){return"<"+this.toStringInner()+">"}toStringInner(){return this.content.join(", ")}toJSON(){return this.content.length?this.content.map(e=>e.toJSON()):null}static fromJSON(e,t){if(!t)return S.empty;if(!Array.isArray(t))throw new RangeError("Invalid input for Fragment.fromJSON");return new S(t.map(e.nodeFromJSON))}static fromArray(e){if(!e.length)return S.empty;let t,n=0;for(let o=0;o<e.length;o++){let i=e[o];n+=i.nodeSize,o&&i.isText&&e[o-1].sameMarkup(i)?(t||(t=e.slice(0,o)),t[t.length-1]=i.withText(t[t.length-1].text+i.text)):t&&t.push(i)}return new S(t||e,n)}static from(e){if(!e)return S.empty;if(e instanceof S)return e;if(Array.isArray(e))return this.fromArray(e);if(e.attrs)return new S([e],e.nodeSize);throw new RangeError("Can not convert "+e+" to a Fragment"+(e.nodesBetween?" (looks like multiple versions of prosemirror-model were loaded)":""))}}S.empty=new S([],0);const Hr={index:0,offset:0};function Yn(r,e){return Hr.index=r,Hr.offset=e,Hr}function mr(r,e){if(r===e)return!0;if(!r||typeof r!="object"||!e||typeof e!="object")return!1;let t=Array.isArray(r);if(Array.isArray(e)!=t)return!1;if(t){if(r.length!=e.length)return!1;for(let n=0;n<r.length;n++)if(!mr(r[n],e[n]))return!1}else{for(let n in r)if(!(n in e)||!mr(r[n],e[n]))return!1;for(let n in e)if(!(n in r))return!1}return!0}let oe=class bo{constructor(e,t){this.type=e,this.attrs=t}addToSet(e){let t,n=!1;for(let o=0;o<e.length;o++){let i=e[o];if(this.eq(i))return e;if(this.type.excludes(i.type))t||(t=e.slice(0,o));else{if(i.type.excludes(this.type))return e;!n&&i.type.rank>this.type.rank&&(t||(t=e.slice(0,o)),t.push(this),n=!0),t&&t.push(i)}}return t||(t=e.slice()),n||t.push(this),t}removeFromSet(e){for(let t=0;t<e.length;t++)if(this.eq(e[t]))return e.slice(0,t).concat(e.slice(t+1));return e}isInSet(e){for(let t=0;t<e.length;t++)if(this.eq(e[t]))return!0;return!1}eq(e){return this==e||this.type==e.type&&mr(this.attrs,e.attrs)}toJSON(){let e={type:this.type.name};for(let t in this.attrs){e.attrs=this.attrs;break}return e}static fromJSON(e,t){if(!t)throw new RangeError("Invalid input for Mark.fromJSON");let n=e.marks[t.type];if(!n)throw new RangeError(`There is no mark type ${t.type} in this schema`);let o=n.create(t.attrs);return n.checkAttrs(o.attrs),o}static sameSet(e,t){if(e==t)return!0;if(e.length!=t.length)return!1;for(let n=0;n<e.length;n++)if(!e[n].eq(t[n]))return!1;return!0}static setFrom(e){if(!e||Array.isArray(e)&&e.length==0)return bo.none;if(e instanceof bo)return[e];let t=e.slice();return t.sort((n,o)=>n.type.rank-o.type.rank),t}};oe.none=[];class gr extends Error{}class D{constructor(e,t,n){this.content=e,this.openStart=t,this.openEnd=n}get size(){return this.content.size-this.openStart-this.openEnd}insertAt(e,t){let n=ia(this.content,e+this.openStart,t);return n&&new D(n,this.openStart,this.openEnd)}removeBetween(e,t){return new D(oa(this.content,e+this.openStart,t+this.openStart),this.openStart,this.openEnd)}eq(e){return this.content.eq(e.content)&&this.openStart==e.openStart&&this.openEnd==e.openEnd}toString(){return this.content+"("+this.openStart+","+this.openEnd+")"}toJSON(){if(!this.content.size)return null;let e={content:this.content.toJSON()};return this.openStart>0&&(e.openStart=this.openStart),this.openEnd>0&&(e.openEnd=this.openEnd),e}static fromJSON(e,t){if(!t)return D.empty;let n=t.openStart||0,o=t.openEnd||0;if(typeof n!="number"||typeof o!="number")throw new RangeError("Invalid input for Slice.fromJSON");return new D(S.fromJSON(e,t.content),n,o)}static maxOpen(e,t=!0){let n=0,o=0;for(let i=e.firstChild;i&&!i.isLeaf&&(t||!i.type.spec.isolating);i=i.firstChild)n++;for(let i=e.lastChild;i&&!i.isLeaf&&(t||!i.type.spec.isolating);i=i.lastChild)o++;return new D(e,n,o)}}function oa(r,e,t){let{index:n,offset:o}=r.findIndex(e),i=r.maybeChild(n),{index:s,offset:a}=r.findIndex(t);if(o==e||i.isText){if(a!=t&&!r.child(s).isText)throw new RangeError("Removing non-flat range");return r.cut(0,e).append(r.cut(t))}if(n!=s)throw new RangeError("Removing non-flat range");return r.replaceChild(n,i.copy(oa(i.content,e-o-1,t-o-1)))}function ia(r,e,t,n){let{index:o,offset:i}=r.findIndex(e),s=r.maybeChild(o);if(i==e||s.isText)return r.cut(0,e).append(t).append(r.cut(e));let a=ia(s.content,e-i-1,t);return a&&r.replaceChild(o,s.copy(a))}function rc(r,e,t){if(t.openStart>r.depth)throw new gr("Inserted content deeper than insertion position");if(r.depth-t.openStart!=e.depth-t.openEnd)throw new gr("Inconsistent open depths");return sa(r,e,t,0)}function sa(r,e,t,n){let o=r.index(n),i=r.node(n);if(o==e.index(n)&&n<r.depth-t.openStart){let s=sa(r,e,t,n+1);return i.copy(i.content.replaceChild(o,s))}if(t.content.size){if(t.openStart||t.openEnd||r.depth!=n||e.depth!=n){let{start:s,end:a}=function(l,c){let h=c.depth-l.openStart,d=c.node(h).copy(l.content);for(let p=h-1;p>=0;p--)d=c.node(p).copy(S.from(d));return{start:d.resolveNoCache(l.openStart+h),end:d.resolveNoCache(d.content.size-l.openEnd-h)}}(t,r);return en(i,la(r,s,a,e,n))}{let s=r.parent,a=s.content;return en(s,a.cut(0,r.parentOffset).append(t.content).append(a.cut(e.parentOffset)))}}return en(i,yr(r,e,n))}function aa(r,e){if(!e.type.compatibleContent(r.type))throw new gr("Cannot join "+e.type.name+" onto "+r.type.name)}function xo(r,e,t){let n=r.node(t);return aa(n,e.node(t)),n}function Qt(r,e){let t=e.length-1;t>=0&&r.isText&&r.sameMarkup(e[t])?e[t]=r.withText(e[t].text+r.text):e.push(r)}function Rn(r,e,t,n){let o=(e||r).node(t),i=0,s=e?e.index(t):o.childCount;r&&(i=r.index(t),r.depth>t?i++:r.textOffset&&(Qt(r.nodeAfter,n),i++));for(let a=i;a<s;a++)Qt(o.child(a),n);e&&e.depth==t&&e.textOffset&&Qt(e.nodeBefore,n)}function en(r,e){return r.type.checkContent(e),r.copy(e)}function la(r,e,t,n,o){let i=r.depth>o&&xo(r,e,o+1),s=n.depth>o&&xo(t,n,o+1),a=[];return Rn(null,r,o,a),i&&s&&e.index(o)==t.index(o)?(aa(i,s),Qt(en(i,la(r,e,t,n,o+1)),a)):(i&&Qt(en(i,yr(r,e,o+1)),a),Rn(e,t,o,a),s&&Qt(en(s,yr(t,n,o+1)),a)),Rn(n,null,o,a),new S(a)}function yr(r,e,t){let n=[];return Rn(null,r,t,n),r.depth>t&&Qt(en(xo(r,e,t+1),yr(r,e,t+1)),n),Rn(e,null,t,n),new S(n)}D.empty=new D(S.empty,0,0);class Vn{constructor(e,t,n){this.pos=e,this.path=t,this.parentOffset=n,this.depth=t.length/3-1}resolveDepth(e){return e==null?this.depth:e<0?this.depth+e:e}get parent(){return this.node(this.depth)}get doc(){return this.node(0)}node(e){return this.path[3*this.resolveDepth(e)]}index(e){return this.path[3*this.resolveDepth(e)+1]}indexAfter(e){return e=this.resolveDepth(e),this.index(e)+(e!=this.depth||this.textOffset?1:0)}start(e){return(e=this.resolveDepth(e))==0?0:this.path[3*e-1]+1}end(e){return e=this.resolveDepth(e),this.start(e)+this.node(e).content.size}before(e){if(!(e=this.resolveDepth(e)))throw new RangeError("There is no position before the top-level node");return e==this.depth+1?this.pos:this.path[3*e-1]}after(e){if(!(e=this.resolveDepth(e)))throw new RangeError("There is no position after the top-level node");return e==this.depth+1?this.pos:this.path[3*e-1]+this.path[3*e].nodeSize}get textOffset(){return this.pos-this.path[this.path.length-1]}get nodeAfter(){let e=this.parent,t=this.index(this.depth);if(t==e.childCount)return null;let n=this.pos-this.path[this.path.length-1],o=e.child(t);return n?e.child(t).cut(n):o}get nodeBefore(){let e=this.index(this.depth),t=this.pos-this.path[this.path.length-1];return t?this.parent.child(e).cut(0,t):e==0?null:this.parent.child(e-1)}posAtIndex(e,t){t=this.resolveDepth(t);let n=this.path[3*t],o=t==0?0:this.path[3*t-1]+1;for(let i=0;i<e;i++)o+=n.child(i).nodeSize;return o}marks(){let e=this.parent,t=this.index();if(e.content.size==0)return oe.none;if(this.textOffset)return e.child(t).marks;let n=e.maybeChild(t-1),o=e.maybeChild(t);if(!n){let a=n;n=o,o=a}let i=n.marks;for(var s=0;s<i.length;s++)i[s].type.spec.inclusive!==!1||o&&i[s].isInSet(o.marks)||(i=i[s--].removeFromSet(i));return i}marksAcross(e){let t=this.parent.maybeChild(this.index());if(!t||!t.isInline)return null;let n=t.marks,o=e.parent.maybeChild(e.index());for(var i=0;i<n.length;i++)n[i].type.spec.inclusive!==!1||o&&n[i].isInSet(o.marks)||(n=n[i--].removeFromSet(n));return n}sharedDepth(e){for(let t=this.depth;t>0;t--)if(this.start(t)<=e&&this.end(t)>=e)return t;return 0}blockRange(e=this,t){if(e.pos<this.pos)return e.blockRange(this);for(let n=this.depth-(this.parent.inlineContent||this.pos==e.pos?1:0);n>=0;n--)if(e.pos<=this.end(n)&&(!t||t(this.node(n))))return new Cr(this,e,n);return null}sameParent(e){return this.pos-this.parentOffset==e.pos-e.parentOffset}max(e){return e.pos>this.pos?e:this}min(e){return e.pos<this.pos?e:this}toString(){let e="";for(let t=1;t<=this.depth;t++)e+=(e?"/":"")+this.node(t).type.name+"_"+this.index(t-1);return e+":"+this.parentOffset}static resolve(e,t){if(!(t>=0&&t<=e.content.size))throw new RangeError("Position "+t+" out of range");let n=[],o=0,i=t;for(let s=e;;){let{index:a,offset:l}=s.content.findIndex(i),c=i-l;if(n.push(s,a,o+l),!c||(s=s.child(a),s.isText))break;i=c-1,o+=l+1}return new Vn(t,n,i)}static resolveCached(e,t){let n=mi.get(e);if(n)for(let i=0;i<n.elts.length;i++){let s=n.elts[i];if(s.pos==t)return s}else mi.set(e,n=new oc);let o=n.elts[n.i]=Vn.resolve(e,t);return n.i=(n.i+1)%ic,o}}class oc{constructor(){this.elts=[],this.i=0}}const ic=12,mi=new WeakMap;class Cr{constructor(e,t,n){this.$from=e,this.$to=t,this.depth=n}get start(){return this.$from.before(this.depth+1)}get end(){return this.$to.after(this.depth+1)}get parent(){return this.$from.node(this.depth)}get startIndex(){return this.$from.index(this.depth)}get endIndex(){return this.$to.indexAfter(this.depth)}}const sc=Object.create(null);let tn=class ko{constructor(e,t,n,o=oe.none){this.type=e,this.attrs=t,this.marks=o,this.content=n||S.empty}get nodeSize(){return this.isLeaf?1:2+this.content.size}get childCount(){return this.content.childCount}child(e){return this.content.child(e)}maybeChild(e){return this.content.maybeChild(e)}forEach(e){this.content.forEach(e)}nodesBetween(e,t,n,o=0){this.content.nodesBetween(e,t,n,o,this)}descendants(e){this.nodesBetween(0,this.content.size,e)}get textContent(){return this.isLeaf&&this.type.spec.leafText?this.type.spec.leafText(this):this.textBetween(0,this.content.size,"")}textBetween(e,t,n,o){return this.content.textBetween(e,t,n,o)}get firstChild(){return this.content.firstChild}get lastChild(){return this.content.lastChild}eq(e){return this==e||this.sameMarkup(e)&&this.content.eq(e.content)}sameMarkup(e){return this.hasMarkup(e.type,e.attrs,e.marks)}hasMarkup(e,t,n){return this.type==e&&mr(this.attrs,t||e.defaultAttrs||sc)&&oe.sameSet(this.marks,n||oe.none)}copy(e=null){return e==this.content?this:new ko(this.type,this.attrs,e,this.marks)}mark(e){return e==this.marks?this:new ko(this.type,this.attrs,this.content,e)}cut(e,t=this.content.size){return e==0&&t==this.content.size?this:this.copy(this.content.cut(e,t))}slice(e,t=this.content.size,n=!1){if(e==t)return D.empty;let o=this.resolve(e),i=this.resolve(t),s=n?0:o.sharedDepth(t),a=o.start(s),l=o.node(s).content.cut(o.pos-a,i.pos-a);return new D(l,o.depth-s,i.depth-s)}replace(e,t,n){return rc(this.resolve(e),this.resolve(t),n)}nodeAt(e){for(let t=this;;){let{index:n,offset:o}=t.content.findIndex(e);if(t=t.maybeChild(n),!t)return null;if(o==e||t.isText)return t;e-=o+1}}childAfter(e){let{index:t,offset:n}=this.content.findIndex(e);return{node:this.content.maybeChild(t),index:t,offset:n}}childBefore(e){if(e==0)return{node:null,index:0,offset:0};let{index:t,offset:n}=this.content.findIndex(e);if(n<e)return{node:this.content.child(t),index:t,offset:n};let o=this.content.child(t-1);return{node:o,index:t-1,offset:n-o.nodeSize}}resolve(e){return Vn.resolveCached(this,e)}resolveNoCache(e){return Vn.resolve(this,e)}rangeHasMark(e,t,n){let o=!1;return t>e&&this.nodesBetween(e,t,i=>(n.isInSet(i.marks)&&(o=!0),!o)),o}get isBlock(){return this.type.isBlock}get isTextblock(){return this.type.isTextblock}get inlineContent(){return this.type.inlineContent}get isInline(){return this.type.isInline}get isText(){return this.type.isText}get isLeaf(){return this.type.isLeaf}get isAtom(){return this.type.isAtom}toString(){if(this.type.spec.toDebugString)return this.type.spec.toDebugString(this);let e=this.type.name;return this.content.size&&(e+="("+this.content.toStringInner()+")"),ca(this.marks,e)}contentMatchAt(e){let t=this.type.contentMatch.matchFragment(this.content,0,e);if(!t)throw new Error("Called contentMatchAt on a node with invalid content");return t}canReplace(e,t,n=S.empty,o=0,i=n.childCount){let s=this.contentMatchAt(e).matchFragment(n,o,i),a=s&&s.matchFragment(this.content,t);if(!a||!a.validEnd)return!1;for(let l=o;l<i;l++)if(!this.type.allowsMarks(n.child(l).marks))return!1;return!0}canReplaceWith(e,t,n,o){if(o&&!this.type.allowsMarks(o))return!1;let i=this.contentMatchAt(e).matchType(n),s=i&&i.matchFragment(this.content,t);return!!s&&s.validEnd}canAppend(e){return e.content.size?this.canReplace(this.childCount,this.childCount,e.content):this.type.compatibleContent(e.type)}check(){this.type.checkContent(this.content),this.type.checkAttrs(this.attrs);let e=oe.none;for(let t=0;t<this.marks.length;t++){let n=this.marks[t];n.type.checkAttrs(n.attrs),e=n.addToSet(e)}if(!oe.sameSet(e,this.marks))throw new RangeError(`Invalid collection of marks for node ${this.type.name}: ${this.marks.map(t=>t.type.name)}`);this.content.forEach(t=>t.check())}toJSON(){let e={type:this.type.name};for(let t in this.attrs){e.attrs=this.attrs;break}return this.content.size&&(e.content=this.content.toJSON()),this.marks.length&&(e.marks=this.marks.map(t=>t.toJSON())),e}static fromJSON(e,t){if(!t)throw new RangeError("Invalid input for Node.fromJSON");let n;if(t.marks){if(!Array.isArray(t.marks))throw new RangeError("Invalid mark data for Node.fromJSON");n=t.marks.map(e.markFromJSON)}if(t.type=="text"){if(typeof t.text!="string")throw new RangeError("Invalid text node in JSON");return e.text(t.text,n)}let o=S.fromJSON(e,t.content),i=e.nodeType(t.type).create(t.attrs,o,n);return i.type.checkAttrs(i.attrs),i}};tn.prototype.text=void 0;class vr extends tn{constructor(e,t,n,o){if(super(e,t,null,o),!n)throw new RangeError("Empty text nodes are not allowed");this.text=n}toString(){return this.type.spec.toDebugString?this.type.spec.toDebugString(this):ca(this.marks,JSON.stringify(this.text))}get textContent(){return this.text}textBetween(e,t){return this.text.slice(e,t)}get nodeSize(){return this.text.length}mark(e){return e==this.marks?this:new vr(this.type,this.attrs,this.text,e)}withText(e){return e==this.text?this:new vr(this.type,this.attrs,e,this.marks)}cut(e=0,t=this.text.length){return e==0&&t==this.text.length?this:this.withText(this.text.slice(e,t))}eq(e){return this.sameMarkup(e)&&this.text==e.text}toJSON(){let e=super.toJSON();return e.text=this.text,e}}function ca(r,e){for(let t=r.length-1;t>=0;t--)e=r[t].type.name+"("+e+")";return e}class on{constructor(e){this.validEnd=e,this.next=[],this.wrapCache=[]}static parse(e,t){let n=new ac(e,t);if(n.next==null)return on.empty;let o=ha(n);n.next&&n.err("Unexpected trailing text");let i=function(s){let a=Object.create(null);return l(yi(s,0));function l(c){let h=[];c.forEach(p=>{s[p].forEach(({term:f,to:u})=>{if(!f)return;let m;for(let g=0;g<h.length;g++)h[g][0]==f&&(m=h[g][1]);yi(s,u).forEach(g=>{m||h.push([f,m=[]]),m.indexOf(g)==-1&&m.push(g)})})});let d=a[c.join(",")]=new on(c.indexOf(s.length-1)>-1);for(let p=0;p<h.length;p++){let f=h[p][1].sort(da);d.next.push({type:h[p][0],next:a[f.join(",")]||l(f)})}return d}}(function(s){let a=[[]];return h(d(s,0),l()),a;function l(){return a.push([])-1}function c(p,f,u){let m={term:u,to:f};return a[p].push(m),m}function h(p,f){p.forEach(u=>u.to=f)}function d(p,f){if(p.type=="choice")return p.exprs.reduce((u,m)=>u.concat(d(m,f)),[]);if(p.type!="seq"){if(p.type=="star"){let u=l();return c(f,u),h(d(p.expr,u),u),[c(u)]}if(p.type=="plus"){let u=l();return h(d(p.expr,f),u),h(d(p.expr,u),u),[c(u)]}if(p.type=="opt")return[c(f)].concat(d(p.expr,f));if(p.type=="range"){let u=f;for(let m=0;m<p.min;m++){let g=l();h(d(p.expr,u),g),u=g}if(p.max==-1)h(d(p.expr,u),u);else for(let m=p.min;m<p.max;m++){let g=l();c(u,g),h(d(p.expr,u),g),u=g}return[c(u)]}if(p.type=="name")return[c(f,void 0,p.value)];throw new Error("Unknown expr type")}for(let u=0;;u++){let m=d(p.exprs[u],f);if(u==p.exprs.length-1)return m;h(m,f=l())}}}(o));return function(s,a){for(let l=0,c=[s];l<c.length;l++){let h=c[l],d=!h.validEnd,p=[];for(let f=0;f<h.next.length;f++){let{type:u,next:m}=h.next[f];p.push(u.name),!d||u.isText||u.hasRequiredAttrs()||(d=!1),c.indexOf(m)==-1&&c.push(m)}d&&a.err("Only non-generatable nodes ("+p.join(", ")+") in a required position (see https://prosemirror.net/docs/guide/#generatable)")}}(i,n),i}matchType(e){for(let t=0;t<this.next.length;t++)if(this.next[t].type==e)return this.next[t].next;return null}matchFragment(e,t=0,n=e.childCount){let o=this;for(let i=t;o&&i<n;i++)o=o.matchType(e.child(i).type);return o}get inlineContent(){return this.next.length!=0&&this.next[0].type.isInline}get defaultType(){for(let e=0;e<this.next.length;e++){let{type:t}=this.next[e];if(!t.isText&&!t.hasRequiredAttrs())return t}return null}compatible(e){for(let t=0;t<this.next.length;t++)for(let n=0;n<e.next.length;n++)if(this.next[t].type==e.next[n].type)return!0;return!1}fillBefore(e,t=!1,n=0){let o=[this];return function i(s,a){let l=s.matchFragment(e,n);if(l&&(!t||l.validEnd))return S.from(a.map(c=>c.createAndFill()));for(let c=0;c<s.next.length;c++){let{type:h,next:d}=s.next[c];if(!h.isText&&!h.hasRequiredAttrs()&&o.indexOf(d)==-1){o.push(d);let p=i(d,a.concat(h));if(p)return p}}return null}(this,[])}findWrapping(e){for(let n=0;n<this.wrapCache.length;n+=2)if(this.wrapCache[n]==e)return this.wrapCache[n+1];let t=this.computeWrapping(e);return this.wrapCache.push(e,t),t}computeWrapping(e){let t=Object.create(null),n=[{match:this,type:null,via:null}];for(;n.length;){let o=n.shift(),i=o.match;if(i.matchType(e)){let s=[];for(let a=o;a.type;a=a.via)s.push(a.type);return s.reverse()}for(let s=0;s<i.next.length;s++){let{type:a,next:l}=i.next[s];a.isLeaf||a.hasRequiredAttrs()||a.name in t||o.type&&!l.validEnd||(n.push({match:a.contentMatch,type:a,via:o}),t[a.name]=!0)}}return null}get edgeCount(){return this.next.length}edge(e){if(e>=this.next.length)throw new RangeError(`There's no ${e}th edge in this content match`);return this.next[e]}toString(){let e=[];return function t(n){e.push(n);for(let o=0;o<n.next.length;o++)e.indexOf(n.next[o].next)==-1&&t(n.next[o].next)}(this),e.map((t,n)=>{let o=n+(t.validEnd?"*":" ")+" ";for(let i=0;i<t.next.length;i++)o+=(i?", ":"")+t.next[i].type.name+"->"+e.indexOf(t.next[i].next);return o}).join(`
`)}}on.empty=new on(!0);class ac{constructor(e,t){this.string=e,this.nodeTypes=t,this.inline=null,this.pos=0,this.tokens=e.split(/\s*(?=\b|\W|$)/),this.tokens[this.tokens.length-1]==""&&this.tokens.pop(),this.tokens[0]==""&&this.tokens.shift()}get next(){return this.tokens[this.pos]}eat(e){return this.next==e&&(this.pos++||!0)}err(e){throw new SyntaxError(e+" (in content expression '"+this.string+"')")}}function ha(r){let e=[];do e.push(lc(r));while(r.eat("|"));return e.length==1?e[0]:{type:"choice",exprs:e}}function lc(r){let e=[];do e.push(cc(r));while(r.next&&r.next!=")"&&r.next!="|");return e.length==1?e[0]:{type:"seq",exprs:e}}function cc(r){let e=function(t){if(t.eat("(")){let n=ha(t);return t.eat(")")||t.err("Missing closing paren"),n}if(!/\W/.test(t.next)){let n=function(o,i){let s=o.nodeTypes,a=s[i];if(a)return[a];let l=[];for(let c in s){let h=s[c];h.isInGroup(i)&&l.push(h)}return l.length==0&&o.err("No node type or group '"+i+"' found"),l}(t,t.next).map(o=>(t.inline==null?t.inline=o.isInline:t.inline!=o.isInline&&t.err("Mixing inline and block content"),{type:"name",value:o}));return t.pos++,n.length==1?n[0]:{type:"choice",exprs:n}}t.err("Unexpected token '"+t.next+"'")}(r);for(;;)if(r.eat("+"))e={type:"plus",expr:e};else if(r.eat("*"))e={type:"star",expr:e};else if(r.eat("?"))e={type:"opt",expr:e};else{if(!r.eat("{"))break;e=hc(r,e)}return e}function gi(r){/\D/.test(r.next)&&r.err("Expected number, got '"+r.next+"'");let e=Number(r.next);return r.pos++,e}function hc(r,e){let t=gi(r),n=t;return r.eat(",")&&(n=r.next!="}"?gi(r):-1),r.eat("}")||r.err("Unclosed braced range"),{type:"range",min:t,max:n,expr:e}}function da(r,e){return e-r}function yi(r,e){let t=[];return function n(o){let i=r[o];if(i.length==1&&!i[0].term)return n(i[0].to);t.push(o);for(let s=0;s<i.length;s++){let{term:a,to:l}=i[s];a||t.indexOf(l)!=-1||n(l)}}(e),t.sort(da)}function pa(r){let e=Object.create(null);for(let t in r){let n=r[t];if(!n.hasDefault)return null;e[t]=n.default}return e}function ua(r,e){let t=Object.create(null);for(let n in r){let o=e&&e[n];if(o===void 0){let i=r[n];if(!i.hasDefault)throw new RangeError("No value supplied for attribute "+n);o=i.default}t[n]=o}return t}function fa(r,e,t,n){for(let o in e)if(!(o in r))throw new RangeError(`Unsupported attribute ${o} for ${t} of type ${o}`);for(let o in r){let i=r[o];i.validate&&i.validate(e[o])}}function ma(r,e){let t=Object.create(null);if(e)for(let n in e)t[n]=new dc(r,n,e[n]);return t}let Ci=class ga{constructor(e,t,n){this.name=e,this.schema=t,this.spec=n,this.markSet=null,this.groups=n.group?n.group.split(" "):[],this.attrs=ma(e,n.attrs),this.defaultAttrs=pa(this.attrs),this.contentMatch=null,this.inlineContent=null,this.isBlock=!(n.inline||e=="text"),this.isText=e=="text"}get isInline(){return!this.isBlock}get isTextblock(){return this.isBlock&&this.inlineContent}get isLeaf(){return this.contentMatch==on.empty}get isAtom(){return this.isLeaf||!!this.spec.atom}isInGroup(e){return this.groups.indexOf(e)>-1}get whitespace(){return this.spec.whitespace||(this.spec.code?"pre":"normal")}hasRequiredAttrs(){for(let e in this.attrs)if(this.attrs[e].isRequired)return!0;return!1}compatibleContent(e){return this==e||this.contentMatch.compatible(e.contentMatch)}computeAttrs(e){return!e&&this.defaultAttrs?this.defaultAttrs:ua(this.attrs,e)}create(e=null,t,n){if(this.isText)throw new Error("NodeType.create can't construct text nodes");return new tn(this,this.computeAttrs(e),S.from(t),oe.setFrom(n))}createChecked(e=null,t,n){return t=S.from(t),this.checkContent(t),new tn(this,this.computeAttrs(e),t,oe.setFrom(n))}createAndFill(e=null,t,n){if(e=this.computeAttrs(e),(t=S.from(t)).size){let s=this.contentMatch.fillBefore(t);if(!s)return null;t=s.append(t)}let o=this.contentMatch.matchFragment(t),i=o&&o.fillBefore(S.empty,!0);return i?new tn(this,e,t.append(i),oe.setFrom(n)):null}validContent(e){let t=this.contentMatch.matchFragment(e);if(!t||!t.validEnd)return!1;for(let n=0;n<e.childCount;n++)if(!this.allowsMarks(e.child(n).marks))return!1;return!0}checkContent(e){if(!this.validContent(e))throw new RangeError(`Invalid content for node ${this.name}: ${e.toString().slice(0,50)}`)}checkAttrs(e){fa(this.attrs,e,"node",this.name)}allowsMarkType(e){return this.markSet==null||this.markSet.indexOf(e)>-1}allowsMarks(e){if(this.markSet==null)return!0;for(let t=0;t<e.length;t++)if(!this.allowsMarkType(e[t].type))return!1;return!0}allowedMarks(e){if(this.markSet==null)return e;let t;for(let n=0;n<e.length;n++)this.allowsMarkType(e[n].type)?t&&t.push(e[n]):t||(t=e.slice(0,n));return t?t.length?t:oe.none:e}static compile(e,t){let n=Object.create(null);e.forEach((i,s)=>n[i]=new ga(i,t,s));let o=t.spec.topNode||"doc";if(!n[o])throw new RangeError("Schema is missing its top node type ('"+o+"')");if(!n.text)throw new RangeError("Every schema needs a 'text' type");for(let i in n.text.attrs)throw new RangeError("The text node type should not have attributes");return n}};class dc{constructor(e,t,n){this.hasDefault=Object.prototype.hasOwnProperty.call(n,"default"),this.default=n.default,this.validate=typeof n.validate=="string"?function(o,i,s){let a=s.split("|");return l=>{let c=l===null?"null":typeof l;if(a.indexOf(c)<0)throw new RangeError(`Expected value of type ${a} for attribute ${i} on type ${o}, got ${c}`)}}(e,t,n.validate):n.validate}get isRequired(){return!this.hasDefault}}class Lr{constructor(e,t,n,o){this.name=e,this.rank=t,this.schema=n,this.spec=o,this.attrs=ma(e,o.attrs),this.excluded=null;let i=pa(this.attrs);this.instance=i?new oe(this,i):null}create(e=null){return!e&&this.instance?this.instance:new oe(this,ua(this.attrs,e))}static compile(e,t){let n=Object.create(null),o=0;return e.forEach((i,s)=>n[i]=new Lr(i,o++,t,s)),n}removeFromSet(e){for(var t=0;t<e.length;t++)e[t].type==this&&(e=e.slice(0,t).concat(e.slice(t+1)),t--);return e}isInSet(e){for(let t=0;t<e.length;t++)if(e[t].type==this)return e[t]}checkAttrs(e){fa(this.attrs,e,"mark",this.name)}excludes(e){return this.excluded.indexOf(e)>-1}}class pc{constructor(e){this.linebreakReplacement=null,this.cached=Object.create(null);let t=this.spec={};for(let o in e)t[o]=e[o];t.nodes=Ce.from(e.nodes),t.marks=Ce.from(e.marks||{}),this.nodes=Ci.compile(this.spec.nodes,this),this.marks=Lr.compile(this.spec.marks,this);let n=Object.create(null);for(let o in this.nodes){if(o in this.marks)throw new RangeError(o+" can not be both a node and a mark");let i=this.nodes[o],s=i.spec.content||"",a=i.spec.marks;if(i.contentMatch=n[s]||(n[s]=on.parse(s,this.nodes)),i.inlineContent=i.contentMatch.inlineContent,i.spec.linebreakReplacement){if(this.linebreakReplacement)throw new RangeError("Multiple linebreak nodes defined");if(!i.isInline||!i.isLeaf)throw new RangeError("Linebreak replacement nodes must be inline leaf nodes");this.linebreakReplacement=i}i.markSet=a=="_"?null:a?vi(this,a.split(" ")):a!=""&&i.inlineContent?null:[]}for(let o in this.marks){let i=this.marks[o],s=i.spec.excludes;i.excluded=s==null?[i]:s==""?[]:vi(this,s.split(" "))}this.nodeFromJSON=this.nodeFromJSON.bind(this),this.markFromJSON=this.markFromJSON.bind(this),this.topNodeType=this.nodes[this.spec.topNode||"doc"],this.cached.wrappings=Object.create(null)}node(e,t=null,n,o){if(typeof e=="string")e=this.nodeType(e);else{if(!(e instanceof Ci))throw new RangeError("Invalid node type: "+e);if(e.schema!=this)throw new RangeError("Node type from different schema used ("+e.name+")")}return e.createChecked(t,n,o)}text(e,t){let n=this.nodes.text;return new vr(n,n.defaultAttrs,e,oe.setFrom(t))}mark(e,t){return typeof e=="string"&&(e=this.marks[e]),e.create(t)}nodeFromJSON(e){return tn.fromJSON(this,e)}markFromJSON(e){return oe.fromJSON(this,e)}nodeType(e){let t=this.nodes[e];if(!t)throw new RangeError("Unknown node type: "+e);return t}}function vi(r,e){let t=[];for(let n=0;n<e.length;n++){let o=e[n],i=r.marks[o],s=i;if(i)t.push(i);else for(let a in r.marks){let l=r.marks[a];(o=="_"||l.spec.group&&l.spec.group.split(" ").indexOf(o)>-1)&&t.push(s=l)}if(!s)throw new SyntaxError("Unknown mark type: '"+e[n]+"'")}return t}class Sn{constructor(e,t){this.schema=e,this.rules=t,this.tags=[],this.styles=[];let n=this.matchedStyles=[];t.forEach(o=>{if(function(i){return i.tag!=null}(o))this.tags.push(o);else if(function(i){return i.style!=null}(o)){let i=/[^=]*/.exec(o.style)[0];n.indexOf(i)<0&&n.push(i),this.styles.push(o)}}),this.normalizeLists=!this.tags.some(o=>{if(!/^(ul|ol)\b/.test(o.tag)||!o.node)return!1;let i=e.nodes[o.node];return i.contentMatch.matchType(i)})}parse(e,t={}){let n=new xi(this,t,!1);return n.addAll(e,oe.none,t.from,t.to),n.finish()}parseSlice(e,t={}){let n=new xi(this,t,!0);return n.addAll(e,oe.none,t.from,t.to),D.maxOpen(n.finish())}matchTag(e,t,n){for(let o=n?this.tags.indexOf(n)+1:0;o<this.tags.length;o++){let i=this.tags[o];if(fc(e,i.tag)&&(i.namespace===void 0||e.namespaceURI==i.namespace)&&(!i.context||t.matchesContext(i.context))){if(i.getAttrs){let s=i.getAttrs(e);if(s===!1)continue;i.attrs=s||void 0}return i}}}matchStyle(e,t,n,o){for(let i=o?this.styles.indexOf(o)+1:0;i<this.styles.length;i++){let s=this.styles[i],a=s.style;if(!(a.indexOf(e)!=0||s.context&&!n.matchesContext(s.context)||a.length>e.length&&(a.charCodeAt(e.length)!=61||a.slice(e.length+1)!=t))){if(s.getAttrs){let l=s.getAttrs(t);if(l===!1)continue;s.attrs=l||void 0}return s}}}static schemaRules(e){let t=[];function n(o){let i=o.priority==null?50:o.priority,s=0;for(;s<t.length;s++){let a=t[s];if((a.priority==null?50:a.priority)<i)break}t.splice(s,0,o)}for(let o in e.marks){let i=e.marks[o].spec.parseDOM;i&&i.forEach(s=>{n(s=ki(s)),s.mark||s.ignore||s.clearMark||(s.mark=o)})}for(let o in e.nodes){let i=e.nodes[o].spec.parseDOM;i&&i.forEach(s=>{n(s=ki(s)),s.node||s.ignore||s.mark||(s.node=o)})}return t}static fromSchema(e){return e.cached.domParser||(e.cached.domParser=new Sn(e,Sn.schemaRules(e)))}}const ya={address:!0,article:!0,aside:!0,blockquote:!0,canvas:!0,dd:!0,div:!0,dl:!0,fieldset:!0,figcaption:!0,figure:!0,footer:!0,form:!0,h1:!0,h2:!0,h3:!0,h4:!0,h5:!0,h6:!0,header:!0,hgroup:!0,hr:!0,li:!0,noscript:!0,ol:!0,output:!0,p:!0,pre:!0,section:!0,table:!0,tfoot:!0,ul:!0},uc={head:!0,noscript:!0,object:!0,script:!0,style:!0,title:!0},wi={ol:!0,ul:!0};function bi(r,e,t){return e!=null?(e?1:0)|(e==="full"?2:0):r&&r.whitespace=="pre"?3:-5&t}class qr{constructor(e,t,n,o,i,s){this.type=e,this.attrs=t,this.marks=n,this.solid=o,this.options=s,this.content=[],this.activeMarks=oe.none,this.match=i||(4&s?null:e.contentMatch)}findWrapping(e){if(!this.match){if(!this.type)return[];let t=this.type.contentMatch.fillBefore(S.from(e));if(!t){let n,o=this.type.contentMatch;return(n=o.findWrapping(e.type))?(this.match=o,n):null}this.match=this.type.contentMatch.matchFragment(t)}return this.match.findWrapping(e.type)}finish(e){if(!(1&this.options)){let n,o=this.content[this.content.length-1];if(o&&o.isText&&(n=/[ \t\r\n\u000c]+$/.exec(o.text))){let i=o;o.text.length==n[0].length?this.content.pop():this.content[this.content.length-1]=i.withText(i.text.slice(0,i.text.length-n[0].length))}}let t=S.from(this.content);return!e&&this.match&&(t=t.append(this.match.fillBefore(S.empty,!0))),this.type?this.type.create(this.attrs,t,this.marks):t}inlineContext(e){return this.type?this.type.inlineContent:this.content.length?this.content[0].isInline:e.parentNode&&!ya.hasOwnProperty(e.parentNode.nodeName.toLowerCase())}}class xi{constructor(e,t,n){this.parser=e,this.options=t,this.isOpen=n,this.open=0;let o,i=t.topNode,s=bi(null,t.preserveWhitespace,0)|(n?4:0);o=i?new qr(i.type,i.attrs,oe.none,!0,t.topMatch||i.type.contentMatch,s):new qr(n?null:e.schema.topNodeType,null,oe.none,!0,null,s),this.nodes=[o],this.find=t.findPositions,this.needsBlock=!1}get top(){return this.nodes[this.open]}addDOM(e,t){e.nodeType==3?this.addTextNode(e,t):e.nodeType==1&&this.addElement(e,t)}addTextNode(e,t){let n=e.nodeValue,o=this.top;if(2&o.options||o.inlineContext(e)||/[^ \t\r\n\u000c]/.test(n)){if(1&o.options)n=2&o.options?n.replace(/\r\n?/g,`
`):n.replace(/\r?\n|\r/g," ");else if(n=n.replace(/[ \t\r\n\u000c]+/g," "),/^[ \t\r\n\u000c]/.test(n)&&this.open==this.nodes.length-1){let i=o.content[o.content.length-1],s=e.previousSibling;(!i||s&&s.nodeName=="BR"||i.isText&&/[ \t\r\n\u000c]$/.test(i.text))&&(n=n.slice(1))}n&&this.insertNode(this.parser.schema.text(n),t),this.findInText(e)}else this.findInside(e)}addElement(e,t,n){let o,i=e.nodeName.toLowerCase();wi.hasOwnProperty(i)&&this.parser.normalizeLists&&function(a){for(let l=a.firstChild,c=null;l;l=l.nextSibling){let h=l.nodeType==1?l.nodeName.toLowerCase():null;h&&wi.hasOwnProperty(h)&&c?(c.appendChild(l),l=c):h=="li"?c=l:h&&(c=null)}}(e);let s=this.options.ruleFromNode&&this.options.ruleFromNode(e)||(o=this.parser.matchTag(e,this,n));if(s?s.ignore:uc.hasOwnProperty(i))this.findInside(e),this.ignoreFallback(e,t);else if(!s||s.skip||s.closeParent){s&&s.closeParent?this.open=Math.max(0,this.open-1):s&&s.skip.nodeType&&(e=s.skip);let a,l=this.top,c=this.needsBlock;if(ya.hasOwnProperty(i))l.content.length&&l.content[0].isInline&&this.open&&(this.open--,l=this.top),a=!0,l.type||(this.needsBlock=!0);else if(!e.firstChild)return void this.leafFallback(e,t);let h=s&&s.skip?t:this.readStyles(e,t);h&&this.addAll(e,h),a&&this.sync(l),this.needsBlock=c}else{let a=this.readStyles(e,t);a&&this.addElementByRule(e,s,a,s.consuming===!1?o:void 0)}}leafFallback(e,t){e.nodeName=="BR"&&this.top.type&&this.top.type.inlineContent&&this.addTextNode(e.ownerDocument.createTextNode(`
`),t)}ignoreFallback(e,t){e.nodeName!="BR"||this.top.type&&this.top.type.inlineContent||this.findPlace(this.parser.schema.text("-"),t)}readStyles(e,t){let n=e.style;if(n&&n.length)for(let o=0;o<this.parser.matchedStyles.length;o++){let i=this.parser.matchedStyles[o],s=n.getPropertyValue(i);if(s)for(let a;;){let l=this.parser.matchStyle(i,s,this,a);if(!l)break;if(l.ignore)return null;if(t=l.clearMark?t.filter(c=>!l.clearMark(c)):t.concat(this.parser.schema.marks[l.mark].create(l.attrs)),l.consuming!==!1)break;a=l}}return t}addElementByRule(e,t,n,o){let i,s;if(t.node)if(s=this.parser.schema.nodes[t.node],s.isLeaf)this.insertNode(s.create(t.attrs),n)||this.leafFallback(e,n);else{let l=this.enter(s,t.attrs||null,n,t.preserveWhitespace);l&&(i=!0,n=l)}else{let l=this.parser.schema.marks[t.mark];n=n.concat(l.create(t.attrs))}let a=this.top;if(s&&s.isLeaf)this.findInside(e);else if(o)this.addElement(e,n,o);else if(t.getContent)this.findInside(e),t.getContent(e,this.parser.schema).forEach(l=>this.insertNode(l,n));else{let l=e;typeof t.contentElement=="string"?l=e.querySelector(t.contentElement):typeof t.contentElement=="function"?l=t.contentElement(e):t.contentElement&&(l=t.contentElement),this.findAround(e,l,!0),this.addAll(l,n),this.findAround(e,l,!1)}i&&this.sync(a)&&this.open--}addAll(e,t,n,o){let i=n||0;for(let s=n?e.childNodes[n]:e.firstChild,a=o==null?null:e.childNodes[o];s!=a;s=s.nextSibling,++i)this.findAtPoint(e,i),this.addDOM(s,t);this.findAtPoint(e,i)}findPlace(e,t){let n,o;for(let i=this.open;i>=0;i--){let s=this.nodes[i],a=s.findWrapping(e);if(a&&(!n||n.length>a.length)&&(n=a,o=s,!a.length)||s.solid)break}if(!n)return null;this.sync(o);for(let i=0;i<n.length;i++)t=this.enterInner(n[i],null,t,!1);return t}insertNode(e,t){if(e.isInline&&this.needsBlock&&!this.top.type){let o=this.textblockFromContext();o&&(t=this.enterInner(o,null,t))}let n=this.findPlace(e,t);if(n){this.closeExtra();let o=this.top;o.match&&(o.match=o.match.matchType(e.type));let i=oe.none;for(let s of n.concat(e.marks))(o.type?o.type.allowsMarkType(s.type):Mi(s.type,e.type))&&(i=s.addToSet(i));return o.content.push(e.mark(i)),!0}return!1}enter(e,t,n,o){let i=this.findPlace(e.create(t),n);return i&&(i=this.enterInner(e,t,n,!0,o)),i}enterInner(e,t,n,o=!1,i){this.closeExtra();let s=this.top;s.match=s.match&&s.match.matchType(e);let a=bi(e,i,s.options);4&s.options&&s.content.length==0&&(a|=4);let l=oe.none;return n=n.filter(c=>!(s.type?s.type.allowsMarkType(c.type):Mi(c.type,e))||(l=c.addToSet(l),!1)),this.nodes.push(new qr(e,t,l,o,null,a)),this.open++,n}closeExtra(e=!1){let t=this.nodes.length-1;if(t>this.open){for(;t>this.open;t--)this.nodes[t-1].content.push(this.nodes[t].finish(e));this.nodes.length=this.open+1}}finish(){return this.open=0,this.closeExtra(this.isOpen),this.nodes[0].finish(this.isOpen||this.options.topOpen)}sync(e){for(let t=this.open;t>=0;t--)if(this.nodes[t]==e)return this.open=t,!0;return!1}get currentPos(){this.closeExtra();let e=0;for(let t=this.open;t>=0;t--){let n=this.nodes[t].content;for(let o=n.length-1;o>=0;o--)e+=n[o].nodeSize;t&&e++}return e}findAtPoint(e,t){if(this.find)for(let n=0;n<this.find.length;n++)this.find[n].node==e&&this.find[n].offset==t&&(this.find[n].pos=this.currentPos)}findInside(e){if(this.find)for(let t=0;t<this.find.length;t++)this.find[t].pos==null&&e.nodeType==1&&e.contains(this.find[t].node)&&(this.find[t].pos=this.currentPos)}findAround(e,t,n){if(e!=t&&this.find)for(let o=0;o<this.find.length;o++)this.find[o].pos==null&&e.nodeType==1&&e.contains(this.find[o].node)&&t.compareDocumentPosition(this.find[o].node)&(n?2:4)&&(this.find[o].pos=this.currentPos)}findInText(e){if(this.find)for(let t=0;t<this.find.length;t++)this.find[t].node==e&&(this.find[t].pos=this.currentPos-(e.nodeValue.length-this.find[t].offset))}matchesContext(e){if(e.indexOf("|")>-1)return e.split(/\s*\|\s*/).some(this.matchesContext,this);let t=e.split("/"),n=this.options.context,o=!(this.isOpen||n&&n.parent.type!=this.nodes[0].type),i=-(n?n.depth+1:0)+(o?0:1),s=(a,l)=>{for(;a>=0;a--){let c=t[a];if(c==""){if(a==t.length-1||a==0)continue;for(;l>=i;l--)if(s(a-1,l))return!0;return!1}{let h=l>0||l==0&&o?this.nodes[l].type:n&&l>=i?n.node(l-i).type:null;if(!h||h.name!=c&&!h.isInGroup(c))return!1;l--}}return!0};return s(t.length-1,this.open)}textblockFromContext(){let e=this.options.context;if(e)for(let t=e.depth;t>=0;t--){let n=e.node(t).contentMatchAt(e.indexAfter(t)).defaultType;if(n&&n.isTextblock&&n.defaultAttrs)return n}for(let t in this.parser.schema.nodes){let n=this.parser.schema.nodes[t];if(n.isTextblock&&n.defaultAttrs)return n}}}function fc(r,e){return(r.matches||r.msMatchesSelector||r.webkitMatchesSelector||r.mozMatchesSelector).call(r,e)}function ki(r){let e={};for(let t in r)e[t]=r[t];return e}function Mi(r,e){let t=e.schema.nodes;for(let n in t){let o=t[n];if(!o.allowsMarkType(r))continue;let i=[],s=a=>{i.push(a);for(let l=0;l<a.edgeCount;l++){let{type:c,next:h}=a.edge(l);if(c==e||i.indexOf(h)<0&&s(h))return!0}};if(s(o.contentMatch))return!0}}class cn{constructor(e,t){this.nodes=e,this.marks=t}serializeFragment(e,t={},n){n||(n=jr(t).createDocumentFragment());let o=n,i=[];return e.forEach(s=>{if(i.length||s.marks.length){let a=0,l=0;for(;a<i.length&&l<s.marks.length;){let c=s.marks[l];if(this.marks[c.type.name]){if(!c.eq(i[a][0])||c.type.spec.spanning===!1)break;a++,l++}else l++}for(;a<i.length;)o=i.pop()[1];for(;l<s.marks.length;){let c=s.marks[l++],h=this.serializeMark(c,s.isInline,t);h&&(i.push([c,o]),o.appendChild(h.dom),o=h.contentDOM||h.dom)}}o.appendChild(this.serializeNodeInner(s,t))}),n}serializeNodeInner(e,t){let{dom:n,contentDOM:o}=rr(jr(t),this.nodes[e.type.name](e),null,e.attrs);if(o){if(e.isLeaf)throw new RangeError("Content hole not allowed in a leaf node spec");this.serializeFragment(e.content,t,o)}return n}serializeNode(e,t={}){let n=this.serializeNodeInner(e,t);for(let o=e.marks.length-1;o>=0;o--){let i=this.serializeMark(e.marks[o],e.isInline,t);i&&((i.contentDOM||i.dom).appendChild(n),n=i.dom)}return n}serializeMark(e,t,n={}){let o=this.marks[e.type.name];return o&&rr(jr(n),o(e,t),null,e.attrs)}static renderSpec(e,t,n=null,o){return rr(e,t,n,o)}static fromSchema(e){return e.cached.domSerializer||(e.cached.domSerializer=new cn(this.nodesFromSchema(e),this.marksFromSchema(e)))}static nodesFromSchema(e){let t=Si(e.nodes);return t.text||(t.text=n=>n.text),t}static marksFromSchema(e){return Si(e.marks)}}function Si(r){let e={};for(let t in r){let n=r[t].spec.toDOM;n&&(e[t]=n)}return e}function jr(r){return r.document||window.document}const Oi=new WeakMap;function mc(r){let e=Oi.get(r);return e===void 0&&Oi.set(r,e=function(t){let n=null;function o(i){if(i&&typeof i=="object")if(Array.isArray(i))if(typeof i[0]=="string")n||(n=[]),n.push(i);else for(let s=0;s<i.length;s++)o(i[s]);else for(let s in i)o(i[s])}return o(t),n}(r)),e}function rr(r,e,t,n){if(typeof e=="string")return{dom:r.createTextNode(e)};if(e.nodeType!=null)return{dom:e};if(e.dom&&e.dom.nodeType!=null)return e;let o,i=e[0];if(typeof i!="string")throw new RangeError("Invalid array passed to renderSpec");if(n&&(o=mc(n))&&o.indexOf(e)>-1)throw new RangeError("Using an array from an attribute object as a DOM spec. This may be an attempted cross site scripting attack.");let s,a=i.indexOf(" ");a>0&&(t=i.slice(0,a),i=i.slice(a+1));let l=t?r.createElementNS(t,i):r.createElement(i),c=e[1],h=1;if(c&&typeof c=="object"&&c.nodeType==null&&!Array.isArray(c)){h=2;for(let d in c)if(c[d]!=null){let p=d.indexOf(" ");p>0?l.setAttributeNS(d.slice(0,p),d.slice(p+1),c[d]):l.setAttribute(d,c[d])}}for(let d=h;d<e.length;d++){let p=e[d];if(p===0){if(d<e.length-1||d>h)throw new RangeError("Content hole must be the only child of its parent node");return{dom:l,contentDOM:l}}{let{dom:f,contentDOM:u}=rr(r,p,t,n);if(l.appendChild(f),u){if(s)throw new RangeError("Multiple content holes");s=u}}}return{dom:l,contentDOM:s}}const Ca=Math.pow(2,16);function gc(r,e){return r+e*Ca}function Ei(r){return 65535&r}class Mo{constructor(e,t,n){this.pos=e,this.delInfo=t,this.recover=n}get deleted(){return(8&this.delInfo)>0}get deletedBefore(){return(5&this.delInfo)>0}get deletedAfter(){return(6&this.delInfo)>0}get deletedAcross(){return(4&this.delInfo)>0}}class Pe{constructor(e,t=!1){if(this.ranges=e,this.inverted=t,!e.length&&Pe.empty)return Pe.empty}recover(e){let t=0,n=Ei(e);if(!this.inverted)for(let o=0;o<n;o++)t+=this.ranges[3*o+2]-this.ranges[3*o+1];return this.ranges[3*n]+t+function(o){return(o-(65535&o))/Ca}(e)}mapResult(e,t=1){return this._map(e,t,!1)}map(e,t=1){return this._map(e,t,!0)}_map(e,t,n){let o=0,i=this.inverted?2:1,s=this.inverted?1:2;for(let a=0;a<this.ranges.length;a+=3){let l=this.ranges[a]-(this.inverted?o:0);if(l>e)break;let c=this.ranges[a+i],h=this.ranges[a+s],d=l+c;if(e<=d){let p=l+o+((c?e==l?-1:e==d?1:t:t)<0?0:h);if(n)return p;let f=e==(t<0?l:d)?null:gc(a/3,e-l),u=e==l?2:e==d?1:4;return(t<0?e!=l:e!=d)&&(u|=8),new Mo(p,u,f)}o+=h-c}return n?e+o:new Mo(e+o,0,null)}touches(e,t){let n=0,o=Ei(t),i=this.inverted?2:1,s=this.inverted?1:2;for(let a=0;a<this.ranges.length;a+=3){let l=this.ranges[a]-(this.inverted?n:0);if(l>e)break;let c=this.ranges[a+i];if(e<=l+c&&a==3*o)return!0;n+=this.ranges[a+s]-c}return!1}forEach(e){let t=this.inverted?2:1,n=this.inverted?1:2;for(let o=0,i=0;o<this.ranges.length;o+=3){let s=this.ranges[o],a=s-(this.inverted?i:0),l=s+(this.inverted?0:i),c=this.ranges[o+t],h=this.ranges[o+n];e(a,a+c,l,l+h),i+=h-c}}invert(){return new Pe(this.ranges,!this.inverted)}toString(){return(this.inverted?"-":"")+JSON.stringify(this.ranges)}static offset(e){return e==0?Pe.empty:new Pe(e<0?[0,-e,0]:[0,0,e])}}Pe.empty=new Pe([]);class Hn{constructor(e,t,n=0,o=e?e.length:0){this.mirror=t,this.from=n,this.to=o,this._maps=e||[],this.ownData=!(e||t)}get maps(){return this._maps}slice(e=0,t=this.maps.length){return new Hn(this._maps,this.mirror,e,t)}appendMap(e,t){this.ownData||(this._maps=this._maps.slice(),this.mirror=this.mirror&&this.mirror.slice(),this.ownData=!0),this.to=this._maps.push(e),t!=null&&this.setMirror(this._maps.length-1,t)}appendMapping(e){for(let t=0,n=this._maps.length;t<e._maps.length;t++){let o=e.getMirror(t);this.appendMap(e._maps[t],o!=null&&o<t?n+o:void 0)}}getMirror(e){if(this.mirror){for(let t=0;t<this.mirror.length;t++)if(this.mirror[t]==e)return this.mirror[t+(t%2?-1:1)]}}setMirror(e,t){this.mirror||(this.mirror=[]),this.mirror.push(e,t)}appendMappingInverted(e){for(let t=e.maps.length-1,n=this._maps.length+e._maps.length;t>=0;t--){let o=e.getMirror(t);this.appendMap(e._maps[t].invert(),o!=null&&o>t?n-o-1:void 0)}}invert(){let e=new Hn;return e.appendMappingInverted(this),e}map(e,t=1){if(this.mirror)return this._map(e,t,!0);for(let n=this.from;n<this.to;n++)e=this._maps[n].map(e,t);return e}mapResult(e,t=1){return this._map(e,t,!1)}_map(e,t,n){let o=0;for(let i=this.from;i<this.to;i++){let s=this._maps[i].mapResult(e,t);if(s.recover!=null){let a=this.getMirror(i);if(a!=null&&a>i&&a<this.to){i=a,e=this._maps[a].recover(s.recover);continue}}o|=s.delInfo,e=s.pos}return n?e:new Mo(e,o,null)}}const Kr=Object.create(null);class Me{getMap(){return Pe.empty}merge(e){return null}static fromJSON(e,t){if(!t||!t.stepType)throw new RangeError("Invalid input for Step.fromJSON");let n=Kr[t.stepType];if(!n)throw new RangeError(`No step type ${t.stepType} defined`);return n.fromJSON(e,t)}static jsonID(e,t){if(e in Kr)throw new RangeError("Duplicate use of step JSON ID "+e);return Kr[e]=t,t.prototype.jsonID=e,t}}class pe{constructor(e,t){this.doc=e,this.failed=t}static ok(e){return new pe(e,null)}static fail(e){return new pe(null,e)}static fromReplace(e,t,n,o){try{return pe.ok(e.replace(t,n,o))}catch(i){if(i instanceof gr)return pe.fail(i.message);throw i}}}function Uo(r,e,t){let n=[];for(let o=0;o<r.childCount;o++){let i=r.child(o);i.content.size&&(i=i.copy(Uo(i.content,e,i))),i.isInline&&(i=e(i,t,o)),n.push(i)}return S.fromArray(n)}class Dt extends Me{constructor(e,t,n){super(),this.from=e,this.to=t,this.mark=n}apply(e){let t=e.slice(this.from,this.to),n=e.resolve(this.from),o=n.node(n.sharedDepth(this.to)),i=new D(Uo(t.content,(s,a)=>s.isAtom&&a.type.allowsMarkType(this.mark.type)?s.mark(this.mark.addToSet(s.marks)):s,o),t.openStart,t.openEnd);return pe.fromReplace(e,this.from,this.to,i)}invert(){return new lt(this.from,this.to,this.mark)}map(e){let t=e.mapResult(this.from,1),n=e.mapResult(this.to,-1);return t.deleted&&n.deleted||t.pos>=n.pos?null:new Dt(t.pos,n.pos,this.mark)}merge(e){return e instanceof Dt&&e.mark.eq(this.mark)&&this.from<=e.to&&this.to>=e.from?new Dt(Math.min(this.from,e.from),Math.max(this.to,e.to),this.mark):null}toJSON(){return{stepType:"addMark",mark:this.mark.toJSON(),from:this.from,to:this.to}}static fromJSON(e,t){if(typeof t.from!="number"||typeof t.to!="number")throw new RangeError("Invalid input for AddMarkStep.fromJSON");return new Dt(t.from,t.to,e.markFromJSON(t.mark))}}Me.jsonID("addMark",Dt);class lt extends Me{constructor(e,t,n){super(),this.from=e,this.to=t,this.mark=n}apply(e){let t=e.slice(this.from,this.to),n=new D(Uo(t.content,o=>o.mark(this.mark.removeFromSet(o.marks)),e),t.openStart,t.openEnd);return pe.fromReplace(e,this.from,this.to,n)}invert(){return new Dt(this.from,this.to,this.mark)}map(e){let t=e.mapResult(this.from,1),n=e.mapResult(this.to,-1);return t.deleted&&n.deleted||t.pos>=n.pos?null:new lt(t.pos,n.pos,this.mark)}merge(e){return e instanceof lt&&e.mark.eq(this.mark)&&this.from<=e.to&&this.to>=e.from?new lt(Math.min(this.from,e.from),Math.max(this.to,e.to),this.mark):null}toJSON(){return{stepType:"removeMark",mark:this.mark.toJSON(),from:this.from,to:this.to}}static fromJSON(e,t){if(typeof t.from!="number"||typeof t.to!="number")throw new RangeError("Invalid input for RemoveMarkStep.fromJSON");return new lt(t.from,t.to,e.markFromJSON(t.mark))}}Me.jsonID("removeMark",lt);class It extends Me{constructor(e,t){super(),this.pos=e,this.mark=t}apply(e){let t=e.nodeAt(this.pos);if(!t)return pe.fail("No node at mark step's position");let n=t.type.create(t.attrs,null,this.mark.addToSet(t.marks));return pe.fromReplace(e,this.pos,this.pos+1,new D(S.from(n),0,t.isLeaf?0:1))}invert(e){let t=e.nodeAt(this.pos);if(t){let n=this.mark.addToSet(t.marks);if(n.length==t.marks.length){for(let o=0;o<t.marks.length;o++)if(!t.marks[o].isInSet(n))return new It(this.pos,t.marks[o]);return new It(this.pos,this.mark)}}return new sn(this.pos,this.mark)}map(e){let t=e.mapResult(this.pos,1);return t.deletedAfter?null:new It(t.pos,this.mark)}toJSON(){return{stepType:"addNodeMark",pos:this.pos,mark:this.mark.toJSON()}}static fromJSON(e,t){if(typeof t.pos!="number")throw new RangeError("Invalid input for AddNodeMarkStep.fromJSON");return new It(t.pos,e.markFromJSON(t.mark))}}Me.jsonID("addNodeMark",It);class sn extends Me{constructor(e,t){super(),this.pos=e,this.mark=t}apply(e){let t=e.nodeAt(this.pos);if(!t)return pe.fail("No node at mark step's position");let n=t.type.create(t.attrs,null,this.mark.removeFromSet(t.marks));return pe.fromReplace(e,this.pos,this.pos+1,new D(S.from(n),0,t.isLeaf?0:1))}invert(e){let t=e.nodeAt(this.pos);return t&&this.mark.isInSet(t.marks)?new It(this.pos,this.mark):this}map(e){let t=e.mapResult(this.pos,1);return t.deletedAfter?null:new sn(t.pos,this.mark)}toJSON(){return{stepType:"removeNodeMark",pos:this.pos,mark:this.mark.toJSON()}}static fromJSON(e,t){if(typeof t.pos!="number")throw new RangeError("Invalid input for RemoveNodeMarkStep.fromJSON");return new sn(t.pos,e.markFromJSON(t.mark))}}Me.jsonID("removeNodeMark",sn);class ge extends Me{constructor(e,t,n,o=!1){super(),this.from=e,this.to=t,this.slice=n,this.structure=o}apply(e){return this.structure&&So(e,this.from,this.to)?pe.fail("Structure replace would overwrite content"):pe.fromReplace(e,this.from,this.to,this.slice)}getMap(){return new Pe([this.from,this.to-this.from,this.slice.size])}invert(e){return new ge(this.from,this.from+this.slice.size,e.slice(this.from,this.to))}map(e){let t=e.mapResult(this.from,1),n=e.mapResult(this.to,-1);return t.deletedAcross&&n.deletedAcross?null:new ge(t.pos,Math.max(t.pos,n.pos),this.slice,this.structure)}merge(e){if(!(e instanceof ge)||e.structure||this.structure)return null;if(this.from+this.slice.size!=e.from||this.slice.openEnd||e.slice.openStart){if(e.to!=this.from||this.slice.openStart||e.slice.openEnd)return null;{let t=this.slice.size+e.slice.size==0?D.empty:new D(e.slice.content.append(this.slice.content),e.slice.openStart,this.slice.openEnd);return new ge(e.from,this.to,t,this.structure)}}{let t=this.slice.size+e.slice.size==0?D.empty:new D(this.slice.content.append(e.slice.content),this.slice.openStart,e.slice.openEnd);return new ge(this.from,this.to+(e.to-e.from),t,this.structure)}}toJSON(){let e={stepType:"replace",from:this.from,to:this.to};return this.slice.size&&(e.slice=this.slice.toJSON()),this.structure&&(e.structure=!0),e}static fromJSON(e,t){if(typeof t.from!="number"||typeof t.to!="number")throw new RangeError("Invalid input for ReplaceStep.fromJSON");return new ge(t.from,t.to,D.fromJSON(e,t.slice),!!t.structure)}}Me.jsonID("replace",ge);class ye extends Me{constructor(e,t,n,o,i,s,a=!1){super(),this.from=e,this.to=t,this.gapFrom=n,this.gapTo=o,this.slice=i,this.insert=s,this.structure=a}apply(e){if(this.structure&&(So(e,this.from,this.gapFrom)||So(e,this.gapTo,this.to)))return pe.fail("Structure gap-replace would overwrite content");let t=e.slice(this.gapFrom,this.gapTo);if(t.openStart||t.openEnd)return pe.fail("Gap is not a flat range");let n=this.slice.insertAt(this.insert,t.content);return n?pe.fromReplace(e,this.from,this.to,n):pe.fail("Content does not fit in gap")}getMap(){return new Pe([this.from,this.gapFrom-this.from,this.insert,this.gapTo,this.to-this.gapTo,this.slice.size-this.insert])}invert(e){let t=this.gapTo-this.gapFrom;return new ye(this.from,this.from+this.slice.size+t,this.from+this.insert,this.from+this.insert+t,e.slice(this.from,this.to).removeBetween(this.gapFrom-this.from,this.gapTo-this.from),this.gapFrom-this.from,this.structure)}map(e){let t=e.mapResult(this.from,1),n=e.mapResult(this.to,-1),o=this.from==this.gapFrom?t.pos:e.map(this.gapFrom,-1),i=this.to==this.gapTo?n.pos:e.map(this.gapTo,1);return t.deletedAcross&&n.deletedAcross||o<t.pos||i>n.pos?null:new ye(t.pos,n.pos,o,i,this.slice,this.insert,this.structure)}toJSON(){let e={stepType:"replaceAround",from:this.from,to:this.to,gapFrom:this.gapFrom,gapTo:this.gapTo,insert:this.insert};return this.slice.size&&(e.slice=this.slice.toJSON()),this.structure&&(e.structure=!0),e}static fromJSON(e,t){if(typeof t.from!="number"||typeof t.to!="number"||typeof t.gapFrom!="number"||typeof t.gapTo!="number"||typeof t.insert!="number")throw new RangeError("Invalid input for ReplaceAroundStep.fromJSON");return new ye(t.from,t.to,t.gapFrom,t.gapTo,D.fromJSON(e,t.slice),t.insert,!!t.structure)}}function So(r,e,t){let n=r.resolve(e),o=t-e,i=n.depth;for(;o>0&&i>0&&n.indexAfter(i)==n.node(i).childCount;)i--,o--;if(o>0){let s=n.node(i).maybeChild(n.indexAfter(i));for(;o>0;){if(!s||s.isLeaf)return!0;s=s.firstChild,o--}}return!1}function Jr(r,e,t,n=t.contentMatch,o=!0){let i=r.doc.nodeAt(e),s=[],a=e+1;for(let l=0;l<i.childCount;l++){let c=i.child(l),h=a+c.nodeSize,d=n.matchType(c.type);if(d){n=d;for(let p=0;p<c.marks.length;p++)t.allowsMarkType(c.marks[p].type)||r.step(new lt(a,h,c.marks[p]));if(o&&c.isText&&t.whitespace!="pre"){let p,f,u=/\r?\n|\r/g;for(;p=u.exec(c.text);)f||(f=new D(S.from(t.schema.text(" ",t.allowedMarks(c.marks))),0,0)),s.push(new ge(a+p.index,a+p.index+p[0].length,f))}}else s.push(new ge(a,h,D.empty));a=h}if(!n.validEnd){let l=n.fillBefore(S.empty,!0);r.replace(a,a,new D(l,0,0))}for(let l=s.length-1;l>=0;l--)r.step(s[l])}function yc(r,e,t){return(e==0||r.canReplace(e,r.childCount))&&(t==r.childCount||r.canReplace(0,t))}function On(r){let e=r.parent.content.cutByIndex(r.startIndex,r.endIndex);for(let t=r.depth;;--t){let n=r.$from.node(t),o=r.$from.index(t),i=r.$to.indexAfter(t);if(t<r.depth&&n.canReplace(o,i,e))return t;if(t==0||n.type.spec.isolating||!yc(n,o,i))break}return null}function va(r,e,t=null,n=r){let o=function(s,a){let{parent:l,startIndex:c,endIndex:h}=s,d=l.contentMatchAt(c).findWrapping(a);if(!d)return null;let p=d.length?d[0]:a;return l.canReplaceWith(c,h,p)?d:null}(r,e),i=o&&function(s,a){let{parent:l,startIndex:c,endIndex:h}=s,d=l.child(c),p=a.contentMatch.findWrapping(d.type);if(!p)return null;let f=(p.length?p[p.length-1]:a).contentMatch;for(let u=c;f&&u<h;u++)f=f.matchType(l.child(u).type);return f&&f.validEnd?p:null}(n,e);return i?o.map(Ti).concat({type:e,attrs:t}).concat(i.map(Ti)):null}function Ti(r){return{type:r,attrs:null}}function Ni(r,e,t,n){e.forEach((o,i)=>{if(o.isText){let s,a=/\r?\n|\r/g;for(;s=a.exec(o.text);){let l=r.mapping.slice(n).map(t+1+i+s.index);r.replaceWith(l,l+1,e.type.schema.linebreakReplacement.create())}}})}function Li(r,e,t,n){e.forEach((o,i)=>{if(o.type==o.type.schema.linebreakReplacement){let s=r.mapping.slice(n).map(t+1+i);r.replaceWith(s,s+1,e.type.schema.text(`
`))}})}function gt(r,e,t=1,n){let o=r.resolve(e),i=o.depth-t,s=n&&n[n.length-1]||o.parent;if(i<0||o.parent.type.spec.isolating||!o.parent.canReplace(o.index(),o.parent.childCount)||!s.type.validContent(o.parent.content.cutByIndex(o.index(),o.parent.childCount)))return!1;for(let c=o.depth-1,h=t-2;c>i;c--,h--){let d=o.node(c),p=o.index(c);if(d.type.spec.isolating)return!1;let f=d.content.cutByIndex(p,d.childCount),u=n&&n[h+1];u&&(f=f.replaceChild(0,u.type.create(u.attrs)));let m=n&&n[h]||d;if(!d.canReplace(p+1,d.childCount)||!m.type.validContent(f))return!1}let a=o.indexAfter(i),l=n&&n[0];return o.node(i).canReplaceWith(a,a,l?l.type:o.node(i+1).type)}function an(r,e){let t=r.resolve(e),n=t.index();return wa(t.nodeBefore,t.nodeAfter)&&t.parent.canReplace(n,n+1)}function wa(r,e){return!(!r||!e||r.isLeaf||!function(t,n){n.content.size||t.type.compatibleContent(n.type);let o=t.contentMatchAt(t.childCount),{linebreakReplacement:i}=t.type.schema;for(let s=0;s<n.childCount;s++){let a=n.child(s),l=a.type==i?t.type.schema.nodes.text:a.type;if(o=o.matchType(l),!o||!t.type.allowsMarks(a.marks))return!1}return o.validEnd}(r,e))}function Xn(r,e,t=-1){let n=r.resolve(e);for(let o=n.depth;;o--){let i,s,a=n.index(o);if(o==n.depth?(i=n.nodeBefore,s=n.nodeAfter):t>0?(i=n.node(o+1),a++,s=n.node(o).maybeChild(a)):(i=n.node(o).maybeChild(a-1),s=n.node(o+1)),i&&!i.isTextblock&&wa(i,s)&&n.node(o).canReplace(a,a+1))return e;if(o==0)break;e=t<0?n.before(o):n.after(o)}}function Ar(r,e,t=e,n=D.empty){if(e==t&&!n.size)return null;let o=r.resolve(e),i=r.resolve(t);return ba(o,i,n)?new ge(e,t,n):new Cc(o,i,n).fit()}function ba(r,e,t){return!t.openStart&&!t.openEnd&&r.start()==e.start()&&r.parent.canReplace(r.index(),e.index(),t.content)}Me.jsonID("replaceAround",ye);class Cc{constructor(e,t,n){this.$from=e,this.$to=t,this.unplaced=n,this.frontier=[],this.placed=S.empty;for(let o=0;o<=e.depth;o++){let i=e.node(o);this.frontier.push({type:i.type,match:i.contentMatchAt(e.indexAfter(o))})}for(let o=e.depth;o>0;o--)this.placed=S.from(e.node(o).copy(this.placed))}get depth(){return this.frontier.length-1}fit(){for(;this.unplaced.size;){let c=this.findFittable();c?this.placeNodes(c):this.openMore()||this.dropNode()}let e=this.mustMoveInline(),t=this.placed.size-this.depth-this.$from.depth,n=this.$from,o=this.close(e<0?this.$to:n.doc.resolve(e));if(!o)return null;let i=this.placed,s=n.depth,a=o.depth;for(;s&&a&&i.childCount==1;)i=i.firstChild.content,s--,a--;let l=new D(i,s,a);return e>-1?new ye(n.pos,e,this.$to.pos,this.$to.end(),l,t):l.size||n.pos!=this.$to.pos?new ge(n.pos,o.pos,l):null}findFittable(){let e=this.unplaced.openStart;for(let t=this.unplaced.content,n=0,o=this.unplaced.openEnd;n<e;n++){let i=t.firstChild;if(t.childCount>1&&(o=0),i.type.spec.isolating&&o<=n){e=n;break}t=i.content}for(let t=1;t<=2;t++)for(let n=t==1?e:this.unplaced.openStart;n>=0;n--){let o,i=null;n?(i=Wr(this.unplaced.content,n-1).firstChild,o=i.content):o=this.unplaced.content;let s=o.firstChild;for(let a=this.depth;a>=0;a--){let l,{type:c,match:h}=this.frontier[a],d=null;if(t==1&&(s?h.matchType(s.type)||(d=h.fillBefore(S.from(s),!1)):i&&c.compatibleContent(i.type)))return{sliceDepth:n,frontierDepth:a,parent:i,inject:d};if(t==2&&s&&(l=h.findWrapping(s.type)))return{sliceDepth:n,frontierDepth:a,parent:i,wrap:l};if(i&&h.matchType(i.type))break}}}openMore(){let{content:e,openStart:t,openEnd:n}=this.unplaced,o=Wr(e,t);return!(!o.childCount||o.firstChild.isLeaf)&&(this.unplaced=new D(e,t+1,Math.max(n,o.size+t>=e.size-n?t+1:0)),!0)}dropNode(){let{content:e,openStart:t,openEnd:n}=this.unplaced,o=Wr(e,t);if(o.childCount<=1&&t>0){let i=e.size-t<=t+o.size;this.unplaced=new D(Dn(e,t-1,1),t-1,i?t-1:n)}else this.unplaced=new D(Dn(e,t,1),t,n)}placeNodes({sliceDepth:e,frontierDepth:t,parent:n,inject:o,wrap:i}){for(;this.depth>t;)this.closeFrontierNode();if(i)for(let m=0;m<i.length;m++)this.openFrontierNode(i[m]);let s=this.unplaced,a=n?n.content:s.content,l=s.openStart-e,c=0,h=[],{match:d,type:p}=this.frontier[t];if(o){for(let m=0;m<o.childCount;m++)h.push(o.child(m));d=d.matchFragment(o)}let f=a.size+e-(s.content.size-s.openEnd);for(;c<a.childCount;){let m=a.child(c),g=d.matchType(m.type);if(!g)break;c++,(c>1||l==0||m.content.size)&&(d=g,h.push(xa(m.mark(p.allowedMarks(m.marks)),c==1?l:0,c==a.childCount?f:-1)))}let u=c==a.childCount;u||(f=-1),this.placed=In(this.placed,t,S.from(h)),this.frontier[t].match=d,u&&f<0&&n&&n.type==this.frontier[this.depth].type&&this.frontier.length>1&&this.closeFrontierNode();for(let m=0,g=a;m<f;m++){let y=g.lastChild;this.frontier.push({type:y.type,match:y.contentMatchAt(y.childCount)}),g=y.content}this.unplaced=u?e==0?D.empty:new D(Dn(s.content,e-1,1),e-1,f<0?s.openEnd:e-1):new D(Dn(s.content,e,c),s.openStart,s.openEnd)}mustMoveInline(){if(!this.$to.parent.isTextblock)return-1;let e,t=this.frontier[this.depth];if(!t.type.isTextblock||!Ur(this.$to,this.$to.depth,t.type,t.match,!1)||this.$to.depth==this.depth&&(e=this.findCloseLevel(this.$to))&&e.depth==this.depth)return-1;let{depth:n}=this.$to,o=this.$to.after(n);for(;n>1&&o==this.$to.end(--n);)++o;return o}findCloseLevel(e){e:for(let t=Math.min(this.depth,e.depth);t>=0;t--){let{match:n,type:o}=this.frontier[t],i=t<e.depth&&e.end(t+1)==e.pos+(e.depth-(t+1)),s=Ur(e,t,o,n,i);if(s){for(let a=t-1;a>=0;a--){let{match:l,type:c}=this.frontier[a],h=Ur(e,a,c,l,!0);if(!h||h.childCount)continue e}return{depth:t,fit:s,move:i?e.doc.resolve(e.after(t+1)):e}}}}close(e){let t=this.findCloseLevel(e);if(!t)return null;for(;this.depth>t.depth;)this.closeFrontierNode();t.fit.childCount&&(this.placed=In(this.placed,t.depth,t.fit)),e=t.move;for(let n=t.depth+1;n<=e.depth;n++){let o=e.node(n),i=o.type.contentMatch.fillBefore(o.content,!0,e.index(n));this.openFrontierNode(o.type,o.attrs,i)}return e}openFrontierNode(e,t=null,n){let o=this.frontier[this.depth];o.match=o.match.matchType(e),this.placed=In(this.placed,this.depth,S.from(e.create(t,n))),this.frontier.push({type:e,match:e.contentMatch})}closeFrontierNode(){let e=this.frontier.pop().match.fillBefore(S.empty,!0);e.childCount&&(this.placed=In(this.placed,this.frontier.length,e))}}function Dn(r,e,t){return e==0?r.cutByIndex(t,r.childCount):r.replaceChild(0,r.firstChild.copy(Dn(r.firstChild.content,e-1,t)))}function In(r,e,t){return e==0?r.append(t):r.replaceChild(r.childCount-1,r.lastChild.copy(In(r.lastChild.content,e-1,t)))}function Wr(r,e){for(let t=0;t<e;t++)r=r.firstChild.content;return r}function xa(r,e,t){if(e<=0)return r;let n=r.content;return e>1&&(n=n.replaceChild(0,xa(n.firstChild,e-1,n.childCount==1?t-1:0))),e>0&&(n=r.type.contentMatch.fillBefore(n).append(n),t<=0&&(n=n.append(r.type.contentMatch.matchFragment(n).fillBefore(S.empty,!0)))),r.copy(n)}function Ur(r,e,t,n,o){let i=r.node(e),s=o?r.indexAfter(e):r.index(e);if(s==i.childCount&&!t.compatibleContent(i.type))return null;let a=n.fillBefore(i.content,!0,s);return a&&!function(l,c,h){for(let d=h;d<c.childCount;d++)if(!l.allowsMarks(c.child(d).marks))return!0;return!1}(t,i.content,s)?a:null}function ka(r,e,t,n,o){if(e<t){let i=r.firstChild;r=r.replaceChild(0,i.copy(ka(i.content,e+1,t,n,i)))}if(e>n){let i=o.contentMatchAt(0),s=i.fillBefore(r).append(r);r=s.append(i.matchFragment(s).fillBefore(S.empty,!0))}return r}function Ai(r,e){let t=[];for(let n=Math.min(r.depth,e.depth);n>=0;n--){let o=r.start(n);if(o<r.pos-(r.depth-n)||e.end(n)>e.pos+(e.depth-n)||r.node(n).type.spec.isolating||e.node(n).type.spec.isolating)break;(o==e.start(n)||n==r.depth&&n==e.depth&&r.parent.inlineContent&&e.parent.inlineContent&&n&&e.start(n-1)==o-1)&&t.push(n)}return t}class xn extends Me{constructor(e,t,n){super(),this.pos=e,this.attr=t,this.value=n}apply(e){let t=e.nodeAt(this.pos);if(!t)return pe.fail("No node at attribute step's position");let n=Object.create(null);for(let i in t.attrs)n[i]=t.attrs[i];n[this.attr]=this.value;let o=t.type.create(n,null,t.marks);return pe.fromReplace(e,this.pos,this.pos+1,new D(S.from(o),0,t.isLeaf?0:1))}getMap(){return Pe.empty}invert(e){return new xn(this.pos,this.attr,e.nodeAt(this.pos).attrs[this.attr])}map(e){let t=e.mapResult(this.pos,1);return t.deletedAfter?null:new xn(t.pos,this.attr,this.value)}toJSON(){return{stepType:"attr",pos:this.pos,attr:this.attr,value:this.value}}static fromJSON(e,t){if(typeof t.pos!="number"||typeof t.attr!="string")throw new RangeError("Invalid input for AttrStep.fromJSON");return new xn(t.pos,t.attr,t.value)}}Me.jsonID("attr",xn);class qn extends Me{constructor(e,t){super(),this.attr=e,this.value=t}apply(e){let t=Object.create(null);for(let o in e.attrs)t[o]=e.attrs[o];t[this.attr]=this.value;let n=e.type.create(t,e.content,e.marks);return pe.ok(n)}getMap(){return Pe.empty}invert(e){return new qn(this.attr,e.attrs[this.attr])}map(e){return this}toJSON(){return{stepType:"docAttr",attr:this.attr,value:this.value}}static fromJSON(e,t){if(typeof t.attr!="string")throw new RangeError("Invalid input for DocAttrStep.fromJSON");return new qn(t.attr,t.value)}}Me.jsonID("docAttr",qn);let _n=class extends Error{};_n=function r(e){let t=Error.call(this,e);return t.__proto__=r.prototype,t},(_n.prototype=Object.create(Error.prototype)).constructor=_n,_n.prototype.name="TransformError";class vc{constructor(e){this.doc=e,this.steps=[],this.docs=[],this.mapping=new Hn}get before(){return this.docs.length?this.docs[0]:this.doc}step(e){let t=this.maybeStep(e);if(t.failed)throw new _n(t.failed);return this}maybeStep(e){let t=e.apply(this.doc);return t.failed||this.addStep(e,t.doc),t}get docChanged(){return this.steps.length>0}addStep(e,t){this.docs.push(this.doc),this.steps.push(e),this.mapping.appendMap(e.getMap()),this.doc=t}replace(e,t=e,n=D.empty){let o=Ar(this.doc,e,t,n);return o&&this.step(o),this}replaceWith(e,t,n){return this.replace(e,t,new D(S.from(n),0,0))}delete(e,t){return this.replace(e,t,D.empty)}insert(e,t){return this.replaceWith(e,e,t)}replaceRange(e,t,n){return function(o,i,s,a){if(!a.size)return o.deleteRange(i,s);let l=o.doc.resolve(i),c=o.doc.resolve(s);if(ba(l,c,a))return o.step(new ge(i,s,a));let h=Ai(l,o.doc.resolve(s));h[h.length-1]==0&&h.pop();let d=-(l.depth+1);h.unshift(d);for(let y=l.depth,C=l.pos-1;y>0;y--,C--){let w=l.node(y).type.spec;if(w.defining||w.definingAsContext||w.isolating)break;h.indexOf(y)>-1?d=y:l.before(y)==C&&h.splice(1,0,-y)}let p=h.indexOf(d),f=[],u=a.openStart;for(let y=a.content,C=0;;C++){let w=y.firstChild;if(f.push(w),C==a.openStart)break;y=w.content}for(let y=u-1;y>=0;y--){let C=f[y],w=(m=C.type).spec.defining||m.spec.definingForContent;if(w&&!C.sameMarkup(l.node(Math.abs(d)-1)))u=y;else if(w||!C.type.isTextblock)break}var m;for(let y=a.openStart;y>=0;y--){let C=(y+u+1)%(a.openStart+1),w=f[C];if(w)for(let v=0;v<h.length;v++){let x=h[(v+p)%h.length],O=!0;x<0&&(O=!1,x=-x);let E=l.node(x-1),M=l.index(x-1);if(E.canReplaceWith(M,M,w.type,w.marks))return o.replace(l.before(x),O?c.after(x):s,new D(ka(a.content,0,a.openStart,C),C,a.openEnd))}}let g=o.steps.length;for(let y=h.length-1;y>=0&&(o.replace(i,s,a),!(o.steps.length>g));y--){let C=h[y];C<0||(i=l.before(C),s=c.after(C))}}(this,e,t,n),this}replaceRangeWith(e,t,n){return function(o,i,s,a){if(!a.isInline&&i==s&&o.doc.resolve(i).parent.content.size){let l=function(c,h,d){let p=c.resolve(h);if(p.parent.canReplaceWith(p.index(),p.index(),d))return h;if(p.parentOffset==0)for(let f=p.depth-1;f>=0;f--){let u=p.index(f);if(p.node(f).canReplaceWith(u,u,d))return p.before(f+1);if(u>0)return null}if(p.parentOffset==p.parent.content.size)for(let f=p.depth-1;f>=0;f--){let u=p.indexAfter(f);if(p.node(f).canReplaceWith(u,u,d))return p.after(f+1);if(u<p.node(f).childCount)return null}return null}(o.doc,i,a.type);l!=null&&(i=s=l)}o.replaceRange(i,s,new D(S.from(a),0,0))}(this,e,t,n),this}deleteRange(e,t){return function(n,o,i){let s=n.doc.resolve(o),a=n.doc.resolve(i),l=Ai(s,a);for(let c=0;c<l.length;c++){let h=l[c],d=c==l.length-1;if(d&&h==0||s.node(h).type.contentMatch.validEnd)return n.delete(s.start(h),a.end(h));if(h>0&&(d||s.node(h-1).canReplace(s.index(h-1),a.indexAfter(h-1))))return n.delete(s.before(h),a.after(h))}for(let c=1;c<=s.depth&&c<=a.depth;c++)if(o-s.start(c)==s.depth-c&&i>s.end(c)&&a.end(c)-i!=a.depth-c&&s.start(c-1)==a.start(c-1)&&s.node(c-1).canReplace(s.index(c-1),a.index(c-1)))return n.delete(s.before(c),i);n.delete(o,i)}(this,e,t),this}lift(e,t){return function(n,o,i){let{$from:s,$to:a,depth:l}=o,c=s.before(l+1),h=a.after(l+1),d=c,p=h,f=S.empty,u=0;for(let y=l,C=!1;y>i;y--)C||s.index(y)>0?(C=!0,f=S.from(s.node(y).copy(f)),u++):d--;let m=S.empty,g=0;for(let y=l,C=!1;y>i;y--)C||a.after(y+1)<a.end(y)?(C=!0,m=S.from(a.node(y).copy(m)),g++):p++;n.step(new ye(d,p,c,h,new D(f.append(m),u,g),f.size-u,!0))}(this,e,t),this}join(e,t=1){return function(n,o,i){let s=null,{linebreakReplacement:a}=n.doc.type.schema,l=n.doc.resolve(o-i),c=l.node().type;if(a&&c.inlineContent){let f=c.whitespace=="pre",u=!!c.contentMatch.matchType(a);f&&!u?s=!1:!f&&u&&(s=!0)}let h=n.steps.length;if(s===!1){let f=n.doc.resolve(o+i);Li(n,f.node(),f.before(),h)}c.inlineContent&&Jr(n,o+i-1,c,l.node().contentMatchAt(l.index()),s==null);let d=n.mapping.slice(h),p=d.map(o-i);if(n.step(new ge(p,d.map(o+i,-1),D.empty,!0)),s===!0){let f=n.doc.resolve(p);Ni(n,f.node(),f.before(),n.steps.length)}}(this,e,t),this}wrap(e,t){return function(n,o,i){let s=S.empty;for(let c=i.length-1;c>=0;c--){if(s.size){let h=i[c].type.contentMatch.matchFragment(s);if(!h||!h.validEnd)throw new RangeError("Wrapper type given to Transform.wrap does not form valid content of its parent wrapper")}s=S.from(i[c].type.create(i[c].attrs,s))}let a=o.start,l=o.end;n.step(new ye(a,l,a,l,new D(s,0,0),i.length,!0))}(this,e,t),this}setBlockType(e,t=e,n,o=null){return function(i,s,a,l,c){if(!l.isTextblock)throw new RangeError("Type given to setBlockType should be a textblock");let h=i.steps.length;i.doc.nodesBetween(s,a,(d,p)=>{let f=typeof c=="function"?c(d):c;if(d.isTextblock&&!d.hasMarkup(l,f)&&function(u,m,g){let y=u.resolve(m),C=y.index();return y.parent.canReplaceWith(C,C+1,g)}(i.doc,i.mapping.slice(h).map(p),l)){let u=null;if(l.schema.linebreakReplacement){let C=l.whitespace=="pre",w=!!l.contentMatch.matchType(l.schema.linebreakReplacement);C&&!w?u=!1:!C&&w&&(u=!0)}u===!1&&Li(i,d,p,h),Jr(i,i.mapping.slice(h).map(p,1),l,void 0,u===null);let m=i.mapping.slice(h),g=m.map(p,1),y=m.map(p+d.nodeSize,1);return i.step(new ye(g,y,g+1,y-1,new D(S.from(l.create(f,null,d.marks)),0,0),1,!0)),u===!0&&Ni(i,d,p,h),!1}})}(this,e,t,n,o),this}setNodeMarkup(e,t,n=null,o){return function(i,s,a,l,c){let h=i.doc.nodeAt(s);if(!h)throw new RangeError("No node at given position");a||(a=h.type);let d=a.create(l,null,c||h.marks);if(h.isLeaf)return i.replaceWith(s,s+h.nodeSize,d);if(!a.validContent(h.content))throw new RangeError("Invalid content for node type "+a.name);i.step(new ye(s,s+h.nodeSize,s+1,s+h.nodeSize-1,new D(S.from(d),0,0),1,!0))}(this,e,t,n,o),this}setNodeAttribute(e,t,n){return this.step(new xn(e,t,n)),this}setDocAttribute(e,t){return this.step(new qn(e,t)),this}addNodeMark(e,t){return this.step(new It(e,t)),this}removeNodeMark(e,t){let n=this.doc.nodeAt(e);if(!n)throw new RangeError("No node at position "+e);if(t instanceof oe)t.isInSet(n.marks)&&this.step(new sn(e,t));else{let o,i=n.marks,s=[];for(;o=t.isInSet(i);)s.push(new sn(e,o)),i=o.removeFromSet(i);for(let a=s.length-1;a>=0;a--)this.step(s[a])}return this}split(e,t=1,n){return function(o,i,s=1,a){let l=o.doc.resolve(i),c=S.empty,h=S.empty;for(let d=l.depth,p=l.depth-s,f=s-1;d>p;d--,f--){c=S.from(l.node(d).copy(c));let u=a&&a[f];h=S.from(u?u.type.create(u.attrs,h):l.node(d).copy(h))}o.step(new ge(i,i,new D(c.append(h),s,s),!0))}(this,e,t,n),this}addMark(e,t,n){return function(o,i,s,a){let l,c,h=[],d=[];o.doc.nodesBetween(i,s,(p,f,u)=>{if(!p.isInline)return;let m=p.marks;if(!a.isInSet(m)&&u.type.allowsMarkType(a.type)){let g=Math.max(f,i),y=Math.min(f+p.nodeSize,s),C=a.addToSet(m);for(let w=0;w<m.length;w++)m[w].isInSet(C)||(l&&l.to==g&&l.mark.eq(m[w])?l.to=y:h.push(l=new lt(g,y,m[w])));c&&c.to==g?c.to=y:d.push(c=new Dt(g,y,a))}}),h.forEach(p=>o.step(p)),d.forEach(p=>o.step(p))}(this,e,t,n),this}removeMark(e,t,n){return function(o,i,s,a){let l=[],c=0;o.doc.nodesBetween(i,s,(h,d)=>{if(!h.isInline)return;c++;let p=null;if(a instanceof Lr){let f,u=h.marks;for(;f=a.isInSet(u);)(p||(p=[])).push(f),u=f.removeFromSet(u)}else a?a.isInSet(h.marks)&&(p=[a]):p=h.marks;if(p&&p.length){let f=Math.min(d+h.nodeSize,s);for(let u=0;u<p.length;u++){let m,g=p[u];for(let y=0;y<l.length;y++){let C=l[y];C.step==c-1&&g.eq(l[y].style)&&(m=C)}m?(m.to=f,m.step=c):l.push({style:g,from:Math.max(d,i),to:f,step:c})}}}),l.forEach(h=>o.step(new lt(h.from,h.to,h.style)))}(this,e,t,n),this}clearIncompatible(e,t,n){return Jr(this,e,t,n),this}}const Gr=Object.create(null);class J{constructor(e,t,n){this.$anchor=e,this.$head=t,this.ranges=n||[new wc(e.min(t),e.max(t))]}get anchor(){return this.$anchor.pos}get head(){return this.$head.pos}get from(){return this.$from.pos}get to(){return this.$to.pos}get $from(){return this.ranges[0].$from}get $to(){return this.ranges[0].$to}get empty(){let e=this.ranges;for(let t=0;t<e.length;t++)if(e[t].$from.pos!=e[t].$to.pos)return!1;return!0}content(){return this.$from.doc.slice(this.from,this.to,!0)}replace(e,t=D.empty){let n=t.content.lastChild,o=null;for(let a=0;a<t.openEnd;a++)o=n,n=n.lastChild;let i=e.steps.length,s=this.ranges;for(let a=0;a<s.length;a++){let{$from:l,$to:c}=s[a],h=e.mapping.slice(i);e.replaceRange(h.map(l.pos),h.map(c.pos),a?D.empty:t),a==0&&_i(e,i,(n?n.isInline:o&&o.isTextblock)?-1:1)}}replaceWith(e,t){let n=e.steps.length,o=this.ranges;for(let i=0;i<o.length;i++){let{$from:s,$to:a}=o[i],l=e.mapping.slice(n),c=l.map(s.pos),h=l.map(a.pos);i?e.deleteRange(c,h):(e.replaceRangeWith(c,h,t),_i(e,n,t.isInline?-1:1))}}static findFrom(e,t,n=!1){let o=e.parent.inlineContent?new K(e):mn(e.node(0),e.parent,e.pos,e.index(),t,n);if(o)return o;for(let i=e.depth-1;i>=0;i--){let s=t<0?mn(e.node(0),e.node(i),e.before(i+1),e.index(i),t,n):mn(e.node(0),e.node(i),e.after(i+1),e.index(i)+1,t,n);if(s)return s}return null}static near(e,t=1){return this.findFrom(e,t)||this.findFrom(e,-t)||new We(e.node(0))}static atStart(e){return mn(e,e,0,0,1)||new We(e)}static atEnd(e){return mn(e,e,e.content.size,e.childCount,-1)||new We(e)}static fromJSON(e,t){if(!t||!t.type)throw new RangeError("Invalid input for Selection.fromJSON");let n=Gr[t.type];if(!n)throw new RangeError(`No selection type ${t.type} defined`);return n.fromJSON(e,t)}static jsonID(e,t){if(e in Gr)throw new RangeError("Duplicate use of selection JSON ID "+e);return Gr[e]=t,t.prototype.jsonID=e,t}getBookmark(){return K.between(this.$anchor,this.$head).getBookmark()}}J.prototype.visible=!0;class wc{constructor(e,t){this.$from=e,this.$to=t}}let Di=!1;function Ii(r){Di||r.parent.inlineContent||(Di=!0,console.warn("TextSelection endpoint not pointing into a node with inline content ("+r.parent.type.name+")"))}class K extends J{constructor(e,t=e){Ii(e),Ii(t),super(e,t)}get $cursor(){return this.$anchor.pos==this.$head.pos?this.$head:null}map(e,t){let n=e.resolve(t.map(this.head));if(!n.parent.inlineContent)return J.near(n);let o=e.resolve(t.map(this.anchor));return new K(o.parent.inlineContent?o:n,n)}replace(e,t=D.empty){if(super.replace(e,t),t==D.empty){let n=this.$from.marksAcross(this.$to);n&&e.ensureMarks(n)}}eq(e){return e instanceof K&&e.anchor==this.anchor&&e.head==this.head}getBookmark(){return new Dr(this.anchor,this.head)}toJSON(){return{type:"text",anchor:this.anchor,head:this.head}}static fromJSON(e,t){if(typeof t.anchor!="number"||typeof t.head!="number")throw new RangeError("Invalid input for TextSelection.fromJSON");return new K(e.resolve(t.anchor),e.resolve(t.head))}static create(e,t,n=t){let o=e.resolve(t);return new this(o,n==t?o:e.resolve(n))}static between(e,t,n){let o=e.pos-t.pos;if(n&&!o||(n=o>=0?1:-1),!t.parent.inlineContent){let i=J.findFrom(t,n,!0)||J.findFrom(t,-n,!0);if(!i)return J.near(t,n);t=i.$head}return e.parent.inlineContent||(o==0||(e=(J.findFrom(e,-n,!0)||J.findFrom(e,n,!0)).$anchor).pos<t.pos!=o<0)&&(e=t),new K(e,t)}}J.jsonID("text",K);class Dr{constructor(e,t){this.anchor=e,this.head=t}map(e){return new Dr(e.map(this.anchor),e.map(this.head))}resolve(e){return K.between(e.resolve(this.anchor),e.resolve(this.head))}}class V extends J{constructor(e){let t=e.nodeAfter,n=e.node(0).resolve(e.pos+t.nodeSize);super(e,n),this.node=t}map(e,t){let{deleted:n,pos:o}=t.mapResult(this.anchor),i=e.resolve(o);return n?J.near(i):new V(i)}content(){return new D(S.from(this.node),0,0)}eq(e){return e instanceof V&&e.anchor==this.anchor}toJSON(){return{type:"node",anchor:this.anchor}}getBookmark(){return new Go(this.anchor)}static fromJSON(e,t){if(typeof t.anchor!="number")throw new RangeError("Invalid input for NodeSelection.fromJSON");return new V(e.resolve(t.anchor))}static create(e,t){return new V(e.resolve(t))}static isSelectable(e){return!e.isText&&e.type.spec.selectable!==!1}}V.prototype.visible=!1,J.jsonID("node",V);class Go{constructor(e){this.anchor=e}map(e){let{deleted:t,pos:n}=e.mapResult(this.anchor);return t?new Dr(n,n):new Go(n)}resolve(e){let t=e.resolve(this.anchor),n=t.nodeAfter;return n&&V.isSelectable(n)?new V(t):J.near(t)}}class We extends J{constructor(e){super(e.resolve(0),e.resolve(e.content.size))}replace(e,t=D.empty){if(t==D.empty){e.delete(0,e.doc.content.size);let n=J.atStart(e.doc);n.eq(e.selection)||e.setSelection(n)}else super.replace(e,t)}toJSON(){return{type:"all"}}static fromJSON(e){return new We(e)}map(e){return new We(e)}eq(e){return e instanceof We}getBookmark(){return bc}}J.jsonID("all",We);const bc={map(){return this},resolve:r=>new We(r)};function mn(r,e,t,n,o,i=!1){if(e.inlineContent)return K.create(r,t);for(let s=n-(o>0?0:1);o>0?s<e.childCount:s>=0;s+=o){let a=e.child(s);if(a.isAtom){if(!i&&V.isSelectable(a))return V.create(r,t-(o<0?a.nodeSize:0))}else{let l=mn(r,a,t+o,o<0?a.childCount:0,o,i);if(l)return l}t+=a.nodeSize*o}return null}function _i(r,e,t){let n=r.steps.length-1;if(n<e)return;let o,i=r.steps[n];(i instanceof ge||i instanceof ye)&&(r.mapping.maps[n].forEach((s,a,l,c)=>{o==null&&(o=c)}),r.setSelection(J.near(r.doc.resolve(o),t)))}class xc extends vc{constructor(e){super(e.doc),this.curSelectionFor=0,this.updated=0,this.meta=Object.create(null),this.time=Date.now(),this.curSelection=e.selection,this.storedMarks=e.storedMarks}get selection(){return this.curSelectionFor<this.steps.length&&(this.curSelection=this.curSelection.map(this.doc,this.mapping.slice(this.curSelectionFor)),this.curSelectionFor=this.steps.length),this.curSelection}setSelection(e){if(e.$from.doc!=this.doc)throw new RangeError("Selection passed to setSelection must point at the current document");return this.curSelection=e,this.curSelectionFor=this.steps.length,this.updated=-3&this.updated|1,this.storedMarks=null,this}get selectionSet(){return(1&this.updated)>0}setStoredMarks(e){return this.storedMarks=e,this.updated|=2,this}ensureMarks(e){return oe.sameSet(this.storedMarks||this.selection.$from.marks(),e)||this.setStoredMarks(e),this}addStoredMark(e){return this.ensureMarks(e.addToSet(this.storedMarks||this.selection.$head.marks()))}removeStoredMark(e){return this.ensureMarks(e.removeFromSet(this.storedMarks||this.selection.$head.marks()))}get storedMarksSet(){return(2&this.updated)>0}addStep(e,t){super.addStep(e,t),this.updated=-3&this.updated,this.storedMarks=null}setTime(e){return this.time=e,this}replaceSelection(e){return this.selection.replace(this,e),this}replaceSelectionWith(e,t=!0){let n=this.selection;return t&&(e=e.mark(this.storedMarks||(n.empty?n.$from.marks():n.$from.marksAcross(n.$to)||oe.none))),n.replaceWith(this,e),this}deleteSelection(){return this.selection.replace(this),this}insertText(e,t,n){let o=this.doc.type.schema;if(t==null)return e?this.replaceSelectionWith(o.text(e),!0):this.deleteSelection();{if(n==null&&(n=t),n=n??t,!e)return this.deleteRange(t,n);let i=this.storedMarks;if(!i){let s=this.doc.resolve(t);i=n==t?s.marks():s.marksAcross(this.doc.resolve(n))}return this.replaceRangeWith(t,n,o.text(e,i)),this.selection.empty||this.setSelection(J.near(this.selection.$to)),this}}setMeta(e,t){return this.meta[typeof e=="string"?e:e.key]=t,this}getMeta(e){return this.meta[typeof e=="string"?e:e.key]}get isGeneric(){for(let e in this.meta)return!1;return!0}scrollIntoView(){return this.updated|=4,this}get scrolledIntoView(){return(4&this.updated)>0}}function $i(r,e){return e&&r?r.bind(e):r}class $n{constructor(e,t,n){this.name=e,this.init=$i(t.init,n),this.apply=$i(t.apply,n)}}const kc=[new $n("doc",{init:r=>r.doc||r.schema.topNodeType.createAndFill(),apply:r=>r.doc}),new $n("selection",{init:(r,e)=>r.selection||J.atStart(e.doc),apply:r=>r.selection}),new $n("storedMarks",{init:r=>r.storedMarks||null,apply:(r,e,t,n)=>n.selection.$cursor?r.storedMarks:null}),new $n("scrollToSelection",{init:()=>0,apply:(r,e)=>r.scrolledIntoView?e+1:e})];class Zr{constructor(e,t){this.schema=e,this.plugins=[],this.pluginsByKey=Object.create(null),this.fields=kc.slice(),t&&t.forEach(n=>{if(this.pluginsByKey[n.key])throw new RangeError("Adding different instances of a keyed plugin ("+n.key+")");this.plugins.push(n),this.pluginsByKey[n.key]=n,n.spec.state&&this.fields.push(new $n(n.key,n.spec.state,n))})}}class Cn{constructor(e){this.config=e}get schema(){return this.config.schema}get plugins(){return this.config.plugins}apply(e){return this.applyTransaction(e).state}filterTransaction(e,t=-1){for(let n=0;n<this.config.plugins.length;n++)if(n!=t){let o=this.config.plugins[n];if(o.spec.filterTransaction&&!o.spec.filterTransaction.call(o,e,this))return!1}return!0}applyTransaction(e){if(!this.filterTransaction(e))return{state:this,transactions:[]};let t=[e],n=this.applyInner(e),o=null;for(;;){let i=!1;for(let s=0;s<this.config.plugins.length;s++){let a=this.config.plugins[s];if(a.spec.appendTransaction){let l=o?o[s].n:0,c=o?o[s].state:this,h=l<t.length&&a.spec.appendTransaction.call(a,l?t.slice(l):t,c,n);if(h&&n.filterTransaction(h,s)){if(h.setMeta("appendedTransaction",e),!o){o=[];for(let d=0;d<this.config.plugins.length;d++)o.push(d<s?{state:n,n:t.length}:{state:this,n:0})}t.push(h),n=n.applyInner(h),i=!0}o&&(o[s]={state:n,n:t.length})}}if(!i)return{state:n,transactions:t}}}applyInner(e){if(!e.before.eq(this.doc))throw new RangeError("Applying a mismatched transaction");let t=new Cn(this.config),n=this.config.fields;for(let o=0;o<n.length;o++){let i=n[o];t[i.name]=i.apply(e,this[i.name],this,t)}return t}get tr(){return new xc(this)}static create(e){let t=new Zr(e.doc?e.doc.type.schema:e.schema,e.plugins),n=new Cn(t);for(let o=0;o<t.fields.length;o++)n[t.fields[o].name]=t.fields[o].init(e,n);return n}reconfigure(e){let t=new Zr(this.schema,e.plugins),n=t.fields,o=new Cn(t);for(let i=0;i<n.length;i++){let s=n[i].name;o[s]=this.hasOwnProperty(s)?this[s]:n[i].init(e,o)}return o}toJSON(e){let t={doc:this.doc.toJSON(),selection:this.selection.toJSON()};if(this.storedMarks&&(t.storedMarks=this.storedMarks.map(n=>n.toJSON())),e&&typeof e=="object")for(let n in e){if(n=="doc"||n=="selection")throw new RangeError("The JSON fields `doc` and `selection` are reserved");let o=e[n],i=o.spec.state;i&&i.toJSON&&(t[n]=i.toJSON.call(o,this[o.key]))}return t}static fromJSON(e,t,n){if(!t)throw new RangeError("Invalid input for EditorState.fromJSON");if(!e.schema)throw new RangeError("Required config field 'schema' missing");let o=new Zr(e.schema,e.plugins),i=new Cn(o);return o.fields.forEach(s=>{if(s.name=="doc")i.doc=tn.fromJSON(e.schema,t.doc);else if(s.name=="selection")i.selection=J.fromJSON(i.doc,t.selection);else if(s.name=="storedMarks")t.storedMarks&&(i.storedMarks=t.storedMarks.map(e.schema.markFromJSON));else{if(n)for(let a in n){let l=n[a],c=l.spec.state;if(l.key==s.name&&c&&c.fromJSON&&Object.prototype.hasOwnProperty.call(t,a))return void(i[s.name]=c.fromJSON.call(l,e,t[a],i))}i[s.name]=s.init(e,i)}}),i}}function Ma(r,e,t){for(let n in r){let o=r[n];o instanceof Function?o=o.bind(e):n=="handleDOMEvents"&&(o=Ma(o,e,{})),t[n]=o}return t}class He{constructor(e){this.spec=e,this.props={},e.props&&Ma(e.props,this,this.props),this.key=e.key?e.key.key:Sa("plugin")}getState(e){return e[this.key]}}const Yr=Object.create(null);function Sa(r){return r in Yr?r+"$"+ ++Yr[r]:(Yr[r]=0,r+"$")}class Ve{constructor(e="key"){this.key=Sa(e)}get(e){return e.config.pluginsByKey[this.key]}getState(e){return e[this.key]}}const be=function(r){for(var e=0;;e++)if(!(r=r.previousSibling))return e},jn=function(r){let e=r.assignedSlot||r.parentNode;return e&&e.nodeType==11?e.host:e};let Oo=null;const ft=function(r,e,t){let n=Oo||(Oo=document.createRange());return n.setEnd(r,t??r.nodeValue.length),n.setStart(r,e||0),n},ln=function(r,e,t,n){return t&&(Ri(r,e,t,n,-1)||Ri(r,e,t,n,1))},Mc=/^(img|br|input|textarea|hr)$/i;function Ri(r,e,t,n,o){for(;;){if(r==t&&e==n)return!0;if(e==(o<0?0:at(r))){let i=r.parentNode;if(!i||i.nodeType!=1||Kn(r)||Mc.test(r.nodeName)||r.contentEditable=="false")return!1;e=be(r)+(o<0?0:1),r=i}else{if(r.nodeType!=1||(r=r.childNodes[e+(o<0?-1:0)]).contentEditable=="false")return!1;e=o<0?at(r):0}}}function at(r){return r.nodeType==3?r.nodeValue.length:r.childNodes.length}function Kn(r){let e;for(let t=r;t&&!(e=t.pmViewDesc);t=t.parentNode);return e&&e.node&&e.node.isBlock&&(e.dom==r||e.contentDOM==r)}const Ir=function(r){return r.focusNode&&ln(r.focusNode,r.focusOffset,r.anchorNode,r.anchorOffset)};function qt(r,e){let t=document.createEvent("Event");return t.initEvent("keydown",!0,!0),t.keyCode=r,t.key=t.code=e,t}const ht=typeof navigator<"u"?navigator:null,Pi=typeof document<"u"?document:null,Bt=ht&&ht.userAgent||"",Eo=/Edge\/(\d+)/.exec(Bt),Oa=/MSIE \d/.exec(Bt),To=/Trident\/(?:[7-9]|\d{2,})\..*rv:(\d+)/.exec(Bt),$e=!!(Oa||To||Eo),$t=Oa?document.documentMode:To?+To[1]:Eo?+Eo[1]:0,tt=!$e&&/gecko\/(\d+)/i.test(Bt);tt&&(/Firefox\/(\d+)/.exec(Bt)||[0,0])[1];const No=!$e&&/Chrome\/(\d+)/.exec(Bt),Oe=!!No,Sc=No?+No[1]:0,Le=!$e&&!!ht&&/Apple Computer/.test(ht.vendor),En=Le&&(/Mobile\/\w+/.test(Bt)||!!ht&&ht.maxTouchPoints>2),Ke=En||!!ht&&/Mac/.test(ht.platform),Oc=!!ht&&/Win/.test(ht.platform),Ze=/Android \d/.test(Bt),Un=!!Pi&&"webkitFontSmoothing"in Pi.documentElement.style,Ec=Un?+(/\bAppleWebKit\/(\d+)/.exec(navigator.userAgent)||[0,0])[1]:0;function Tc(r){let e=r.defaultView&&r.defaultView.visualViewport;return e?{left:0,right:e.width,top:0,bottom:e.height}:{left:0,right:r.documentElement.clientWidth,top:0,bottom:r.documentElement.clientHeight}}function ut(r,e){return typeof r=="number"?r:r[e]}function Nc(r){let e=r.getBoundingClientRect(),t=e.width/r.offsetWidth||1,n=e.height/r.offsetHeight||1;return{left:e.left,right:e.left+r.clientWidth*t,top:e.top,bottom:e.top+r.clientHeight*n}}function zi(r,e,t){let n=r.someProp("scrollThreshold")||0,o=r.someProp("scrollMargin")||5,i=r.dom.ownerDocument;for(let s=t||r.dom;s;s=jn(s)){if(s.nodeType!=1)continue;let a=s,l=a==i.body,c=l?Tc(i):Nc(a),h=0,d=0;if(e.top<c.top+ut(n,"top")?d=-(c.top-e.top+ut(o,"top")):e.bottom>c.bottom-ut(n,"bottom")&&(d=e.bottom-e.top>c.bottom-c.top?e.top+ut(o,"top")-c.top:e.bottom-c.bottom+ut(o,"bottom")),e.left<c.left+ut(n,"left")?h=-(c.left-e.left+ut(o,"left")):e.right>c.right-ut(n,"right")&&(h=e.right-c.right+ut(o,"right")),h||d)if(l)i.defaultView.scrollBy(h,d);else{let p=a.scrollLeft,f=a.scrollTop;d&&(a.scrollTop+=d),h&&(a.scrollLeft+=h);let u=a.scrollLeft-p,m=a.scrollTop-f;e={left:e.left-u,top:e.top-m,right:e.right-u,bottom:e.bottom-m}}if(l||/^(fixed|sticky)$/.test(getComputedStyle(s).position))break}}function Fi(r){let e=[],t=r.ownerDocument;for(let n=r;n&&(e.push({dom:n,top:n.scrollTop,left:n.scrollLeft}),r!=t);n=jn(n));return e}function Bi(r,e){for(let t=0;t<r.length;t++){let{dom:n,top:o,left:i}=r[t];n.scrollTop!=o+e&&(n.scrollTop=o+e),n.scrollLeft!=i&&(n.scrollLeft=i)}}let un=null;function Ea(r,e){let t,n,o,i,s=2e8,a=0,l=e.top,c=e.top;for(let h=r.firstChild,d=0;h;h=h.nextSibling,d++){let p;if(h.nodeType==1)p=h.getClientRects();else{if(h.nodeType!=3)continue;p=ft(h).getClientRects()}for(let f=0;f<p.length;f++){let u=p[f];if(u.top<=l&&u.bottom>=c){l=Math.max(u.bottom,l),c=Math.min(u.top,c);let m=u.left>e.left?u.left-e.left:u.right<e.left?e.left-u.right:0;if(m<s){t=h,s=m,n=m&&t.nodeType==3?{left:u.right<e.left?u.right:u.left,top:e.top}:e,h.nodeType==1&&m&&(a=d+(e.left>=(u.left+u.right)/2?1:0));continue}}else u.top>e.top&&!o&&u.left<=e.left&&u.right>=e.left&&(o=h,i={left:Math.max(u.left,Math.min(u.right,e.left)),top:u.top});!t&&(e.left>=u.right&&e.top>=u.top||e.left>=u.left&&e.top>=u.bottom)&&(a=d+1)}}return!t&&o&&(t=o,n=i,s=0),t&&t.nodeType==3?function(h,d){let p=h.nodeValue.length,f=document.createRange();for(let u=0;u<p;u++){f.setEnd(h,u+1),f.setStart(h,u);let m=kt(f,1);if(m.top!=m.bottom&&Zo(d,m))return{node:h,offset:u+(d.left>=(m.left+m.right)/2?1:0)}}return{node:h,offset:0}}(t,n):!t||s&&t.nodeType==1?{node:r,offset:a}:Ea(t,n)}function Zo(r,e){return r.left>=e.left-1&&r.left<=e.right+1&&r.top>=e.top-1&&r.top<=e.bottom+1}function Ta(r,e,t){let n=r.childNodes.length;if(n&&t.top<t.bottom)for(let o=Math.max(0,Math.min(n-1,Math.floor(n*(e.top-t.top)/(t.bottom-t.top))-2)),i=o;;){let s=r.childNodes[i];if(s.nodeType==1){let a=s.getClientRects();for(let l=0;l<a.length;l++){let c=a[l];if(Zo(e,c))return Ta(s,e,c)}}if((i=(i+1)%n)==o)break}return r}function Lc(r,e){let t,n=r.dom.ownerDocument,o=0,i=function(c,h,d){if(c.caretPositionFromPoint)try{let p=c.caretPositionFromPoint(h,d);if(p)return{node:p.offsetNode,offset:p.offset}}catch{}if(c.caretRangeFromPoint){let p=c.caretRangeFromPoint(h,d);if(p)return{node:p.startContainer,offset:p.startOffset}}}(n,e.left,e.top);i&&({node:t,offset:o}=i);let s,a=(r.root.elementFromPoint?r.root:n).elementFromPoint(e.left,e.top);if(!a||!r.dom.contains(a.nodeType!=1?a.parentNode:a)){let c=r.dom.getBoundingClientRect();if(!Zo(e,c)||(a=Ta(r.dom,e,c),!a))return null}if(Le)for(let c=a;t&&c;c=jn(c))c.draggable&&(t=void 0);if(a=function(c,h){let d=c.parentNode;return d&&/^li$/i.test(d.nodeName)&&h.left<c.getBoundingClientRect().left?d:c}(a,e),t){if(tt&&t.nodeType==1&&(o=Math.min(o,t.childNodes.length),o<t.childNodes.length)){let h,d=t.childNodes[o];d.nodeName=="IMG"&&(h=d.getBoundingClientRect()).right<=e.left&&h.bottom>e.top&&o++}let c;Un&&o&&t.nodeType==1&&(c=t.childNodes[o-1]).nodeType==1&&c.contentEditable=="false"&&c.getBoundingClientRect().top>=e.top&&o--,t==r.dom&&o==t.childNodes.length-1&&t.lastChild.nodeType==1&&e.top>t.lastChild.getBoundingClientRect().bottom?s=r.state.doc.content.size:o!=0&&t.nodeType==1&&t.childNodes[o-1].nodeName=="BR"||(s=function(h,d,p,f){let u=-1;for(let m=d,g=!1;m!=h.dom;){let y=h.docView.nearestDesc(m,!0);if(!y)return null;if(y.dom.nodeType==1&&(y.node.isBlock&&y.parent&&!g||!y.contentDOM)){let C=y.dom.getBoundingClientRect();if(y.node.isBlock&&y.parent&&!g&&(g=!0,C.left>f.left||C.top>f.top?u=y.posBefore:(C.right<f.left||C.bottom<f.top)&&(u=y.posAfter)),!y.contentDOM&&u<0&&!y.node.isText)return(y.node.isBlock?f.top<(C.top+C.bottom)/2:f.left<(C.left+C.right)/2)?y.posBefore:y.posAfter}m=y.dom.parentNode}return u>-1?u:h.docView.posFromDOM(d,p,-1)}(r,t,o,e))}s==null&&(s=function(c,h,d){let{node:p,offset:f}=Ea(h,d),u=-1;if(p.nodeType==1&&!p.firstChild){let m=p.getBoundingClientRect();u=m.left!=m.right&&d.left>(m.left+m.right)/2?1:-1}return c.docView.posFromDOM(p,f,u)}(r,a,e));let l=r.docView.nearestDesc(a,!0);return{pos:s,inside:l?l.posAtStart-l.border:-1}}function Vi(r){return r.top<r.bottom||r.left<r.right}function kt(r,e){let t=r.getClientRects();if(t.length){let n=t[e<0?0:t.length-1];if(Vi(n))return n}return Array.prototype.find.call(t,Vi)||r.getBoundingClientRect()}const Ac=/[\u0590-\u05f4\u0600-\u06ff\u0700-\u08ac]/;function Na(r,e,t){let{node:n,offset:o,atom:i}=r.docView.domFromPos(e,t<0?-1:1),s=Un||tt;if(n.nodeType==3){if(!s||!Ac.test(n.nodeValue)&&(t<0?o:o!=n.nodeValue.length)){let a=o,l=o,c=t<0?1:-1;return t<0&&!o?(l++,c=-1):t>=0&&o==n.nodeValue.length?(a--,c=1):t<0?a--:l++,Ln(kt(ft(n,a,l),c),c<0)}{let a=kt(ft(n,o,o),t);if(tt&&o&&/\s/.test(n.nodeValue[o-1])&&o<n.nodeValue.length){let l=kt(ft(n,o-1,o-1),-1);if(l.top==a.top){let c=kt(ft(n,o,o+1),-1);if(c.top!=a.top)return Ln(c,c.left<l.left)}}return a}}if(!r.state.doc.resolve(e-(i||0)).parent.inlineContent){if(i==null&&o&&(t<0||o==at(n))){let a=n.childNodes[o-1];if(a.nodeType==1)return Xr(a.getBoundingClientRect(),!1)}if(i==null&&o<at(n)){let a=n.childNodes[o];if(a.nodeType==1)return Xr(a.getBoundingClientRect(),!0)}return Xr(n.getBoundingClientRect(),t>=0)}if(i==null&&o&&(t<0||o==at(n))){let a=n.childNodes[o-1],l=a.nodeType==3?ft(a,at(a)-(s?0:1)):a.nodeType!=1||a.nodeName=="BR"&&a.nextSibling?null:a;if(l)return Ln(kt(l,1),!1)}if(i==null&&o<at(n)){let a=n.childNodes[o];for(;a.pmViewDesc&&a.pmViewDesc.ignoreForCoords;)a=a.nextSibling;let l=a?a.nodeType==3?ft(a,0,s?0:1):a.nodeType==1?a:null:null;if(l)return Ln(kt(l,-1),!0)}return Ln(kt(n.nodeType==3?ft(n):n,-t),t>=0)}function Ln(r,e){if(r.width==0)return r;let t=e?r.left:r.right;return{top:r.top,bottom:r.bottom,left:t,right:t}}function Xr(r,e){if(r.height==0)return r;let t=e?r.top:r.bottom;return{top:t,bottom:t,left:r.left,right:r.right}}function Hi(r,e,t){let n=r.state,o=r.root.activeElement;n!=e&&r.updateState(e),o!=r.dom&&r.focus();try{return t()}finally{n!=e&&r.updateState(n),o!=r.dom&&o&&o.focus()}}const Dc=/[\u0590-\u08ac]/;let qi=null,ji=null,Ki=!1;function Ic(r,e,t){return qi==e&&ji==t?Ki:(qi=e,ji=t,Ki=t=="up"||t=="down"?function(n,o,i){let s=o.selection,a=i=="up"?s.$from:s.$to;return Hi(n,o,()=>{let{node:l}=n.docView.domFromPos(a.pos,i=="up"?-1:1);for(;;){let h=n.docView.nearestDesc(l,!0);if(!h)break;if(h.node.isBlock){l=h.contentDOM||h.dom;break}l=h.dom.parentNode}let c=Na(n,a.pos,1);for(let h=l.firstChild;h;h=h.nextSibling){let d;if(h.nodeType==1)d=h.getClientRects();else{if(h.nodeType!=3)continue;d=ft(h,0,h.nodeValue.length).getClientRects()}for(let p=0;p<d.length;p++){let f=d[p];if(f.bottom>f.top+1&&(i=="up"?c.top-f.top>2*(f.bottom-c.top):f.bottom-c.bottom>2*(c.bottom-f.top)))return!1}}return!0})}(r,e,t):function(n,o,i){let{$head:s}=o.selection;if(!s.parent.isTextblock)return!1;let a=s.parentOffset,l=!a,c=a==s.parent.content.size,h=n.domSelection();return Dc.test(s.parent.textContent)&&h.modify?Hi(n,o,()=>{let{focusNode:d,focusOffset:p,anchorNode:f,anchorOffset:u}=n.domSelectionRange(),m=h.caretBidiLevel;h.modify("move",i,"character");let g=s.depth?n.docView.domAfterPos(s.before()):n.dom,{focusNode:y,focusOffset:C}=n.domSelectionRange(),w=y&&!g.contains(y.nodeType==1?y:y.parentNode)||d==y&&p==C;try{h.collapse(f,u),d&&(d!=f||p!=u)&&h.extend&&h.extend(d,p)}catch{}return m!=null&&(h.caretBidiLevel=m),w}):i=="left"||i=="backward"?l:c}(r,e,t))}class Gn{constructor(e,t,n,o){this.parent=e,this.children=t,this.dom=n,this.contentDOM=o,this.dirty=0,n.pmViewDesc=this}matchesWidget(e){return!1}matchesMark(e){return!1}matchesNode(e,t,n){return!1}matchesHack(e){return!1}parseRule(){return null}stopEvent(e){return!1}get size(){let e=0;for(let t=0;t<this.children.length;t++)e+=this.children[t].size;return e}get border(){return 0}destroy(){this.parent=void 0,this.dom.pmViewDesc==this&&(this.dom.pmViewDesc=void 0);for(let e=0;e<this.children.length;e++)this.children[e].destroy()}posBeforeChild(e){for(let t=0,n=this.posAtStart;;t++){let o=this.children[t];if(o==e)return n;n+=o.size}}get posBefore(){return this.parent.posBeforeChild(this)}get posAtStart(){return this.parent?this.parent.posBeforeChild(this)+this.border:0}get posAfter(){return this.posBefore+this.size}get posAtEnd(){return this.posAtStart+this.size-2*this.border}localPosFromDOM(e,t,n){if(this.contentDOM&&this.contentDOM.contains(e.nodeType==1?e:e.parentNode)){if(n<0){let i,s;if(e==this.contentDOM)i=e.childNodes[t-1];else{for(;e.parentNode!=this.contentDOM;)e=e.parentNode;i=e.previousSibling}for(;i&&(!(s=i.pmViewDesc)||s.parent!=this);)i=i.previousSibling;return i?this.posBeforeChild(s)+s.size:this.posAtStart}{let i,s;if(e==this.contentDOM)i=e.childNodes[t];else{for(;e.parentNode!=this.contentDOM;)e=e.parentNode;i=e.nextSibling}for(;i&&(!(s=i.pmViewDesc)||s.parent!=this);)i=i.nextSibling;return i?this.posBeforeChild(s):this.posAtEnd}}let o;if(e==this.dom&&this.contentDOM)o=t>be(this.contentDOM);else if(this.contentDOM&&this.contentDOM!=this.dom&&this.dom.contains(this.contentDOM))o=2&e.compareDocumentPosition(this.contentDOM);else if(this.dom.firstChild){if(t==0)for(let i=e;;i=i.parentNode){if(i==this.dom){o=!1;break}if(i.previousSibling)break}if(o==null&&t==e.childNodes.length)for(let i=e;;i=i.parentNode){if(i==this.dom){o=!0;break}if(i.nextSibling)break}}return o??n>0?this.posAtEnd:this.posAtStart}nearestDesc(e,t=!1){for(let n=!0,o=e;o;o=o.parentNode){let i,s=this.getDesc(o);if(s&&(!t||s.node)){if(!n||!(i=s.nodeDOM)||(i.nodeType==1?i.contains(e.nodeType==1?e:e.parentNode):i==e))return s;n=!1}}}getDesc(e){let t=e.pmViewDesc;for(let n=t;n;n=n.parent)if(n==this)return t}posFromDOM(e,t,n){for(let o=e;o;o=o.parentNode){let i=this.getDesc(o);if(i)return i.localPosFromDOM(e,t,n)}return-1}descAt(e){for(let t=0,n=0;t<this.children.length;t++){let o=this.children[t],i=n+o.size;if(n==e&&i!=n){for(;!o.border&&o.children.length;)o=o.children[0];return o}if(e<i)return o.descAt(e-n-o.border);n=i}}domFromPos(e,t){if(!this.contentDOM)return{node:this.dom,offset:0,atom:e+1};let n=0,o=0;for(let i=0;n<this.children.length;n++){let s=this.children[n],a=i+s.size;if(a>e||s instanceof Aa){o=e-i;break}i=a}if(o)return this.children[n].domFromPos(o-this.children[n].border,t);for(let i;n&&!(i=this.children[n-1]).size&&i instanceof La&&i.side>=0;n--);if(t<=0){let i,s=!0;for(;i=n?this.children[n-1]:null,i&&i.dom.parentNode!=this.contentDOM;n--,s=!1);return i&&t&&s&&!i.border&&!i.domAtom?i.domFromPos(i.size,t):{node:this.contentDOM,offset:i?be(i.dom)+1:0}}{let i,s=!0;for(;i=n<this.children.length?this.children[n]:null,i&&i.dom.parentNode!=this.contentDOM;n++,s=!1);return i&&s&&!i.border&&!i.domAtom?i.domFromPos(0,t):{node:this.contentDOM,offset:i?be(i.dom):this.contentDOM.childNodes.length}}}parseRange(e,t,n=0){if(this.children.length==0)return{node:this.contentDOM,from:e,to:t,fromOffset:0,toOffset:this.contentDOM.childNodes.length};let o=-1,i=-1;for(let s=n,a=0;;a++){let l=this.children[a],c=s+l.size;if(o==-1&&e<=c){let h=s+l.border;if(e>=h&&t<=c-l.border&&l.node&&l.contentDOM&&this.contentDOM.contains(l.contentDOM))return l.parseRange(e,t,h);e=s;for(let d=a;d>0;d--){let p=this.children[d-1];if(p.size&&p.dom.parentNode==this.contentDOM&&!p.emptyChildAt(1)){o=be(p.dom)+1;break}e-=p.size}o==-1&&(o=0)}if(o>-1&&(c>t||a==this.children.length-1)){t=c;for(let h=a+1;h<this.children.length;h++){let d=this.children[h];if(d.size&&d.dom.parentNode==this.contentDOM&&!d.emptyChildAt(-1)){i=be(d.dom);break}t+=d.size}i==-1&&(i=this.contentDOM.childNodes.length);break}s=c}return{node:this.contentDOM,from:e,to:t,fromOffset:o,toOffset:i}}emptyChildAt(e){if(this.border||!this.contentDOM||!this.children.length)return!1;let t=this.children[e<0?0:this.children.length-1];return t.size==0||t.emptyChildAt(e)}domAfterPos(e){let{node:t,offset:n}=this.domFromPos(e,0);if(t.nodeType!=1||n==t.childNodes.length)throw new RangeError("No node after pos "+e);return t.childNodes[n]}setSelection(e,t,n,o=!1){let i=Math.min(e,t),s=Math.max(e,t);for(let p=0,f=0;p<this.children.length;p++){let u=this.children[p],m=f+u.size;if(i>f&&s<m)return u.setSelection(e-f-u.border,t-f-u.border,n,o);f=m}let a=this.domFromPos(e,e?-1:1),l=t==e?a:this.domFromPos(t,t?-1:1),c=n.getSelection(),h=!1;if((tt||Le)&&e==t){let{node:p,offset:f}=a;if(p.nodeType==3){if(h=!(!f||p.nodeValue[f-1]!=`
`),h&&f==p.nodeValue.length)for(let u,m=p;m;m=m.parentNode){if(u=m.nextSibling){u.nodeName=="BR"&&(a=l={node:u.parentNode,offset:be(u)+1});break}let g=m.pmViewDesc;if(g&&g.node&&g.node.isBlock)break}}else{let u=p.childNodes[f-1];h=u&&(u.nodeName=="BR"||u.contentEditable=="false")}}if(tt&&c.focusNode&&c.focusNode!=l.node&&c.focusNode.nodeType==1){let p=c.focusNode.childNodes[c.focusOffset];p&&p.contentEditable=="false"&&(o=!0)}if(!(o||h&&Le)&&ln(a.node,a.offset,c.anchorNode,c.anchorOffset)&&ln(l.node,l.offset,c.focusNode,c.focusOffset))return;let d=!1;if((c.extend||e==t)&&!h){c.collapse(a.node,a.offset);try{e!=t&&c.extend(l.node,l.offset),d=!0}catch{}}if(!d){if(e>t){let f=a;a=l,l=f}let p=document.createRange();p.setEnd(l.node,l.offset),p.setStart(a.node,a.offset),c.removeAllRanges(),c.addRange(p)}}ignoreMutation(e){return!this.contentDOM&&e.type!="selection"}get contentLost(){return this.contentDOM&&this.contentDOM!=this.dom&&!this.dom.contains(this.contentDOM)}markDirty(e,t){for(let n=0,o=0;o<this.children.length;o++){let i=this.children[o],s=n+i.size;if(n==s?e<=s&&t>=n:e<s&&t>n){let a=n+i.border,l=s-i.border;if(e>=a&&t<=l)return this.dirty=e==n||t==s?2:1,void(e!=a||t!=l||!i.contentLost&&i.dom.parentNode==this.contentDOM?i.markDirty(e-a,t-a):i.dirty=3);i.dirty=i.dom!=i.contentDOM||i.dom.parentNode!=this.contentDOM||i.children.length?3:2}n=s}this.dirty=2}markParentsDirty(){let e=1;for(let t=this.parent;t;t=t.parent,e++){let n=e==1?2:1;t.dirty<n&&(t.dirty=n)}}get domAtom(){return!1}get ignoreForCoords(){return!1}isText(e){return!1}}class La extends Gn{constructor(e,t,n,o){let i,s=t.type.toDOM;if(typeof s=="function"&&(s=s(n,()=>i?i.parent?i.parent.posBeforeChild(i):void 0:o)),!t.type.spec.raw){if(s.nodeType!=1){let a=document.createElement("span");a.appendChild(s),s=a}s.contentEditable="false",s.classList.add("ProseMirror-widget")}super(e,[],s,null),this.widget=t,this.widget=t,i=this}matchesWidget(e){return this.dirty==0&&e.type.eq(this.widget.type)}parseRule(){return{ignore:!0}}stopEvent(e){let t=this.widget.spec.stopEvent;return!!t&&t(e)}ignoreMutation(e){return e.type!="selection"||this.widget.spec.ignoreSelection}destroy(){this.widget.type.destroy(this.dom),super.destroy()}get domAtom(){return!0}get side(){return this.widget.type.side}}class _c extends Gn{constructor(e,t,n,o){super(e,[],t,null),this.textDOM=n,this.text=o}get size(){return this.text.length}localPosFromDOM(e,t){return e!=this.textDOM?this.posAtStart+(t?this.size:0):this.posAtStart+t}domFromPos(e){return{node:this.textDOM,offset:e}}ignoreMutation(e){return e.type==="characterData"&&e.target.nodeValue==e.oldValue}}class nn extends Gn{constructor(e,t,n,o){super(e,[],n,o),this.mark=t}static create(e,t,n,o){let i=o.nodeViews[t.type.name],s=i&&i(t,o,n);return s&&s.dom||(s=cn.renderSpec(document,t.type.spec.toDOM(t,n))),new nn(e,t,s.dom,s.contentDOM||s.dom)}parseRule(){return 3&this.dirty||this.mark.type.spec.reparseInView?null:{mark:this.mark.type.name,attrs:this.mark.attrs,contentElement:this.contentDOM}}matchesMark(e){return this.dirty!=3&&this.mark.eq(e)}markDirty(e,t){if(super.markDirty(e,t),this.dirty!=0){let n=this.parent;for(;!n.node;)n=n.parent;n.dirty<this.dirty&&(n.dirty=this.dirty),this.dirty=0}}slice(e,t,n){let o=nn.create(this.parent,this.mark,!0,n),i=this.children,s=this.size;t<s&&(i=Do(i,t,s,n)),e>0&&(i=Do(i,0,e,n));for(let a=0;a<i.length;a++)i[a].parent=o;return o.children=i,o}}class Rt extends Gn{constructor(e,t,n,o,i,s,a,l,c){super(e,[],i,s),this.node=t,this.outerDeco=n,this.innerDeco=o,this.nodeDOM=a}static create(e,t,n,o,i,s){let a,l=i.nodeViews[t.type.name],c=l&&l(t,i,()=>a?a.parent?a.parent.posBeforeChild(a):void 0:s,n,o),h=c&&c.dom,d=c&&c.contentDOM;if(t.isText)if(h){if(h.nodeType!=3)throw new RangeError("Text must be rendered as a DOM text node")}else h=document.createTextNode(t.text);else h||({dom:h,contentDOM:d}=cn.renderSpec(document,t.type.spec.toDOM(t)));d||t.isText||h.nodeName=="BR"||(h.hasAttribute("contenteditable")||(h.contentEditable="false"),t.type.spec.draggable&&(h.draggable=!0));let p=h;return h=_a(h,n,t),c?a=new $c(e,t,n,o,h,d||null,p,c,i,s+1):t.isText?new _r(e,t,n,o,h,p,i):new Rt(e,t,n,o,h,d||null,p,i,s+1)}parseRule(){if(this.node.type.spec.reparseInView)return null;let e={node:this.node.type.name,attrs:this.node.attrs};if(this.node.type.whitespace=="pre"&&(e.preserveWhitespace="full"),this.contentDOM)if(this.contentLost){for(let t=this.children.length-1;t>=0;t--){let n=this.children[t];if(this.dom.contains(n.dom.parentNode)){e.contentElement=n.dom.parentNode;break}}e.contentElement||(e.getContent=()=>S.empty)}else e.contentElement=this.contentDOM;else e.getContent=()=>this.node.content;return e}matchesNode(e,t,n){return this.dirty==0&&e.eq(this.node)&&Ao(t,this.outerDeco)&&n.eq(this.innerDeco)}get size(){return this.node.nodeSize}get border(){return this.node.isLeaf?0:1}updateChildren(e,t){let n=this.node.inlineContent,o=t,i=e.composing?this.localCompositionInfo(e,t):null,s=i&&i.pos>-1?i:null,a=i&&i.pos<0,l=new Pc(this,s&&s.node,e);(function(c,h,d,p){let f=h.locals(c),u=0;if(f.length==0){for(let C=0;C<c.childCount;C++){let w=c.child(C);p(w,f,h.forChild(u,w),C),u+=w.nodeSize}return}let m=0,g=[],y=null;for(let C=0;;){let w,v,x,O;for(;m<f.length&&f[m].to==u;){let M=f[m++];M.widget&&(w?(v||(v=[w])).push(M):w=M)}if(w)if(v){v.sort(zc);for(let M=0;M<v.length;M++)d(v[M],C,!!y)}else d(w,C,!!y);if(y)O=-1,x=y,y=null;else{if(!(C<c.childCount))break;O=C,x=c.child(C++)}for(let M=0;M<g.length;M++)g[M].to<=u&&g.splice(M--,1);for(;m<f.length&&f[m].from<=u&&f[m].to>u;)g.push(f[m++]);let E=u+x.nodeSize;if(x.isText){let M=E;m<f.length&&f[m].from<M&&(M=f[m].from);for(let k=0;k<g.length;k++)g[k].to<M&&(M=g[k].to);M<E&&(y=x.cut(M-u),x=x.cut(0,M-u),E=M,O=-1)}else for(;m<f.length&&f[m].to<E;)m++;p(x,x.isInline&&!x.isLeaf?g.filter(M=>!M.inline):g.slice(),h.forChild(u,x),O),u=E}})(this.node,this.innerDeco,(c,h,d)=>{c.spec.marks?l.syncToMarks(c.spec.marks,n,e):c.type.side>=0&&!d&&l.syncToMarks(h==this.node.childCount?oe.none:this.node.child(h).marks,n,e),l.placeWidget(c,e,o)},(c,h,d,p)=>{let f;l.syncToMarks(c.marks,n,e),l.findNodeMatch(c,h,d,p)||a&&e.state.selection.from>o&&e.state.selection.to<o+c.nodeSize&&(f=l.findIndexWithChild(i.node))>-1&&l.updateNodeAt(c,h,d,f,e)||l.updateNextNode(c,h,d,e,p,o)||l.addNode(c,h,d,e,o),o+=c.nodeSize}),l.syncToMarks([],n,e),this.node.isTextblock&&l.addTextblockHacks(),l.destroyRest(),(l.changed||this.dirty==2)&&(s&&this.protectLocalComposition(e,s),Da(this.contentDOM,this.children,e),En&&function(c){if(c.nodeName=="UL"||c.nodeName=="OL"){let h=c.style.cssText;c.style.cssText=h+"; list-style: square !important",window.getComputedStyle(c).listStyle,c.style.cssText=h}}(this.dom))}localCompositionInfo(e,t){let{from:n,to:o}=e.state.selection;if(!(e.state.selection instanceof K)||n<t||o>t+this.node.content.size)return null;let i=e.input.compositionNode;if(!i||!this.dom.contains(i.parentNode))return null;if(this.node.inlineContent){let s=i.nodeValue,a=function(l,c,h,d){for(let p=0,f=0;p<l.childCount&&f<=d;){let u=l.child(p++),m=f;if(f+=u.nodeSize,!u.isText)continue;let g=u.text;for(;p<l.childCount;){let y=l.child(p++);if(f+=y.nodeSize,!y.isText)break;g+=y.text}if(f>=h){if(f>=d&&g.slice(d-c.length-m,d-m)==c)return d-c.length;let y=m<d?g.lastIndexOf(c,d-m-1):-1;if(y>=0&&y+c.length+m>=h)return m+y;if(h==d&&g.length>=d+c.length-m&&g.slice(d-m,d-m+c.length)==c)return d}}return-1}(this.node.content,s,n-t,o-t);return a<0?null:{node:i,pos:a,text:s}}return{node:i,pos:-1,text:""}}protectLocalComposition(e,{node:t,pos:n,text:o}){if(this.getDesc(t))return;let i=t;for(;i.parentNode!=this.contentDOM;i=i.parentNode){for(;i.previousSibling;)i.parentNode.removeChild(i.previousSibling);for(;i.nextSibling;)i.parentNode.removeChild(i.nextSibling);i.pmViewDesc&&(i.pmViewDesc=void 0)}let s=new _c(this,i,t,o);e.input.compositionNodes.push(s),this.children=Do(this.children,n,n+o.length,e,s)}update(e,t,n,o){return!(this.dirty==3||!e.sameMarkup(this.node))&&(this.updateInner(e,t,n,o),!0)}updateInner(e,t,n,o){this.updateOuterDeco(t),this.node=e,this.innerDeco=n,this.contentDOM&&this.updateChildren(o,this.posAtStart),this.dirty=0}updateOuterDeco(e){if(Ao(e,this.outerDeco))return;let t=this.nodeDOM.nodeType!=1,n=this.dom;this.dom=Ia(this.dom,this.nodeDOM,Lo(this.outerDeco,this.node,t),Lo(e,this.node,t)),this.dom!=n&&(n.pmViewDesc=void 0,this.dom.pmViewDesc=this),this.outerDeco=e}selectNode(){this.nodeDOM.nodeType==1&&this.nodeDOM.classList.add("ProseMirror-selectednode"),!this.contentDOM&&this.node.type.spec.draggable||(this.dom.draggable=!0)}deselectNode(){this.nodeDOM.nodeType==1&&this.nodeDOM.classList.remove("ProseMirror-selectednode"),!this.contentDOM&&this.node.type.spec.draggable||this.dom.removeAttribute("draggable")}get domAtom(){return this.node.isAtom}}function Ji(r,e,t,n,o){_a(n,e,r);let i=new Rt(void 0,r,e,t,n,n,n,o,0);return i.contentDOM&&i.updateChildren(o,0),i}class _r extends Rt{constructor(e,t,n,o,i,s,a){super(e,t,n,o,i,null,s,a,0)}parseRule(){let e=this.nodeDOM.parentNode;for(;e&&e!=this.dom&&!e.pmIsDeco;)e=e.parentNode;return{skip:e||!0}}update(e,t,n,o){return!(this.dirty==3||this.dirty!=0&&!this.inParent()||!e.sameMarkup(this.node))&&(this.updateOuterDeco(t),this.dirty==0&&e.text==this.node.text||e.text==this.nodeDOM.nodeValue||(this.nodeDOM.nodeValue=e.text,o.trackWrites==this.nodeDOM&&(o.trackWrites=null)),this.node=e,this.dirty=0,!0)}inParent(){let e=this.parent.contentDOM;for(let t=this.nodeDOM;t;t=t.parentNode)if(t==e)return!0;return!1}domFromPos(e){return{node:this.nodeDOM,offset:e}}localPosFromDOM(e,t,n){return e==this.nodeDOM?this.posAtStart+Math.min(t,this.node.text.length):super.localPosFromDOM(e,t,n)}ignoreMutation(e){return e.type!="characterData"&&e.type!="selection"}slice(e,t,n){let o=this.node.cut(e,t),i=document.createTextNode(o.text);return new _r(this.parent,o,this.outerDeco,this.innerDeco,i,i,n)}markDirty(e,t){super.markDirty(e,t),this.dom==this.nodeDOM||e!=0&&t!=this.nodeDOM.nodeValue.length||(this.dirty=3)}get domAtom(){return!1}isText(e){return this.node.text==e}}class Aa extends Gn{parseRule(){return{ignore:!0}}matchesHack(e){return this.dirty==0&&this.dom.nodeName==e}get domAtom(){return!0}get ignoreForCoords(){return this.dom.nodeName=="IMG"}}class $c extends Rt{constructor(e,t,n,o,i,s,a,l,c,h){super(e,t,n,o,i,s,a,c,h),this.spec=l}update(e,t,n,o){if(this.dirty==3)return!1;if(this.spec.update){let i=this.spec.update(e,t,n);return i&&this.updateInner(e,t,n,o),i}return!(!this.contentDOM&&!e.isLeaf)&&super.update(e,t,n,o)}selectNode(){this.spec.selectNode?this.spec.selectNode():super.selectNode()}deselectNode(){this.spec.deselectNode?this.spec.deselectNode():super.deselectNode()}setSelection(e,t,n,o){this.spec.setSelection?this.spec.setSelection(e,t,n):super.setSelection(e,t,n,o)}destroy(){this.spec.destroy&&this.spec.destroy(),super.destroy()}stopEvent(e){return!!this.spec.stopEvent&&this.spec.stopEvent(e)}ignoreMutation(e){return this.spec.ignoreMutation?this.spec.ignoreMutation(e):super.ignoreMutation(e)}}function Da(r,e,t){let n=r.firstChild,o=!1;for(let i=0;i<e.length;i++){let s=e[i],a=s.dom;if(a.parentNode==r){for(;a!=n;)n=Wi(n),o=!0;n=n.nextSibling}else o=!0,r.insertBefore(a,n);if(s instanceof nn){let l=n?n.previousSibling:r.lastChild;Da(s.contentDOM,s.children,t),n=l?l.nextSibling:r.firstChild}}for(;n;)n=Wi(n),o=!0;o&&t.trackWrites==r&&(t.trackWrites=null)}const Pn=function(r){r&&(this.nodeName=r)};Pn.prototype=Object.create(null);const Ut=[new Pn];function Lo(r,e,t){if(r.length==0)return Ut;let n=t?Ut[0]:new Pn,o=[n];for(let i=0;i<r.length;i++){let s=r[i].type.attrs;if(s){s.nodeName&&o.push(n=new Pn(s.nodeName));for(let a in s){let l=s[a];l!=null&&(t&&o.length==1&&o.push(n=new Pn(e.isInline?"span":"div")),a=="class"?n.class=(n.class?n.class+" ":"")+l:a=="style"?n.style=(n.style?n.style+";":"")+l:a!="nodeName"&&(n[a]=l))}}}return o}function Ia(r,e,t,n){if(t==Ut&&n==Ut)return e;let o=e;for(let i=0;i<n.length;i++){let s=n[i],a=t[i];if(i){let l;a&&a.nodeName==s.nodeName&&o!=r&&(l=o.parentNode)&&l.nodeName.toLowerCase()==s.nodeName||(l=document.createElement(s.nodeName),l.pmIsDeco=!0,l.appendChild(o),a=Ut[0]),o=l}Rc(o,a||Ut[0],s)}return o}function Rc(r,e,t){for(let n in e)n=="class"||n=="style"||n=="nodeName"||n in t||r.removeAttribute(n);for(let n in t)n!="class"&&n!="style"&&n!="nodeName"&&t[n]!=e[n]&&r.setAttribute(n,t[n]);if(e.class!=t.class){let n=e.class?e.class.split(" ").filter(Boolean):[],o=t.class?t.class.split(" ").filter(Boolean):[];for(let i=0;i<n.length;i++)o.indexOf(n[i])==-1&&r.classList.remove(n[i]);for(let i=0;i<o.length;i++)n.indexOf(o[i])==-1&&r.classList.add(o[i]);r.classList.length==0&&r.removeAttribute("class")}if(e.style!=t.style){if(e.style){let n,o=/\s*([\w\-\xa1-\uffff]+)\s*:(?:"(?:\\.|[^"])*"|'(?:\\.|[^'])*'|\(.*?\)|[^;])*/g;for(;n=o.exec(e.style);)r.style.removeProperty(n[1])}t.style&&(r.style.cssText+=t.style)}}function _a(r,e,t){return Ia(r,r,Ut,Lo(e,t,r.nodeType!=1))}function Ao(r,e){if(r.length!=e.length)return!1;for(let t=0;t<r.length;t++)if(!r[t].type.eq(e[t].type))return!1;return!0}function Wi(r){let e=r.nextSibling;return r.parentNode.removeChild(r),e}class Pc{constructor(e,t,n){this.lock=t,this.view=n,this.index=0,this.stack=[],this.changed=!1,this.top=e,this.preMatch=function(o,i){let s=i,a=s.children.length,l=o.childCount,c=new Map,h=[];e:for(;l>0;){let d;for(;;)if(a){let f=s.children[a-1];if(!(f instanceof nn)){d=f,a--;break}s=f,a=f.children.length}else{if(s==i)break e;a=s.parent.children.indexOf(s),s=s.parent}let p=d.node;if(p){if(p!=o.child(l-1))break;--l,c.set(d,l),h.push(d)}}return{index:l,matched:c,matches:h.reverse()}}(e.node.content,e)}destroyBetween(e,t){if(e!=t){for(let n=e;n<t;n++)this.top.children[n].destroy();this.top.children.splice(e,t-e),this.changed=!0}}destroyRest(){this.destroyBetween(this.index,this.top.children.length)}syncToMarks(e,t,n){let o=0,i=this.stack.length>>1,s=Math.min(i,e.length);for(;o<s&&(o==i-1?this.top:this.stack[o+1<<1]).matchesMark(e[o])&&e[o].type.spec.spanning!==!1;)o++;for(;o<i;)this.destroyRest(),this.top.dirty=0,this.index=this.stack.pop(),this.top=this.stack.pop(),i--;for(;i<e.length;){this.stack.push(this.top,this.index+1);let a=-1;for(let l=this.index;l<Math.min(this.index+3,this.top.children.length);l++){let c=this.top.children[l];if(c.matchesMark(e[i])&&!this.isLocked(c.dom)){a=l;break}}if(a>-1)a>this.index&&(this.changed=!0,this.destroyBetween(this.index,a)),this.top=this.top.children[this.index];else{let l=nn.create(this.top,e[i],t,n);this.top.children.splice(this.index,0,l),this.top=l,this.changed=!0}this.index=0,i++}}findNodeMatch(e,t,n,o){let i,s=-1;if(o>=this.preMatch.index&&(i=this.preMatch.matches[o-this.preMatch.index]).parent==this.top&&i.matchesNode(e,t,n))s=this.top.children.indexOf(i,this.index);else for(let a=this.index,l=Math.min(this.top.children.length,a+5);a<l;a++){let c=this.top.children[a];if(c.matchesNode(e,t,n)&&!this.preMatch.matched.has(c)){s=a;break}}return!(s<0)&&(this.destroyBetween(this.index,s),this.index++,!0)}updateNodeAt(e,t,n,o,i){let s=this.top.children[o];return s.dirty==3&&s.dom==s.contentDOM&&(s.dirty=2),!!s.update(e,t,n,i)&&(this.destroyBetween(this.index,o),this.index++,!0)}findIndexWithChild(e){for(;;){let t=e.parentNode;if(!t)return-1;if(t==this.top.contentDOM){let n=e.pmViewDesc;if(n){for(let o=this.index;o<this.top.children.length;o++)if(this.top.children[o]==n)return o}return-1}e=t}}updateNextNode(e,t,n,o,i,s){for(let a=this.index;a<this.top.children.length;a++){let l=this.top.children[a];if(l instanceof Rt){let c=this.preMatch.matched.get(l);if(c!=null&&c!=i)return!1;let h,d=l.dom,p=this.isLocked(d)&&!(e.isText&&l.node&&l.node.isText&&l.nodeDOM.nodeValue==e.text&&l.dirty!=3&&Ao(t,l.outerDeco));if(!p&&l.update(e,t,n,o))return this.destroyBetween(this.index,a),l.dom!=d&&(this.changed=!0),this.index++,!0;if(!p&&(h=this.recreateWrapper(l,e,t,n,o,s)))return this.top.children[this.index]=h,h.contentDOM&&(h.dirty=2,h.updateChildren(o,s+1),h.dirty=0),this.changed=!0,this.index++,!0;break}}return!1}recreateWrapper(e,t,n,o,i,s){if(e.dirty||t.isAtom||!e.children.length||!e.node.content.eq(t.content))return null;let a=Rt.create(this.top,t,n,o,i,s);if(a.contentDOM){a.children=e.children,e.children=[];for(let l of a.children)l.parent=a}return e.destroy(),a}addNode(e,t,n,o,i){let s=Rt.create(this.top,e,t,n,o,i);s.contentDOM&&s.updateChildren(o,i+1),this.top.children.splice(this.index++,0,s),this.changed=!0}placeWidget(e,t,n){let o=this.index<this.top.children.length?this.top.children[this.index]:null;if(!o||!o.matchesWidget(e)||e!=o.widget&&o.widget.type.toDOM.parentNode){let i=new La(this.top,e,t,n);this.top.children.splice(this.index++,0,i),this.changed=!0}else this.index++}addTextblockHacks(){let e=this.top.children[this.index-1],t=this.top;for(;e instanceof nn;)t=e,e=t.children[t.children.length-1];(!e||!(e instanceof _r)||/\n$/.test(e.node.text)||this.view.requiresGeckoHackNode&&/\s$/.test(e.node.text))&&((Le||Oe)&&e&&e.dom.contentEditable=="false"&&this.addHackNode("IMG",t),this.addHackNode("BR",this.top))}addHackNode(e,t){if(t==this.top&&this.index<t.children.length&&t.children[this.index].matchesHack(e))this.index++;else{let n=document.createElement(e);e=="IMG"&&(n.className="ProseMirror-separator",n.alt=""),e=="BR"&&(n.className="ProseMirror-trailingBreak");let o=new Aa(this.top,[],n,null);t!=this.top?t.children.push(o):t.children.splice(this.index++,0,o),this.changed=!0}}isLocked(e){return this.lock&&(e==this.lock||e.nodeType==1&&e.contains(this.lock.parentNode))}}function zc(r,e){return r.type.side-e.type.side}function Do(r,e,t,n,o){let i=[];for(let s=0,a=0;s<r.length;s++){let l=r[s],c=a,h=a+=l.size;c>=t||h<=e?i.push(l):(c<e&&i.push(l.slice(0,e-c,n)),o&&(i.push(o),o=void 0),h>t&&i.push(l.slice(t-c,l.size,n)))}return i}function Yo(r,e=null){let t=r.domSelectionRange(),n=r.state.doc;if(!t.focusNode)return null;let o=r.docView.nearestDesc(t.focusNode),i=o&&o.size==0,s=r.docView.posFromDOM(t.focusNode,t.focusOffset,1);if(s<0)return null;let a,l,c=n.resolve(s);if(Ir(t)){for(a=c;o&&!o.node;)o=o.parent;let h=o.node;if(o&&h.isAtom&&V.isSelectable(h)&&o.parent&&(!h.isInline||!function(d,p,f){for(let u=p==0,m=p==at(d);u||m;){if(d==f)return!0;let g=be(d);if(!(d=d.parentNode))return!1;u=u&&g==0,m=m&&g==at(d)}}(t.focusNode,t.focusOffset,o.dom))){let d=o.posBefore;l=new V(s==d?c:n.resolve(d))}}else{let h=r.docView.posFromDOM(t.anchorNode,t.anchorOffset,1);if(h<0)return null;a=n.resolve(h)}return l||(l=Xo(r,a,c,e=="pointer"||r.state.selection.head<c.pos&&!i?1:-1)),l}function Ui(r){return r.editable?r.hasFocus():Ra(r)&&document.activeElement&&document.activeElement.contains(r.dom)}function yt(r,e=!1){let t=r.state.selection;if($a(r,t),Ui(r)){if(!e&&r.input.mouseDown&&r.input.mouseDown.allowDefault&&Oe){let n=r.domSelectionRange(),o=r.domObserver.currentSelection;if(n.anchorNode&&o.anchorNode&&ln(n.anchorNode,n.anchorOffset,o.anchorNode,o.anchorOffset))return r.input.mouseDown.delayedSelectionSync=!0,void r.domObserver.setCurSelection()}if(r.domObserver.disconnectSelection(),r.cursorWrapper)(function(n){let o=n.domSelection(),i=document.createRange(),s=n.cursorWrapper.dom,a=s.nodeName=="IMG";a?i.setEnd(s.parentNode,be(s)+1):i.setEnd(s,0),i.collapse(!1),o.removeAllRanges(),o.addRange(i),!a&&!n.state.selection.visible&&$e&&$t<=11&&(s.disabled=!0,s.disabled=!1)})(r);else{let n,o,{anchor:i,head:s}=t;!Gi||t instanceof K||(t.$from.parent.inlineContent||(n=Zi(r,t.from)),t.empty||t.$from.parent.inlineContent||(o=Zi(r,t.to))),r.docView.setSelection(i,s,r.root,e),Gi&&(n&&Yi(n),o&&Yi(o)),t.visible?r.dom.classList.remove("ProseMirror-hideselection"):(r.dom.classList.add("ProseMirror-hideselection"),"onselectionchange"in document&&function(a){let l=a.dom.ownerDocument;l.removeEventListener("selectionchange",a.input.hideSelectionGuard);let c=a.domSelectionRange(),h=c.anchorNode,d=c.anchorOffset;l.addEventListener("selectionchange",a.input.hideSelectionGuard=()=>{c.anchorNode==h&&c.anchorOffset==d||(l.removeEventListener("selectionchange",a.input.hideSelectionGuard),setTimeout(()=>{Ui(a)&&!a.state.selection.visible||a.dom.classList.remove("ProseMirror-hideselection")},20))})}(r))}r.domObserver.setCurSelection(),r.domObserver.connectSelection()}}const Gi=Le||Oe&&Sc<63;function Zi(r,e){let{node:t,offset:n}=r.docView.domFromPos(e,0),o=n<t.childNodes.length?t.childNodes[n]:null,i=n?t.childNodes[n-1]:null;if(Le&&o&&o.contentEditable=="false")return Qr(o);if(!(o&&o.contentEditable!="false"||i&&i.contentEditable!="false")){if(o)return Qr(o);if(i)return Qr(i)}}function Qr(r){return r.contentEditable="true",Le&&r.draggable&&(r.draggable=!1,r.wasDraggable=!0),r}function Yi(r){r.contentEditable="false",r.wasDraggable&&(r.draggable=!0,r.wasDraggable=null)}function $a(r,e){if(e instanceof V){let t=r.docView.descAt(e.from);t!=r.lastSelectedViewDesc&&(Xi(r),t&&t.selectNode(),r.lastSelectedViewDesc=t)}else Xi(r)}function Xi(r){r.lastSelectedViewDesc&&(r.lastSelectedViewDesc.parent&&r.lastSelectedViewDesc.deselectNode(),r.lastSelectedViewDesc=void 0)}function Xo(r,e,t,n){return r.someProp("createSelectionBetween",o=>o(r,e,t))||K.between(e,t,n)}function Qi(r){return!(r.editable&&!r.hasFocus())&&Ra(r)}function Ra(r){let e=r.domSelectionRange();if(!e.anchorNode)return!1;try{return r.dom.contains(e.anchorNode.nodeType==3?e.anchorNode.parentNode:e.anchorNode)&&(r.editable||r.dom.contains(e.focusNode.nodeType==3?e.focusNode.parentNode:e.focusNode))}catch{return!1}}function Io(r,e){let{$anchor:t,$head:n}=r.selection,o=e>0?t.max(n):t.min(n),i=o.parent.inlineContent?o.depth?r.doc.resolve(e>0?o.after():o.before()):null:o;return i&&J.findFrom(i,e)}function Ot(r,e){return r.dispatch(r.state.tr.setSelection(e).scrollIntoView()),!0}function es(r,e,t){let n=r.state.selection;if(!(n instanceof K)){if(n instanceof V&&n.node.isInline)return Ot(r,new K(e>0?n.$to:n.$from));{let o=Io(r.state,e);return!!o&&Ot(r,o)}}if(t.indexOf("s")>-1){let{$head:o}=n,i=o.textOffset?null:e<0?o.nodeBefore:o.nodeAfter;if(!i||i.isText||!i.isLeaf)return!1;let s=r.state.doc.resolve(o.pos+i.nodeSize*(e<0?-1:1));return Ot(r,new K(n.$anchor,s))}if(!n.empty)return!1;if(r.endOfTextblock(e>0?"forward":"backward")){let o=Io(r.state,e);return!!(o&&o instanceof V)&&Ot(r,o)}if(!(Ke&&t.indexOf("m")>-1)){let o,i=n.$head,s=i.textOffset?null:e<0?i.nodeBefore:i.nodeAfter;if(!s||s.isText)return!1;let a=e<0?i.pos-s.nodeSize:i.pos;return!!(s.isAtom||(o=r.docView.descAt(a))&&!o.contentDOM)&&(V.isSelectable(s)?Ot(r,new V(e<0?r.state.doc.resolve(i.pos-s.nodeSize):i)):!!Un&&Ot(r,new K(r.state.doc.resolve(e<0?a:a+s.nodeSize))))}}function Qn(r){return r.nodeType==3?r.nodeValue.length:r.childNodes.length}function An(r,e){let t=r.pmViewDesc;return t&&t.size==0&&(e<0||r.nextSibling||r.nodeName!="BR")}function fn(r,e){return e<0?function(t){let n=t.domSelectionRange(),o=n.focusNode,i=n.focusOffset;if(!o)return;let s,a,l=!1;for(tt&&o.nodeType==1&&i<Qn(o)&&An(o.childNodes[i],-1)&&(l=!0);;)if(i>0){if(o.nodeType!=1)break;{let c=o.childNodes[i-1];if(An(c,-1))s=o,a=--i;else{if(c.nodeType!=3)break;o=c,i=o.nodeValue.length}}}else{if(ts(o))break;{let c=o.previousSibling;for(;c&&An(c,-1);)s=o.parentNode,a=be(c),c=c.previousSibling;if(c)o=c,i=Qn(o);else{if(o=o.parentNode,o==t.dom)break;i=0}}}l?eo(t,o,i):s&&eo(t,s,a)}(r):function(t){let n=t.domSelectionRange(),o=n.focusNode,i=n.focusOffset;if(!o)return;let s,a,l=Qn(o);for(;;)if(i<l){if(o.nodeType!=1||!An(o.childNodes[i],1))break;s=o,a=++i}else{if(ts(o))break;{let c=o.nextSibling;for(;c&&An(c,1);)s=c.parentNode,a=be(c)+1,c=c.nextSibling;if(c)o=c,i=0,l=Qn(o);else{if(o=o.parentNode,o==t.dom)break;i=l=0}}}s&&eo(t,s,a)}(r)}function ts(r){let e=r.pmViewDesc;return e&&e.node&&e.node.isBlock}function eo(r,e,t){if(e.nodeType!=3){let i,s;(s=function(a,l){for(;a&&l==a.childNodes.length&&!Kn(a);)l=be(a)+1,a=a.parentNode;for(;a&&l<a.childNodes.length;){let c=a.childNodes[l];if(c.nodeType==3)return c;if(c.nodeType==1&&c.contentEditable=="false")break;a=c,l=0}}(e,t))?(e=s,t=0):(i=function(a,l){for(;a&&!l&&!Kn(a);)l=be(a),a=a.parentNode;for(;a&&l;){let c=a.childNodes[l-1];if(c.nodeType==3)return c;if(c.nodeType==1&&c.contentEditable=="false")break;l=(a=c).childNodes.length}}(e,t))&&(e=i,t=i.nodeValue.length)}let n=r.domSelection();if(Ir(n)){let i=document.createRange();i.setEnd(e,t),i.setStart(e,t),n.removeAllRanges(),n.addRange(i)}else n.extend&&n.extend(e,t);r.domObserver.setCurSelection();let{state:o}=r;setTimeout(()=>{r.state==o&&yt(r)},50)}function ns(r,e){let t=r.state.doc.resolve(e);if(!Oe&&!Oc&&t.parent.inlineContent){let n=r.coordsAtPos(e);if(e>t.start()){let o=r.coordsAtPos(e-1),i=(o.top+o.bottom)/2;if(i>n.top&&i<n.bottom&&Math.abs(o.left-n.left)>1)return o.left<n.left?"ltr":"rtl"}if(e<t.end()){let o=r.coordsAtPos(e+1),i=(o.top+o.bottom)/2;if(i>n.top&&i<n.bottom&&Math.abs(o.left-n.left)>1)return o.left>n.left?"ltr":"rtl"}}return getComputedStyle(r.dom).direction=="rtl"?"rtl":"ltr"}function rs(r,e,t){let n=r.state.selection;if(n instanceof K&&!n.empty||t.indexOf("s")>-1||Ke&&t.indexOf("m")>-1)return!1;let{$from:o,$to:i}=n;if(!o.parent.inlineContent||r.endOfTextblock(e<0?"up":"down")){let s=Io(r.state,e);if(s&&s instanceof V)return Ot(r,s)}if(!o.parent.inlineContent){let s=e<0?o:i,a=n instanceof We?J.near(s,e):J.findFrom(s,e);return!!a&&Ot(r,a)}return!1}function is(r,e){if(!(r.state.selection instanceof K))return!0;let{$head:t,$anchor:n,empty:o}=r.state.selection;if(!t.sameParent(n))return!0;if(!o)return!1;if(r.endOfTextblock(e>0?"forward":"backward"))return!0;let i=!t.textOffset&&(e<0?t.nodeBefore:t.nodeAfter);if(i&&!i.isText){let s=r.state.tr;return e<0?s.delete(t.pos-i.nodeSize,t.pos):s.delete(t.pos,t.pos+i.nodeSize),r.dispatch(s),!0}return!1}function ss(r,e,t){r.domObserver.stop(),e.contentEditable=t,r.domObserver.start()}function Fc(r,e){let t=e.keyCode,n=function(o){let i="";return o.ctrlKey&&(i+="c"),o.metaKey&&(i+="m"),o.altKey&&(i+="a"),o.shiftKey&&(i+="s"),i}(e);if(t==8||Ke&&t==72&&n=="c")return is(r,-1)||fn(r,-1);if(t==46&&!e.shiftKey||Ke&&t==68&&n=="c")return is(r,1)||fn(r,1);if(t==13||t==27)return!0;if(t==37||Ke&&t==66&&n=="c"){let o=t==37?ns(r,r.state.selection.from)=="ltr"?-1:1:-1;return es(r,o,n)||fn(r,o)}if(t==39||Ke&&t==70&&n=="c"){let o=t==39?ns(r,r.state.selection.from)=="ltr"?1:-1:1;return es(r,o,n)||fn(r,o)}return t==38||Ke&&t==80&&n=="c"?rs(r,-1,n)||fn(r,-1):t==40||Ke&&t==78&&n=="c"?function(o){if(!Le||o.state.selection.$head.parentOffset>0)return!1;let{focusNode:i,focusOffset:s}=o.domSelectionRange();if(i&&i.nodeType==1&&s==0&&i.firstChild&&i.firstChild.contentEditable=="false"){let a=i.firstChild;ss(o,a,"true"),setTimeout(()=>ss(o,a,"false"),20)}return!1}(r)||rs(r,1,n)||fn(r,1):n==(Ke?"m":"c")&&(t==66||t==73||t==89||t==90)}function Pa(r,e){r.someProp("transformCopied",p=>{e=p(e,r)});let t=[],{content:n,openStart:o,openEnd:i}=e;for(;o>1&&i>1&&n.childCount==1&&n.firstChild.childCount==1;){o--,i--;let p=n.firstChild;t.push(p.type.name,p.attrs!=p.type.defaultAttrs?p.attrs:null),n=p.content}let s=r.someProp("clipboardSerializer")||cn.fromSchema(r.state.schema),a=qa(),l=a.createElement("div");l.appendChild(s.serializeFragment(n,{document:a}));let c,h=l.firstChild,d=0;for(;h&&h.nodeType==1&&(c=Ha[h.nodeName.toLowerCase()]);){for(let p=c.length-1;p>=0;p--){let f=a.createElement(c[p]);for(;l.firstChild;)f.appendChild(l.firstChild);l.appendChild(f),d++}h=l.firstChild}return h&&h.nodeType==1&&h.setAttribute("data-pm-slice",`${o} ${i}${d?` -${d}`:""} ${JSON.stringify(t)}`),{dom:l,text:r.someProp("clipboardTextSerializer",p=>p(e,r))||e.content.textBetween(0,e.content.size,`

`),slice:e}}function za(r,e,t,n,o){let i,s,a=o.parent.type.spec.code;if(!t&&!e)return null;let l=e&&(n||a||!t);if(l){if(r.someProp("transformPastedText",p=>{e=p(e,a||n,r)}),a)return e?new D(S.from(r.state.schema.text(e.replace(/\r\n?/g,`
`))),0,0):D.empty;let d=r.someProp("clipboardTextParser",p=>p(e,o,n,r));if(d)s=d;else{let p=o.marks(),{schema:f}=r.state,u=cn.fromSchema(f);i=document.createElement("div"),e.split(/(?:\r\n?|\n)+/).forEach(m=>{let g=i.appendChild(document.createElement("p"));m&&g.appendChild(u.serializeNode(f.text(m,p)))})}}else r.someProp("transformPastedHTML",d=>{t=d(t,r)}),i=function(d){let p=/^(\s*<meta [^>]*>)*/.exec(d);p&&(d=d.slice(p[0].length));let f,u=qa().createElement("div"),m=/<([a-z][^>\s]+)/i.exec(d);if((f=m&&Ha[m[1].toLowerCase()])&&(d=f.map(g=>"<"+g+">").join("")+d+f.map(g=>"</"+g+">").reverse().join("")),u.innerHTML=d,f)for(let g=0;g<f.length;g++)u=u.querySelector(f[g])||u;return u}(t),Un&&function(d){let p=d.querySelectorAll(Oe?"span:not([class]):not([style])":"span.Apple-converted-space");for(let f=0;f<p.length;f++){let u=p[f];u.childNodes.length==1&&u.textContent==" "&&u.parentNode&&u.parentNode.replaceChild(d.ownerDocument.createTextNode(" "),u)}}(i);let c=i&&i.querySelector("[data-pm-slice]"),h=c&&/^(\d+) (\d+)(?: -(\d+))? (.*)/.exec(c.getAttribute("data-pm-slice")||"");if(h&&h[3])for(let d=+h[3];d>0;d--){let p=i.firstChild;for(;p&&p.nodeType!=1;)p=p.nextSibling;if(!p)break;i=p}if(s||(s=(r.someProp("clipboardParser")||r.someProp("domParser")||Sn.fromSchema(r.state.schema)).parseSlice(i,{preserveWhitespace:!(!l&&!h),context:o,ruleFromNode:p=>p.nodeName!="BR"||p.nextSibling||!p.parentNode||Bc.test(p.parentNode.nodeName)?null:{ignore:!0}})),h)s=function(d,p){if(!d.size)return d;let f,u=d.content.firstChild.type.schema;try{f=JSON.parse(p)}catch{return d}let{content:m,openStart:g,openEnd:y}=d;for(let C=f.length-2;C>=0;C-=2){let w=u.nodes[f[C]];if(!w||w.hasRequiredAttrs())break;m=S.from(w.create(f[C+1],m)),g++,y++}return new D(m,g,y)}(as(s,+h[1],+h[2]),h[4]);else if(s=D.maxOpen(function(d,p){if(d.childCount<2)return d;for(let f=p.depth;f>=0;f--){let u,m=p.node(f).contentMatchAt(p.index(f)),g=[];if(d.forEach(y=>{if(!g)return;let C,w=m.findWrapping(y.type);if(!w)return g=null;if(C=g.length&&u.length&&Ba(w,u,y,g[g.length-1],0))g[g.length-1]=C;else{g.length&&(g[g.length-1]=Va(g[g.length-1],u.length));let v=Fa(y,w);g.push(v),m=m.matchType(v.type),u=w}}),g)return S.from(g)}return d}(s.content,o),!0),s.openStart||s.openEnd){let d=0,p=0;for(let f=s.content.firstChild;d<s.openStart&&!f.type.spec.isolating;d++,f=f.firstChild);for(let f=s.content.lastChild;p<s.openEnd&&!f.type.spec.isolating;p++,f=f.lastChild);s=as(s,d,p)}return r.someProp("transformPasted",d=>{s=d(s,r)}),s}const Bc=/^(a|abbr|acronym|b|cite|code|del|em|i|ins|kbd|label|output|q|ruby|s|samp|span|strong|sub|sup|time|u|tt|var)$/i;function Fa(r,e,t=0){for(let n=e.length-1;n>=t;n--)r=e[n].create(null,S.from(r));return r}function Ba(r,e,t,n,o){if(o<r.length&&o<e.length&&r[o]==e[o]){let i=Ba(r,e,t,n.lastChild,o+1);if(i)return n.copy(n.content.replaceChild(n.childCount-1,i));if(n.contentMatchAt(n.childCount).matchType(o==r.length-1?t.type:r[o+1]))return n.copy(n.content.append(S.from(Fa(t,r,o+1))))}}function Va(r,e){if(e==0)return r;let t=r.content.replaceChild(r.childCount-1,Va(r.lastChild,e-1)),n=r.contentMatchAt(r.childCount).fillBefore(S.empty,!0);return r.copy(t.append(n))}function _o(r,e,t,n,o,i){let s=e<0?r.firstChild:r.lastChild,a=s.content;return r.childCount>1&&(i=0),o<n-1&&(a=_o(a,e,t,n,o+1,i)),o>=t&&(a=e<0?s.contentMatchAt(0).fillBefore(a,i<=o).append(a):a.append(s.contentMatchAt(s.childCount).fillBefore(S.empty,!0))),r.replaceChild(e<0?0:r.childCount-1,s.copy(a))}function as(r,e,t){return e<r.openStart&&(r=new D(_o(r.content,-1,e,r.openStart,0,r.openEnd),e,r.openEnd)),t<r.openEnd&&(r=new D(_o(r.content,1,t,r.openEnd,0,0),r.openStart,t)),r}const Ha={thead:["table"],tbody:["table"],tfoot:["table"],caption:["table"],colgroup:["table"],col:["table","colgroup"],tr:["table","tbody"],td:["table","tbody","tr"],th:["table","tbody","tr"]};let ls=null;function qa(){return ls||(ls=document.implementation.createHTMLDocument("title"))}const Ee={},Ne={},Vc={touchstart:!0,touchmove:!0};class Hc{constructor(){this.shiftKey=!1,this.mouseDown=null,this.lastKeyCode=null,this.lastKeyCodeTime=0,this.lastClick={time:0,x:0,y:0,type:""},this.lastSelectionOrigin=null,this.lastSelectionTime=0,this.lastIOSEnter=0,this.lastIOSEnterFallbackTimeout=-1,this.lastFocus=0,this.lastTouch=0,this.lastAndroidDelete=0,this.composing=!1,this.compositionNode=null,this.composingTimeout=-1,this.compositionNodes=[],this.compositionEndedAt=-2e8,this.compositionID=1,this.compositionPendingChanges=0,this.domChangeCount=0,this.eventHandlers=Object.create(null),this.hideSelectionGuard=null}}function _t(r,e){r.input.lastSelectionOrigin=e,r.input.lastSelectionTime=Date.now()}function to(r){r.someProp("handleDOMEvents",e=>{for(let t in e)r.input.eventHandlers[t]||r.dom.addEventListener(t,r.input.eventHandlers[t]=n=>$o(r,n))})}function $o(r,e){return r.someProp("handleDOMEvents",t=>{let n=t[e.type];return!!n&&(n(r,e)||e.defaultPrevented)})}function qc(r,e){if(!e.bubbles)return!0;if(e.defaultPrevented)return!1;for(let t=e.target;t!=r.dom;t=t.parentNode)if(!t||t.nodeType==11||t.pmViewDesc&&t.pmViewDesc.stopEvent(e))return!1;return!0}function wr(r){return{left:r.clientX,top:r.clientY}}function Qo(r,e,t,n,o){if(n==-1)return!1;let i=r.state.doc.resolve(n);for(let s=i.depth+1;s>0;s--)if(r.someProp(e,a=>s>i.depth?a(r,t,i.nodeAfter,i.before(s),o,!0):a(r,t,i.node(s),i.before(s),o,!1)))return!0;return!1}function kn(r,e,t){r.focused||r.focus();let n=r.state.tr.setSelection(e);n.setMeta("pointer",!0),r.dispatch(n)}function jc(r,e,t,n,o){return Qo(r,"handleClickOn",e,t,n)||r.someProp("handleClick",i=>i(r,e,n))||(o?function(i,s){if(s==-1)return!1;let a,l,c=i.state.selection;c instanceof V&&(a=c.node);let h=i.state.doc.resolve(s);for(let d=h.depth+1;d>0;d--){let p=d>h.depth?h.nodeAfter:h.node(d);if(V.isSelectable(p)){l=a&&c.$from.depth>0&&d>=c.$from.depth&&h.before(c.$from.depth+1)==c.$from.pos?h.before(c.$from.depth):h.before(d);break}}return l!=null&&(kn(i,V.create(i.state.doc,l)),!0)}(r,t):function(i,s){if(s==-1)return!1;let a=i.state.doc.resolve(s),l=a.nodeAfter;return!!(l&&l.isAtom&&V.isSelectable(l))&&(kn(i,new V(a)),!0)}(r,t))}function Kc(r,e,t,n){return Qo(r,"handleDoubleClickOn",e,t,n)||r.someProp("handleDoubleClick",o=>o(r,e,n))}function Jc(r,e,t,n){return Qo(r,"handleTripleClickOn",e,t,n)||r.someProp("handleTripleClick",o=>o(r,e,n))||function(o,i,s){if(s.button!=0)return!1;let a=o.state.doc;if(i==-1)return!!a.inlineContent&&(kn(o,K.create(a,0,a.content.size)),!0);let l=a.resolve(i);for(let c=l.depth+1;c>0;c--){let h=c>l.depth?l.nodeAfter:l.node(c),d=l.before(c);if(h.inlineContent)kn(o,K.create(a,d+1,d+1+h.content.size));else{if(!V.isSelectable(h))continue;kn(o,V.create(a,d))}return!0}}(r,t,n)}function Ro(r){return br(r)}Ne.keydown=(r,e)=>{let t=e;if(r.input.shiftKey=t.keyCode==16||t.shiftKey,!cs(r,t)&&(r.input.lastKeyCode=t.keyCode,r.input.lastKeyCodeTime=Date.now(),!Ze||!Oe||t.keyCode!=13))if(t.keyCode!=229&&r.domObserver.forceFlush(),!En||t.keyCode!=13||t.ctrlKey||t.altKey||t.metaKey)r.someProp("handleKeyDown",n=>n(r,t))||Fc(r,t)?t.preventDefault():_t(r,"key");else{let n=Date.now();r.input.lastIOSEnter=n,r.input.lastIOSEnterFallbackTimeout=setTimeout(()=>{r.input.lastIOSEnter==n&&(r.someProp("handleKeyDown",o=>o(r,qt(13,"Enter"))),r.input.lastIOSEnter=0)},200)}},Ne.keyup=(r,e)=>{e.keyCode==16&&(r.input.shiftKey=!1)},Ne.keypress=(r,e)=>{let t=e;if(cs(r,t)||!t.charCode||t.ctrlKey&&!t.altKey||Ke&&t.metaKey)return;if(r.someProp("handleKeyPress",o=>o(r,t)))return void t.preventDefault();let n=r.state.selection;if(!(n instanceof K&&n.$from.sameParent(n.$to))){let o=String.fromCharCode(t.charCode);/[\r\n]/.test(o)||r.someProp("handleTextInput",i=>i(r,n.$from.pos,n.$to.pos,o))||r.dispatch(r.state.tr.insertText(o).scrollIntoView()),t.preventDefault()}};const ja=Ke?"metaKey":"ctrlKey";Ee.mousedown=(r,e)=>{let t=e;r.input.shiftKey=t.shiftKey;let n=Ro(r),o=Date.now(),i="singleClick";o-r.input.lastClick.time<500&&function(a,l){let c=l.x-a.clientX,h=l.y-a.clientY;return c*c+h*h<100}(t,r.input.lastClick)&&!t[ja]&&(r.input.lastClick.type=="singleClick"?i="doubleClick":r.input.lastClick.type=="doubleClick"&&(i="tripleClick")),r.input.lastClick={time:o,x:t.clientX,y:t.clientY,type:i};let s=r.posAtCoords(wr(t));s&&(i=="singleClick"?(r.input.mouseDown&&r.input.mouseDown.done(),r.input.mouseDown=new Wc(r,s,t,!!n)):(i=="doubleClick"?Kc:Jc)(r,s.pos,s.inside,t)?t.preventDefault():_t(r,"pointer"))};class Wc{constructor(e,t,n,o){let i,s;if(this.view=e,this.pos=t,this.event=n,this.flushed=o,this.delayedSelectionSync=!1,this.mightDrag=null,this.startDoc=e.state.doc,this.selectNode=!!n[ja],this.allowDefault=n.shiftKey,t.inside>-1)i=e.state.doc.nodeAt(t.inside),s=t.inside;else{let h=e.state.doc.resolve(t.pos);i=h.parent,s=h.depth?h.before():0}const a=o?null:n.target,l=a?e.docView.nearestDesc(a,!0):null;this.target=l?l.dom:null;let{selection:c}=e.state;(n.button==0&&i.type.spec.draggable&&i.type.spec.selectable!==!1||c instanceof V&&c.from<=s&&c.to>s)&&(this.mightDrag={node:i,pos:s,addAttr:!(!this.target||this.target.draggable),setUneditable:!(!this.target||!tt||this.target.hasAttribute("contentEditable"))}),this.target&&this.mightDrag&&(this.mightDrag.addAttr||this.mightDrag.setUneditable)&&(this.view.domObserver.stop(),this.mightDrag.addAttr&&(this.target.draggable=!0),this.mightDrag.setUneditable&&setTimeout(()=>{this.view.input.mouseDown==this&&this.target.setAttribute("contentEditable","false")},20),this.view.domObserver.start()),e.root.addEventListener("mouseup",this.up=this.up.bind(this)),e.root.addEventListener("mousemove",this.move=this.move.bind(this)),_t(e,"pointer")}done(){this.view.root.removeEventListener("mouseup",this.up),this.view.root.removeEventListener("mousemove",this.move),this.mightDrag&&this.target&&(this.view.domObserver.stop(),this.mightDrag.addAttr&&this.target.removeAttribute("draggable"),this.mightDrag.setUneditable&&this.target.removeAttribute("contentEditable"),this.view.domObserver.start()),this.delayedSelectionSync&&setTimeout(()=>yt(this.view)),this.view.input.mouseDown=null}up(e){if(this.done(),!this.view.dom.contains(e.target))return;let t=this.pos;this.view.state.doc!=this.startDoc&&(t=this.view.posAtCoords(wr(e))),this.updateAllowDefault(e),this.allowDefault||!t?_t(this.view,"pointer"):jc(this.view,t.pos,t.inside,e,this.selectNode)?e.preventDefault():e.button==0&&(this.flushed||Le&&this.mightDrag&&!this.mightDrag.node.isAtom||Oe&&!this.view.state.selection.visible&&Math.min(Math.abs(t.pos-this.view.state.selection.from),Math.abs(t.pos-this.view.state.selection.to))<=2)?(kn(this.view,J.near(this.view.state.doc.resolve(t.pos))),e.preventDefault()):_t(this.view,"pointer")}move(e){this.updateAllowDefault(e),_t(this.view,"pointer"),e.buttons==0&&this.done()}updateAllowDefault(e){!this.allowDefault&&(Math.abs(this.event.x-e.clientX)>4||Math.abs(this.event.y-e.clientY)>4)&&(this.allowDefault=!0)}}function cs(r,e){return!!r.composing||!!(Le&&Math.abs(e.timeStamp-r.input.compositionEndedAt)<500)&&(r.input.compositionEndedAt=-2e8,!0)}Ee.touchstart=r=>{r.input.lastTouch=Date.now(),Ro(r),_t(r,"pointer")},Ee.touchmove=r=>{r.input.lastTouch=Date.now(),_t(r,"pointer")},Ee.contextmenu=r=>Ro(r);const Uc=Ze?5e3:-1;function hs(r,e){clearTimeout(r.input.composingTimeout),e>-1&&(r.input.composingTimeout=setTimeout(()=>br(r),e))}function Ka(r){for(r.composing&&(r.input.composing=!1,r.input.compositionEndedAt=function(){let e=document.createEvent("Event");return e.initEvent("event",!0,!0),e.timeStamp}());r.input.compositionNodes.length>0;)r.input.compositionNodes.pop().markParentsDirty()}function Gc(r){let e=r.domSelectionRange();if(!e.focusNode)return null;let t=function(o,i){for(;;){if(o.nodeType==3&&i)return o;if(o.nodeType==1&&i>0){if(o.contentEditable=="false")return null;i=at(o=o.childNodes[i-1])}else{if(!o.parentNode||Kn(o))return null;i=be(o),o=o.parentNode}}}(e.focusNode,e.focusOffset),n=function(o,i){for(;;){if(o.nodeType==3&&i<o.nodeValue.length)return o;if(o.nodeType==1&&i<o.childNodes.length){if(o.contentEditable=="false")return null;o=o.childNodes[i],i=0}else{if(!o.parentNode||Kn(o))return null;i=be(o)+1,o=o.parentNode}}}(e.focusNode,e.focusOffset);if(t&&n&&t!=n){let o=n.pmViewDesc;if(!o||!o.isText(n.nodeValue))return n;if(r.input.compositionNode==n){let i=t.pmViewDesc;if(i&&i.isText(t.nodeValue))return n}}return t||n}function br(r,e=!1){if(!(Ze&&r.domObserver.flushingSoon>=0)){if(r.domObserver.forceFlush(),Ka(r),e||r.docView&&r.docView.dirty){let t=Yo(r);return t&&!t.eq(r.state.selection)?r.dispatch(r.state.tr.setSelection(t)):r.updateState(r.state),!0}return!1}}Ne.compositionstart=Ne.compositionupdate=r=>{if(!r.composing){r.domObserver.flush();let{state:e}=r,t=e.selection.$from;if(e.selection.empty&&(e.storedMarks||!t.textOffset&&t.parentOffset&&t.nodeBefore.marks.some(n=>n.type.spec.inclusive===!1)))r.markCursor=r.state.storedMarks||t.marks(),br(r,!0),r.markCursor=null;else if(br(r),tt&&e.selection.empty&&t.parentOffset&&!t.textOffset&&t.nodeBefore.marks.length){let n=r.domSelectionRange();for(let o=n.focusNode,i=n.focusOffset;o&&o.nodeType==1&&i!=0;){let s=i<0?o.lastChild:o.childNodes[i-1];if(!s)break;if(s.nodeType==3){r.domSelection().collapse(s,s.nodeValue.length);break}o=s,i=-1}}r.input.composing=!0}hs(r,Uc)},Ne.compositionend=(r,e)=>{r.composing&&(r.input.composing=!1,r.input.compositionEndedAt=e.timeStamp,r.input.compositionPendingChanges=r.domObserver.pendingRecords().length?r.input.compositionID:0,r.input.compositionNode=null,r.input.compositionPendingChanges&&Promise.resolve().then(()=>r.domObserver.flush()),r.input.compositionID++,hs(r,20))};const zn=$e&&$t<15||En&&Ec<604;function Fn(r,e,t,n,o){let i=za(r,e,t,n,r.state.selection.$from);if(r.someProp("handlePaste",l=>l(r,o,i||D.empty)))return!0;if(!i)return!1;let s=function(l){return l.openStart==0&&l.openEnd==0&&l.content.childCount==1?l.content.firstChild:null}(i),a=s?r.state.tr.replaceSelectionWith(s,n):r.state.tr.replaceSelection(i);return r.dispatch(a.scrollIntoView().setMeta("paste",!0).setMeta("uiEvent","paste")),!0}function Ja(r){let e=r.getData("text/plain")||r.getData("Text");if(e)return e;let t=r.getData("text/uri-list");return t?t.replace(/\r?\n/g," "):""}Ee.copy=Ne.cut=(r,e)=>{let t=e,n=r.state.selection,o=t.type=="cut";if(n.empty)return;let i=zn?null:t.clipboardData,s=n.content(),{dom:a,text:l}=Pa(r,s);i?(t.preventDefault(),i.clearData(),i.setData("text/html",a.innerHTML),i.setData("text/plain",l)):function(c,h){if(!c.dom.parentNode)return;let d=c.dom.parentNode.appendChild(document.createElement("div"));d.appendChild(h),d.style.cssText="position: fixed; left: -10000px; top: 10px";let p=getSelection(),f=document.createRange();f.selectNodeContents(h),c.dom.blur(),p.removeAllRanges(),p.addRange(f),setTimeout(()=>{d.parentNode&&d.parentNode.removeChild(d),c.focus()},50)}(r,a),o&&r.dispatch(r.state.tr.deleteSelection().scrollIntoView().setMeta("uiEvent","cut"))},Ne.paste=(r,e)=>{let t=e;if(r.composing&&!Ze)return;let n=zn?null:t.clipboardData,o=r.input.shiftKey&&r.input.lastKeyCode!=45;n&&Fn(r,Ja(n),n.getData("text/html"),o,t)?t.preventDefault():function(i,s){if(!i.dom.parentNode)return;let a=i.input.shiftKey||i.state.selection.$from.parent.type.spec.code,l=i.dom.parentNode.appendChild(document.createElement(a?"textarea":"div"));a||(l.contentEditable="true"),l.style.cssText="position: fixed; left: -10000px; top: 10px",l.focus();let c=i.input.shiftKey&&i.input.lastKeyCode!=45;setTimeout(()=>{i.focus(),l.parentNode&&l.parentNode.removeChild(l),a?Fn(i,l.value,null,c,s):Fn(i,l.textContent,l.innerHTML,c,s)},50)}(r,t)};class Wa{constructor(e,t,n){this.slice=e,this.move=t,this.node=n}}const ds=Ke?"altKey":"ctrlKey";Ee.dragstart=(r,e)=>{let t=e,n=r.input.mouseDown;if(n&&n.done(),!t.dataTransfer)return;let o,i=r.state.selection,s=i.empty?null:r.posAtCoords(wr(t));if(!(s&&s.pos>=i.from&&s.pos<=(i instanceof V?i.to-1:i.to))){if(n&&n.mightDrag)o=V.create(r.state.doc,n.mightDrag.pos);else if(t.target&&t.target.nodeType==1){let d=r.docView.nearestDesc(t.target,!0);d&&d.node.type.spec.draggable&&d!=r.docView&&(o=V.create(r.state.doc,d.posBefore))}}let a=(o||r.state.selection).content(),{dom:l,text:c,slice:h}=Pa(r,a);t.dataTransfer.clearData(),t.dataTransfer.setData(zn?"Text":"text/html",l.innerHTML),t.dataTransfer.effectAllowed="copyMove",zn||t.dataTransfer.setData("text/plain",c),r.dragging=new Wa(h,!t[ds],o)},Ee.dragend=r=>{let e=r.dragging;window.setTimeout(()=>{r.dragging==e&&(r.dragging=null)},50)},Ne.dragover=Ne.dragenter=(r,e)=>e.preventDefault(),Ne.drop=(r,e)=>{let t=e,n=r.dragging;if(r.dragging=null,!t.dataTransfer)return;let o=r.posAtCoords(wr(t));if(!o)return;let i=r.state.doc.resolve(o.pos),s=n&&n.slice;s?r.someProp("transformPasted",u=>{s=u(s,r)}):s=za(r,Ja(t.dataTransfer),zn?null:t.dataTransfer.getData("text/html"),!1,i);let a=!(!n||t[ds]);if(r.someProp("handleDrop",u=>u(r,t,s||D.empty,a)))return void t.preventDefault();if(!s)return;t.preventDefault();let l=s?function(u,m,g){let y=u.resolve(m);if(!g.content.size)return m;let C=g.content;for(let w=0;w<g.openStart;w++)C=C.firstChild.content;for(let w=1;w<=(g.openStart==0&&g.size?2:1);w++)for(let v=y.depth;v>=0;v--){let x=v==y.depth?0:y.pos<=(y.start(v+1)+y.end(v+1))/2?-1:1,O=y.index(v)+(x>0?1:0),E=y.node(v),M=!1;if(w==1)M=E.canReplace(O,O,C);else{let k=E.contentMatchAt(O).findWrapping(C.firstChild.type);M=k&&E.canReplaceWith(O,O,k[0])}if(M)return x==0?y.pos:x<0?y.before(v+1):y.after(v+1)}return null}(r.state.doc,i.pos,s):i.pos;l==null&&(l=i.pos);let c=r.state.tr;if(a){let{node:u}=n;u?u.replace(c):c.deleteSelection()}let h=c.mapping.map(l),d=s.openStart==0&&s.openEnd==0&&s.content.childCount==1,p=c.doc;if(d?c.replaceRangeWith(h,h,s.content.firstChild):c.replaceRange(h,h,s),c.doc.eq(p))return;let f=c.doc.resolve(h);if(d&&V.isSelectable(s.content.firstChild)&&f.nodeAfter&&f.nodeAfter.sameMarkup(s.content.firstChild))c.setSelection(new V(f));else{let u=c.mapping.map(l);c.mapping.maps[c.mapping.maps.length-1].forEach((m,g,y,C)=>u=C),c.setSelection(Xo(r,f,c.doc.resolve(u)))}r.focus(),r.dispatch(c.setMeta("uiEvent","drop"))},Ee.focus=r=>{r.input.lastFocus=Date.now(),r.focused||(r.domObserver.stop(),r.dom.classList.add("ProseMirror-focused"),r.domObserver.start(),r.focused=!0,setTimeout(()=>{r.docView&&r.hasFocus()&&!r.domObserver.currentSelection.eq(r.domSelectionRange())&&yt(r)},20))},Ee.blur=(r,e)=>{let t=e;r.focused&&(r.domObserver.stop(),r.dom.classList.remove("ProseMirror-focused"),r.domObserver.start(),t.relatedTarget&&r.dom.contains(t.relatedTarget)&&r.domObserver.currentSelection.clear(),r.focused=!1)},Ee.beforeinput=(r,e)=>{if(Oe&&Ze&&e.inputType=="deleteContentBackward"){r.domObserver.flushSoon();let{domChangeCount:t}=r.input;setTimeout(()=>{if(r.input.domChangeCount!=t||(r.dom.blur(),r.focus(),r.someProp("handleKeyDown",o=>o(r,qt(8,"Backspace")))))return;let{$cursor:n}=r.state.selection;n&&n.pos>0&&r.dispatch(r.state.tr.delete(n.pos-1,n.pos).scrollIntoView())},50)}};for(let r in Ne)Ee[r]=Ne[r];function Jn(r,e){if(r==e)return!0;for(let t in r)if(r[t]!==e[t])return!1;for(let t in e)if(!(t in r))return!1;return!0}class xr{constructor(e,t){this.toDOM=e,this.spec=t||rn,this.side=this.spec.side||0}map(e,t,n,o){let{pos:i,deleted:s}=e.mapResult(t.from+o,this.side<0?-1:1);return s?null:new Be(i-n,i-n,this)}valid(){return!0}eq(e){return this==e||e instanceof xr&&(this.spec.key&&this.spec.key==e.spec.key||this.toDOM==e.toDOM&&Jn(this.spec,e.spec))}destroy(e){this.spec.destroy&&this.spec.destroy(e)}}class Pt{constructor(e,t){this.attrs=e,this.spec=t||rn}map(e,t,n,o){let i=e.map(t.from+o,this.spec.inclusiveStart?-1:1)-n,s=e.map(t.to+o,this.spec.inclusiveEnd?1:-1)-n;return i>=s?null:new Be(i,s,this)}valid(e,t){return t.from<t.to}eq(e){return this==e||e instanceof Pt&&Jn(this.attrs,e.attrs)&&Jn(this.spec,e.spec)}static is(e){return e.type instanceof Pt}destroy(){}}class ei{constructor(e,t){this.attrs=e,this.spec=t||rn}map(e,t,n,o){let i=e.mapResult(t.from+o,1);if(i.deleted)return null;let s=e.mapResult(t.to+o,-1);return s.deleted||s.pos<=i.pos?null:new Be(i.pos-n,s.pos-n,this)}valid(e,t){let n,{index:o,offset:i}=e.content.findIndex(t.from);return i==t.from&&!(n=e.child(o)).isText&&i+n.nodeSize==t.to}eq(e){return this==e||e instanceof ei&&Jn(this.attrs,e.attrs)&&Jn(this.spec,e.spec)}destroy(){}}class Be{constructor(e,t,n){this.from=e,this.to=t,this.type=n}copy(e,t){return new Be(e,t,this.type)}eq(e,t=0){return this.type.eq(e.type)&&this.from+t==e.from&&this.to+t==e.to}map(e,t,n){return this.type.map(e,this,t,n)}static widget(e,t,n){return new Be(e,e,new xr(t,n))}static inline(e,t,n,o){return new Be(e,t,new Pt(n,o))}static node(e,t,n,o){return new Be(e,t,new ei(n,o))}get spec(){return this.type.spec}get inline(){return this.type instanceof Pt}get widget(){return this.type instanceof xr}}const gn=[],rn={};class de{constructor(e,t){this.local=e.length?e:gn,this.children=t.length?t:gn}static create(e,t){return t.length?or(t,e,0,rn):xe}find(e,t,n){let o=[];return this.findInner(e??0,t??1e9,o,0,n),o}findInner(e,t,n,o,i){for(let s=0;s<this.local.length;s++){let a=this.local[s];a.from<=t&&a.to>=e&&(!i||i(a.spec))&&n.push(a.copy(a.from+o,a.to+o))}for(let s=0;s<this.children.length;s+=3)if(this.children[s]<t&&this.children[s+1]>e){let a=this.children[s]+1;this.children[s+2].findInner(e-a,t-a,n,o+a,i)}}map(e,t,n){return this==xe||e.maps.length==0?this:this.mapInner(e,t,0,0,n||rn)}mapInner(e,t,n,o,i){let s;for(let a=0;a<this.local.length;a++){let l=this.local[a].map(e,n,o);l&&l.type.valid(t,l)?(s||(s=[])).push(l):i.onRemove&&i.onRemove(this.local[a].spec)}return this.children.length?function(a,l,c,h,d,p,f){let u=a.slice();for(let g=0,y=p;g<c.maps.length;g++){let C=0;c.maps[g].forEach((w,v,x,O)=>{let E=O-x-(v-w);for(let M=0;M<u.length;M+=3){let k=u[M+1];if(k<0||w>k+y-C)continue;let N=u[M]+y-C;v>=N?u[M+1]=w<=N?-2:-1:w>=y&&E&&(u[M]+=E,u[M+1]+=E)}C+=E}),y=c.maps[g].map(y,-1)}let m=!1;for(let g=0;g<u.length;g+=3)if(u[g+1]<0){if(u[g+1]==-2){m=!0,u[g+1]=-1;continue}let y=c.map(a[g]+p),C=y-d;if(C<0||C>=h.content.size){m=!0;continue}let w=c.map(a[g+1]+p,-1)-d,{index:v,offset:x}=h.content.findIndex(C),O=h.maybeChild(v);if(O&&x==C&&x+O.nodeSize==w){let E=u[g+2].mapInner(c,O,y+1,a[g]+p+1,f);E!=xe?(u[g]=C,u[g+1]=w,u[g+2]=E):(u[g+1]=-2,m=!0)}else m=!0}if(m){let g=function(C,w,v,x,O,E,M){function k(N,I){for(let L=0;L<N.local.length;L++){let z=N.local[L].map(x,O,I);z?v.push(z):M.onRemove&&M.onRemove(N.local[L].spec)}for(let L=0;L<N.children.length;L+=3)k(N.children[L+2],N.children[L]+I+1)}for(let N=0;N<C.length;N+=3)C[N+1]==-1&&k(C[N+2],w[N]+E+1);return v}(u,a,l,c,d,p,f),y=or(g,h,0,f);l=y.local;for(let C=0;C<u.length;C+=3)u[C+1]<0&&(u.splice(C,3),C-=3);for(let C=0,w=0;C<y.children.length;C+=3){let v=y.children[C];for(;w<u.length&&u[w]<v;)w+=3;u.splice(w,0,y.children[C],y.children[C+1],y.children[C+2])}}return new de(l.sort(Gt),u)}(this.children,s||[],e,t,n,o,i):s?new de(s.sort(Gt),gn):xe}add(e,t){return t.length?this==xe?de.create(e,t):this.addInner(e,t,0):this}addInner(e,t,n){let o,i=0;e.forEach((a,l)=>{let c,h=l+n;if(c=Ga(t,a,h)){for(o||(o=this.children.slice());i<o.length&&o[i]<l;)i+=3;o[i]==l?o[i+2]=o[i+2].addInner(a,c,h+1):o.splice(i,0,l,l+a.nodeSize,or(c,a,h+1,rn)),i+=3}});let s=Ua(i?Za(t):t,-n);for(let a=0;a<s.length;a++)s[a].type.valid(e,s[a])||s.splice(a--,1);return new de(s.length?this.local.concat(s).sort(Gt):this.local,o||this.children)}remove(e){return e.length==0||this==xe?this:this.removeInner(e,0)}removeInner(e,t){let n=this.children,o=this.local;for(let i=0;i<n.length;i+=3){let s,a=n[i]+t,l=n[i+1]+t;for(let h,d=0;d<e.length;d++)(h=e[d])&&h.from>a&&h.to<l&&(e[d]=null,(s||(s=[])).push(h));if(!s)continue;n==this.children&&(n=this.children.slice());let c=n[i+2].removeInner(s,a+1);c!=xe?n[i+2]=c:(n.splice(i,3),i-=3)}if(o.length){for(let i,s=0;s<e.length;s++)if(i=e[s])for(let a=0;a<o.length;a++)o[a].eq(i,t)&&(o==this.local&&(o=this.local.slice()),o.splice(a--,1))}return n==this.children&&o==this.local?this:o.length||n.length?new de(o,n):xe}forChild(e,t){if(this==xe)return this;if(t.isLeaf)return de.empty;let n,o;for(let a=0;a<this.children.length;a+=3)if(this.children[a]>=e){this.children[a]==e&&(n=this.children[a+2]);break}let i=e+1,s=i+t.content.size;for(let a=0;a<this.local.length;a++){let l=this.local[a];if(l.from<s&&l.to>i&&l.type instanceof Pt){let c=Math.max(i,l.from)-i,h=Math.min(s,l.to)-i;c<h&&(o||(o=[])).push(l.copy(c,h))}}if(o){let a=new de(o.sort(Gt),gn);return n?new At([a,n]):a}return n||xe}eq(e){if(this==e)return!0;if(!(e instanceof de)||this.local.length!=e.local.length||this.children.length!=e.children.length)return!1;for(let t=0;t<this.local.length;t++)if(!this.local[t].eq(e.local[t]))return!1;for(let t=0;t<this.children.length;t+=3)if(this.children[t]!=e.children[t]||this.children[t+1]!=e.children[t+1]||!this.children[t+2].eq(e.children[t+2]))return!1;return!0}locals(e){return ti(this.localsInner(e))}localsInner(e){if(this==xe)return gn;if(e.inlineContent||!this.local.some(Pt.is))return this.local;let t=[];for(let n=0;n<this.local.length;n++)this.local[n].type instanceof Pt||t.push(this.local[n]);return t}}de.empty=new de([],[]),de.removeOverlap=ti;const xe=de.empty;class At{constructor(e){this.members=e}map(e,t){const n=this.members.map(o=>o.map(e,t,rn));return At.from(n)}forChild(e,t){if(t.isLeaf)return de.empty;let n=[];for(let o=0;o<this.members.length;o++){let i=this.members[o].forChild(e,t);i!=xe&&(i instanceof At?n=n.concat(i.members):n.push(i))}return At.from(n)}eq(e){if(!(e instanceof At)||e.members.length!=this.members.length)return!1;for(let t=0;t<this.members.length;t++)if(!this.members[t].eq(e.members[t]))return!1;return!0}locals(e){let t,n=!0;for(let o=0;o<this.members.length;o++){let i=this.members[o].localsInner(e);if(i.length)if(t){n&&(t=t.slice(),n=!1);for(let s=0;s<i.length;s++)t.push(i[s])}else t=i}return t?ti(n?t:t.sort(Gt)):gn}static from(e){switch(e.length){case 0:return xe;case 1:return e[0];default:return new At(e.every(t=>t instanceof de)?e:e.reduce((t,n)=>t.concat(n instanceof de?n:n.members),[]))}}}function Ua(r,e){if(!e||!r.length)return r;let t=[];for(let n=0;n<r.length;n++){let o=r[n];t.push(new Be(o.from+e,o.to+e,o.type))}return t}function Ga(r,e,t){if(e.isLeaf)return null;let n=t+e.nodeSize,o=null;for(let i,s=0;s<r.length;s++)(i=r[s])&&i.from>t&&i.to<n&&((o||(o=[])).push(i),r[s]=null);return o}function Za(r){let e=[];for(let t=0;t<r.length;t++)r[t]!=null&&e.push(r[t]);return e}function or(r,e,t,n){let o=[],i=!1;e.forEach((a,l)=>{let c=Ga(r,a,l+t);if(c){i=!0;let h=or(c,a,t+l+1,n);h!=xe&&o.push(l,l+a.nodeSize,h)}});let s=Ua(i?Za(r):r,-t).sort(Gt);for(let a=0;a<s.length;a++)s[a].type.valid(e,s[a])||(n.onRemove&&n.onRemove(s[a].spec),s.splice(a--,1));return s.length||o.length?new de(s,o):xe}function Gt(r,e){return r.from-e.from||r.to-e.to}function ti(r){let e=r;for(let t=0;t<e.length-1;t++){let n=e[t];if(n.from!=n.to)for(let o=t+1;o<e.length;o++){let i=e[o];if(i.from!=n.from){i.from<n.to&&(e==r&&(e=r.slice()),e[t]=n.copy(n.from,i.from),ps(e,o,n.copy(i.from,n.to)));break}i.to!=n.to&&(e==r&&(e=r.slice()),e[o]=i.copy(i.from,n.to),ps(e,o+1,i.copy(n.to,i.to)))}}return e}function ps(r,e,t){for(;e<r.length&&Gt(t,r[e])>0;)e++;r.splice(e,0,t)}function no(r){let e=[];return r.someProp("decorations",t=>{let n=t(r.state);n&&n!=xe&&e.push(n)}),r.cursorWrapper&&e.push(de.create(r.state.doc,[r.cursorWrapper.deco])),At.from(e)}const Zc={childList:!0,characterData:!0,characterDataOldValue:!0,attributes:!0,attributeOldValue:!0,subtree:!0},Yc=$e&&$t<=11;class Xc{constructor(){this.anchorNode=null,this.anchorOffset=0,this.focusNode=null,this.focusOffset=0}set(e){this.anchorNode=e.anchorNode,this.anchorOffset=e.anchorOffset,this.focusNode=e.focusNode,this.focusOffset=e.focusOffset}clear(){this.anchorNode=this.focusNode=null}eq(e){return e.anchorNode==this.anchorNode&&e.anchorOffset==this.anchorOffset&&e.focusNode==this.focusNode&&e.focusOffset==this.focusOffset}}class Qc{constructor(e,t){this.view=e,this.handleDOMChange=t,this.queue=[],this.flushingSoon=-1,this.observer=null,this.currentSelection=new Xc,this.onCharData=null,this.suppressingSelectionUpdates=!1,this.observer=window.MutationObserver&&new window.MutationObserver(n=>{for(let o=0;o<n.length;o++)this.queue.push(n[o]);$e&&$t<=11&&n.some(o=>o.type=="childList"&&o.removedNodes.length||o.type=="characterData"&&o.oldValue.length>o.target.nodeValue.length)?this.flushSoon():this.flush()}),Yc&&(this.onCharData=n=>{this.queue.push({target:n.target,type:"characterData",oldValue:n.prevValue}),this.flushSoon()}),this.onSelectionChange=this.onSelectionChange.bind(this)}flushSoon(){this.flushingSoon<0&&(this.flushingSoon=window.setTimeout(()=>{this.flushingSoon=-1,this.flush()},20))}forceFlush(){this.flushingSoon>-1&&(window.clearTimeout(this.flushingSoon),this.flushingSoon=-1,this.flush())}start(){this.observer&&(this.observer.takeRecords(),this.observer.observe(this.view.dom,Zc)),this.onCharData&&this.view.dom.addEventListener("DOMCharacterDataModified",this.onCharData),this.connectSelection()}stop(){if(this.observer){let e=this.observer.takeRecords();if(e.length){for(let t=0;t<e.length;t++)this.queue.push(e[t]);window.setTimeout(()=>this.flush(),20)}this.observer.disconnect()}this.onCharData&&this.view.dom.removeEventListener("DOMCharacterDataModified",this.onCharData),this.disconnectSelection()}connectSelection(){this.view.dom.ownerDocument.addEventListener("selectionchange",this.onSelectionChange)}disconnectSelection(){this.view.dom.ownerDocument.removeEventListener("selectionchange",this.onSelectionChange)}suppressSelectionUpdates(){this.suppressingSelectionUpdates=!0,setTimeout(()=>this.suppressingSelectionUpdates=!1,50)}onSelectionChange(){if(Qi(this.view)){if(this.suppressingSelectionUpdates)return yt(this.view);if($e&&$t<=11&&!this.view.state.selection.empty){let e=this.view.domSelectionRange();if(e.focusNode&&ln(e.focusNode,e.focusOffset,e.anchorNode,e.anchorOffset))return this.flushSoon()}this.flush()}}setCurSelection(){this.currentSelection.set(this.view.domSelectionRange())}ignoreSelectionChange(e){if(!e.focusNode)return!0;let t,n=new Set;for(let i=e.focusNode;i;i=jn(i))n.add(i);for(let i=e.anchorNode;i;i=jn(i))if(n.has(i)){t=i;break}let o=t&&this.view.docView.nearestDesc(t);return o&&o.ignoreMutation({type:"selection",target:t.nodeType==3?t.parentNode:t})?(this.setCurSelection(),!0):void 0}pendingRecords(){if(this.observer)for(let e of this.observer.takeRecords())this.queue.push(e);return this.queue}flush(){let{view:e}=this;if(!e.docView||this.flushingSoon>-1)return;let t=this.pendingRecords();t.length&&(this.queue=[]);let n=e.domSelectionRange(),o=!this.suppressingSelectionUpdates&&!this.currentSelection.eq(n)&&Qi(e)&&!this.ignoreSelectionChange(n),i=-1,s=-1,a=!1,l=[];if(e.editable)for(let h=0;h<t.length;h++){let d=this.registerMutation(t[h],l);d&&(i=i<0?d.from:Math.min(d.from,i),s=s<0?d.to:Math.max(d.to,s),d.typeOver&&(a=!0))}if(tt&&l.length>1){let h=l.filter(d=>d.nodeName=="BR");if(h.length==2){let d=h[0],p=h[1];d.parentNode&&d.parentNode.parentNode==p.parentNode?p.remove():d.remove()}}let c=null;i<0&&o&&e.input.lastFocus>Date.now()-200&&Math.max(e.input.lastTouch,e.input.lastClick.time)<Date.now()-300&&Ir(n)&&(c=Yo(e))&&c.eq(J.near(e.state.doc.resolve(0),1))?(e.input.lastFocus=0,yt(e),this.currentSelection.set(n),e.scrollToSelection()):(i>-1||o)&&(i>-1&&(e.docView.markDirty(i,s),function(h){if(!us.has(h)&&(us.set(h,null),["normal","nowrap","pre-line"].indexOf(getComputedStyle(h.dom).whiteSpace)!==-1)){if(h.requiresGeckoHackNode=tt,fs)return;console.warn("ProseMirror expects the CSS white-space property to be set, preferably to 'pre-wrap'. It is recommended to load style/prosemirror.css from the prosemirror-view package."),fs=!0}}(e)),this.handleDOMChange(i,s,a,l),e.docView&&e.docView.dirty?e.updateState(e.state):this.currentSelection.eq(n)||yt(e),this.currentSelection.set(n))}registerMutation(e,t){if(t.indexOf(e.target)>-1)return null;let n=this.view.docView.nearestDesc(e.target);if(e.type=="attributes"&&(n==this.view.docView||e.attributeName=="contenteditable"||e.attributeName=="style"&&!e.oldValue&&!e.target.getAttribute("style"))||!n||n.ignoreMutation(e))return null;if(e.type=="childList"){for(let c=0;c<e.addedNodes.length;c++)t.push(e.addedNodes[c]);if(n.contentDOM&&n.contentDOM!=n.dom&&!n.contentDOM.contains(e.target))return{from:n.posBefore,to:n.posAfter};let o=e.previousSibling,i=e.nextSibling;if($e&&$t<=11&&e.addedNodes.length)for(let c=0;c<e.addedNodes.length;c++){let{previousSibling:h,nextSibling:d}=e.addedNodes[c];(!h||Array.prototype.indexOf.call(e.addedNodes,h)<0)&&(o=h),(!d||Array.prototype.indexOf.call(e.addedNodes,d)<0)&&(i=d)}let s=o&&o.parentNode==e.target?be(o)+1:0,a=n.localPosFromDOM(e.target,s,-1),l=i&&i.parentNode==e.target?be(i):e.target.childNodes.length;return{from:a,to:n.localPosFromDOM(e.target,l,1)}}return e.type=="attributes"?{from:n.posAtStart-n.border,to:n.posAtEnd+n.border}:{from:n.posAtStart,to:n.posAtEnd,typeOver:e.target.nodeValue==e.oldValue}}}let us=new WeakMap,fs=!1;function ms(r,e){let t=e.startContainer,n=e.startOffset,o=e.endContainer,i=e.endOffset,s=r.domAtPos(r.state.selection.anchor);return ln(s.node,s.offset,o,i)&&([t,n,o,i]=[o,i,t,n]),{anchorNode:t,anchorOffset:n,focusNode:o,focusOffset:i}}function eh(r){let e=r.pmViewDesc;if(e)return e.parseRule();if(r.nodeName=="BR"&&r.parentNode){if(Le&&/^(ul|ol)$/i.test(r.parentNode.nodeName)){let t=document.createElement("div");return t.appendChild(document.createElement("li")),{skip:t}}if(r.parentNode.lastChild==r||Le&&/^(tr|table)$/i.test(r.parentNode.nodeName))return{ignore:!0}}else if(r.nodeName=="IMG"&&r.getAttribute("mark-placeholder"))return{ignore:!0};return null}const th=/^(a|abbr|acronym|b|bd[io]|big|br|button|cite|code|data(list)?|del|dfn|em|i|ins|kbd|label|map|mark|meter|output|q|ruby|s|samp|small|span|strong|su[bp]|time|u|tt|var)$/i;function nh(r,e,t,n,o){let i=r.input.compositionPendingChanges||(r.composing?r.input.compositionID:0);if(r.input.compositionPendingChanges=0,e<0){let k=r.input.lastSelectionTime>Date.now()-50?r.input.lastSelectionOrigin:null,N=Yo(r,k);if(N&&!r.state.selection.eq(N)){if(Oe&&Ze&&r.input.lastKeyCode===13&&Date.now()-100<r.input.lastKeyCodeTime&&r.someProp("handleKeyDown",L=>L(r,qt(13,"Enter"))))return;let I=r.state.tr.setSelection(N);k=="pointer"?I.setMeta("pointer",!0):k=="key"&&I.scrollIntoView(),i&&I.setMeta("composition",i),r.dispatch(I)}return}let s=r.state.doc.resolve(e),a=s.sharedDepth(t);e=s.before(a+1),t=r.state.doc.resolve(t).after(a+1);let l,c,h=r.state.selection,d=function(k,N,I){let L,{node:z,fromOffset:A,toOffset:F,from:_,to:H}=k.docView.parseRange(N,I),G=k.domSelectionRange(),B=G.anchorNode;if(B&&k.dom.contains(B.nodeType==1?B:B.parentNode)&&(L=[{node:B,offset:G.anchorOffset}],Ir(G)||L.push({node:G.focusNode,offset:G.focusOffset})),Oe&&k.input.lastKeyCode===8)for(let Z=F;Z>A;Z--){let ue=z.childNodes[Z-1],X=ue.pmViewDesc;if(ue.nodeName=="BR"&&!X){F=Z;break}if(!X||X.size)break}let ee=k.state.doc,re=k.someProp("domParser")||Sn.fromSchema(k.state.schema),ae=ee.resolve(_),te=null,se=re.parse(z,{topNode:ae.parent,topMatch:ae.parent.contentMatchAt(ae.index()),topOpen:!0,from:A,to:F,preserveWhitespace:ae.parent.type.whitespace!="pre"||"full",findPositions:L,ruleFromNode:eh,context:ae});if(L&&L[0].pos!=null){let Z=L[0].pos,ue=L[1]&&L[1].pos;ue==null&&(ue=Z),te={anchor:Z+_,head:ue+_}}return{doc:se,sel:te,from:_,to:H}}(r,e,t),p=r.state.doc,f=p.slice(d.from,d.to);r.input.lastKeyCode===8&&Date.now()-100<r.input.lastKeyCodeTime?(l=r.state.selection.to,c="end"):(l=r.state.selection.from,c="start"),r.input.lastKeyCode=null;let u=function(k,N,I,L,z){let A=k.findDiffStart(N,I);if(A==null)return null;let{a:F,b:_}=k.findDiffEnd(N,I+k.size,I+N.size);if(z=="end"&&(L-=F+Math.max(0,A-Math.min(F,_))-A),F<A&&k.size<N.size){let H=L<=A&&L>=F?A-L:0;A-=H,A&&A<N.size&&ys(N.textBetween(A-1,A+1))&&(A+=H?1:-1),_=A+(_-F),F=A}else if(_<A){let H=L<=A&&L>=_?A-L:0;A-=H,A&&A<k.size&&ys(k.textBetween(A-1,A+1))&&(A+=H?1:-1),F=A+(F-_),_=A}return{start:A,endA:F,endB:_}}(f.content,d.doc.content,d.from,l,c);if((En&&r.input.lastIOSEnter>Date.now()-225||Ze)&&o.some(k=>k.nodeType==1&&!th.test(k.nodeName))&&(!u||u.endA>=u.endB)&&r.someProp("handleKeyDown",k=>k(r,qt(13,"Enter"))))return void(r.input.lastIOSEnter=0);if(!u){if(!(n&&h instanceof K&&!h.empty&&h.$head.sameParent(h.$anchor))||r.composing||d.sel&&d.sel.anchor!=d.sel.head){if(d.sel){let k=gs(r,r.state.doc,d.sel);if(k&&!k.eq(r.state.selection)){let N=r.state.tr.setSelection(k);i&&N.setMeta("composition",i),r.dispatch(N)}}return}u={start:h.from,endA:h.to,endB:h.to}}r.input.domChangeCount++,r.state.selection.from<r.state.selection.to&&u.start==u.endB&&r.state.selection instanceof K&&(u.start>r.state.selection.from&&u.start<=r.state.selection.from+2&&r.state.selection.from>=d.from?u.start=r.state.selection.from:u.endA<r.state.selection.to&&u.endA>=r.state.selection.to-2&&r.state.selection.to<=d.to&&(u.endB+=r.state.selection.to-u.endA,u.endA=r.state.selection.to)),$e&&$t<=11&&u.endB==u.start+1&&u.endA==u.start&&u.start>d.from&&d.doc.textBetween(u.start-d.from-1,u.start-d.from+1)=="  "&&(u.start--,u.endA--,u.endB--);let m,g=d.doc.resolveNoCache(u.start-d.from),y=d.doc.resolveNoCache(u.endB-d.from),C=p.resolve(u.start),w=g.sameParent(y)&&g.parent.inlineContent&&C.end()>=u.endA;if((En&&r.input.lastIOSEnter>Date.now()-225&&(!w||o.some(k=>k.nodeName=="DIV"||k.nodeName=="P"))||!w&&g.pos<d.doc.content.size&&!g.sameParent(y)&&(m=J.findFrom(d.doc.resolve(g.pos+1),1,!0))&&m.head==y.pos)&&r.someProp("handleKeyDown",k=>k(r,qt(13,"Enter"))))return void(r.input.lastIOSEnter=0);if(r.state.selection.anchor>u.start&&function(k,N,I,L,z){if(I-N<=z.pos-L.pos||ro(L,!0,!1)<z.pos)return!1;let A=k.resolve(N);if(!L.parent.isTextblock){let _=A.nodeAfter;return _!=null&&I==N+_.nodeSize}if(A.parentOffset<A.parent.content.size||!A.parent.isTextblock)return!1;let F=k.resolve(ro(A,!0,!0));return!(!F.parent.isTextblock||F.pos>I||ro(F,!0,!1)<I)&&L.parent.content.cut(L.parentOffset).eq(F.parent.content)}(p,u.start,u.endA,g,y)&&r.someProp("handleKeyDown",k=>k(r,qt(8,"Backspace"))))return void(Ze&&Oe&&r.domObserver.suppressSelectionUpdates());Oe&&Ze&&u.endB==u.start&&(r.input.lastAndroidDelete=Date.now()),Ze&&!w&&g.start()!=y.start()&&y.parentOffset==0&&g.depth==y.depth&&d.sel&&d.sel.anchor==d.sel.head&&d.sel.head==u.endA&&(u.endB-=2,y=d.doc.resolveNoCache(u.endB-d.from),setTimeout(()=>{r.someProp("handleKeyDown",function(k){return k(r,qt(13,"Enter"))})},20));let v,x,O,E=u.start,M=u.endA;if(w){if(g.pos==y.pos)$e&&$t<=11&&g.parentOffset==0&&(r.domObserver.suppressSelectionUpdates(),setTimeout(()=>yt(r),20)),v=r.state.tr.delete(E,M),x=p.resolve(u.start).marksAcross(p.resolve(u.endA));else if(u.endA==u.endB&&(O=function(k,N){let I,L,z,A=k.firstChild.marks,F=N.firstChild.marks,_=A,H=F;for(let B=0;B<F.length;B++)_=F[B].removeFromSet(_);for(let B=0;B<A.length;B++)H=A[B].removeFromSet(H);if(_.length==1&&H.length==0)L=_[0],I="add",z=B=>B.mark(L.addToSet(B.marks));else{if(_.length!=0||H.length!=1)return null;L=H[0],I="remove",z=B=>B.mark(L.removeFromSet(B.marks))}let G=[];for(let B=0;B<N.childCount;B++)G.push(z(N.child(B)));if(S.from(G).eq(k))return{mark:L,type:I}}(g.parent.content.cut(g.parentOffset,y.parentOffset),C.parent.content.cut(C.parentOffset,u.endA-C.start()))))v=r.state.tr,O.type=="add"?v.addMark(E,M,O.mark):v.removeMark(E,M,O.mark);else if(g.parent.child(g.index()).isText&&g.index()==y.index()-(y.textOffset?0:1)){let k=g.parent.textBetween(g.parentOffset,y.parentOffset);if(r.someProp("handleTextInput",N=>N(r,E,M,k)))return;v=r.state.tr.insertText(k,E,M)}}if(v||(v=r.state.tr.replace(E,M,d.doc.slice(u.start-d.from,u.endB-d.from))),d.sel){let k=gs(r,v.doc,d.sel);k&&!(Oe&&Ze&&r.composing&&k.empty&&(u.start!=u.endB||r.input.lastAndroidDelete<Date.now()-100)&&(k.head==E||k.head==v.mapping.map(M)-1)||$e&&k.empty&&k.head==E)&&v.setSelection(k)}x&&v.ensureMarks(x),i&&v.setMeta("composition",i),r.dispatch(v.scrollIntoView())}function gs(r,e,t){return Math.max(t.anchor,t.head)>e.content.size?null:Xo(r,e.resolve(t.anchor),e.resolve(t.head))}function ro(r,e,t){let n=r.depth,o=e?r.end():r.pos;for(;n>0&&(e||r.indexAfter(n)==r.node(n).childCount);)n--,o++,e=!1;if(t){let i=r.node(n).maybeChild(r.indexAfter(n));for(;i&&!i.isLeaf;)i=i.firstChild,o++}return o}function ys(r){if(r.length!=2)return!1;let e=r.charCodeAt(0),t=r.charCodeAt(1);return e>=56320&&e<=57343&&t>=55296&&t<=56319}class rh{constructor(e,t){this._root=null,this.focused=!1,this.trackWrites=null,this.mounted=!1,this.markCursor=null,this.cursorWrapper=null,this.lastSelectedViewDesc=void 0,this.input=new Hc,this.prevDirectPlugins=[],this.pluginViews=[],this.requiresGeckoHackNode=!1,this.dragging=null,this._props=t,this.state=t.state,this.directPlugins=t.plugins||[],this.directPlugins.forEach(xs),this.dispatch=this.dispatch.bind(this),this.dom=e&&e.mount||document.createElement("div"),e&&(e.appendChild?e.appendChild(this.dom):typeof e=="function"?e(this.dom):e.mount&&(this.mounted=!0)),this.editable=ws(this),vs(this),this.nodeViews=bs(this),this.docView=Ji(this.state.doc,Cs(this),no(this),this.dom,this),this.domObserver=new Qc(this,(n,o,i,s)=>nh(this,n,o,i,s)),this.domObserver.start(),function(n){for(let o in Ee){let i=Ee[o];n.dom.addEventListener(o,n.input.eventHandlers[o]=s=>{!qc(n,s)||$o(n,s)||!n.editable&&s.type in Ne||i(n,s)},Vc[o]?{passive:!0}:void 0)}Le&&n.dom.addEventListener("input",()=>null),to(n)}(this),this.updatePluginViews()}get composing(){return this.input.composing}get props(){if(this._props.state!=this.state){let e=this._props;this._props={};for(let t in e)this._props[t]=e[t];this._props.state=this.state}return this._props}update(e){e.handleDOMEvents!=this._props.handleDOMEvents&&to(this);let t=this._props;this._props=e,e.plugins&&(e.plugins.forEach(xs),this.directPlugins=e.plugins),this.updateStateInner(e.state,t)}setProps(e){let t={};for(let n in this._props)t[n]=this._props[n];t.state=this.state;for(let n in e)t[n]=e[n];this.update(t)}updateState(e){this.updateStateInner(e,this._props)}updateStateInner(e,t){var n;let o=this.state,i=!1,s=!1;e.storedMarks&&this.composing&&(Ka(this),s=!0),this.state=e;let a=o.plugins!=e.plugins||this._props.plugins!=t.plugins;if(a||this._props.plugins!=t.plugins||this._props.nodeViews!=t.nodeViews){let f=bs(this);(function(u,m){let g=0,y=0;for(let C in u){if(u[C]!=m[C])return!0;g++}for(let C in m)y++;return g!=y})(f,this.nodeViews)&&(this.nodeViews=f,i=!0)}(a||t.handleDOMEvents!=this._props.handleDOMEvents)&&to(this),this.editable=ws(this),vs(this);let l=no(this),c=Cs(this),h=o.plugins==e.plugins||o.doc.eq(e.doc)?e.scrollToSelection>o.scrollToSelection?"to selection":"preserve":"reset",d=i||!this.docView.matchesNode(e.doc,c,l);!d&&e.selection.eq(o.selection)||(s=!0);let p=h=="preserve"&&s&&this.dom.style.overflowAnchor==null&&function(f){let u,m,g=f.dom.getBoundingClientRect(),y=Math.max(0,g.top);for(let C=(g.left+g.right)/2,w=y+1;w<Math.min(innerHeight,g.bottom);w+=5){let v=f.root.elementFromPoint(C,w);if(!v||v==f.dom||!f.dom.contains(v))continue;let x=v.getBoundingClientRect();if(x.top>=y-20){u=v,m=x.top;break}}return{refDOM:u,refTop:m,stack:Fi(f.dom)}}(this);if(s){this.domObserver.stop();let f=d&&($e||Oe)&&!this.composing&&!o.selection.empty&&!e.selection.empty&&function(u,m){let g=Math.min(u.$anchor.sharedDepth(u.head),m.$anchor.sharedDepth(m.head));return u.$anchor.start(g)!=m.$anchor.start(g)}(o.selection,e.selection);if(d){let u=Oe?this.trackWrites=this.domSelectionRange().focusNode:null;this.composing&&(this.input.compositionNode=Gc(this)),!i&&this.docView.update(e.doc,c,l,this)||(this.docView.updateOuterDeco(c),this.docView.destroy(),this.docView=Ji(e.doc,c,l,this.dom,this)),u&&!this.trackWrites&&(f=!0)}f||!(this.input.mouseDown&&this.domObserver.currentSelection.eq(this.domSelectionRange())&&function(u){let m=u.docView.domFromPos(u.state.selection.anchor,0),g=u.domSelectionRange();return ln(m.node,m.offset,g.anchorNode,g.anchorOffset)}(this))?yt(this,f):($a(this,e.selection),this.domObserver.setCurSelection()),this.domObserver.start()}this.updatePluginViews(o),!((n=this.dragging)===null||n===void 0)&&n.node&&!o.doc.eq(e.doc)&&this.updateDraggedNode(this.dragging,o),h=="reset"?this.dom.scrollTop=0:h=="to selection"?this.scrollToSelection():p&&function({refDOM:f,refTop:u,stack:m}){let g=f?f.getBoundingClientRect().top:0;Bi(m,g==0?0:g-u)}(p)}scrollToSelection(){let e=this.domSelectionRange().focusNode;if(!this.someProp("handleScrollToSelection",t=>t(this)))if(this.state.selection instanceof V){let t=this.docView.domAfterPos(this.state.selection.from);t.nodeType==1&&zi(this,t.getBoundingClientRect(),e)}else zi(this,this.coordsAtPos(this.state.selection.head,1),e)}destroyPluginViews(){let e;for(;e=this.pluginViews.pop();)e.destroy&&e.destroy()}updatePluginViews(e){if(e&&e.plugins==this.state.plugins&&this.directPlugins==this.prevDirectPlugins)for(let t=0;t<this.pluginViews.length;t++){let n=this.pluginViews[t];n.update&&n.update(this,e)}else{this.prevDirectPlugins=this.directPlugins,this.destroyPluginViews();for(let t=0;t<this.directPlugins.length;t++){let n=this.directPlugins[t];n.spec.view&&this.pluginViews.push(n.spec.view(this))}for(let t=0;t<this.state.plugins.length;t++){let n=this.state.plugins[t];n.spec.view&&this.pluginViews.push(n.spec.view(this))}}}updateDraggedNode(e,t){let n=e.node,o=-1;if(this.state.doc.nodeAt(n.from)==n.node)o=n.from;else{let i=n.from+(this.state.doc.content.size-t.doc.content.size);(i>0&&this.state.doc.nodeAt(i))==n.node&&(o=i)}this.dragging=new Wa(e.slice,e.move,o<0?void 0:V.create(this.state.doc,o))}someProp(e,t){let n,o=this._props&&this._props[e];if(o!=null&&(n=t?t(o):o))return n;for(let s=0;s<this.directPlugins.length;s++){let a=this.directPlugins[s].props[e];if(a!=null&&(n=t?t(a):a))return n}let i=this.state.plugins;if(i)for(let s=0;s<i.length;s++){let a=i[s].props[e];if(a!=null&&(n=t?t(a):a))return n}}hasFocus(){if($e){let e=this.root.activeElement;if(e==this.dom)return!0;if(!e||!this.dom.contains(e))return!1;for(;e&&this.dom!=e&&this.dom.contains(e);){if(e.contentEditable=="false")return!1;e=e.parentElement}return!0}return this.root.activeElement==this.dom}focus(){this.domObserver.stop(),this.editable&&function(e){if(e.setActive)return e.setActive();if(un)return e.focus(un);let t=Fi(e);e.focus(un==null?{get preventScroll(){return un={preventScroll:!0},!0}}:void 0),un||(un=!1,Bi(t,0))}(this.dom),yt(this),this.domObserver.start()}get root(){let e=this._root;if(e==null){for(let t=this.dom.parentNode;t;t=t.parentNode)if(t.nodeType==9||t.nodeType==11&&t.host)return t.getSelection||(Object.getPrototypeOf(t).getSelection=()=>t.ownerDocument.getSelection()),this._root=t}return e||document}updateRoot(){this._root=null}posAtCoords(e){return Lc(this,e)}coordsAtPos(e,t=1){return Na(this,e,t)}domAtPos(e,t=0){return this.docView.domFromPos(e,t)}nodeDOM(e){let t=this.docView.descAt(e);return t?t.nodeDOM:null}posAtDOM(e,t,n=-1){let o=this.docView.posFromDOM(e,t,n);if(o==null)throw new RangeError("DOM position not inside the editor");return o}endOfTextblock(e,t){return Ic(this,t||this.state,e)}pasteHTML(e,t){return Fn(this,"",e,!1,t||new ClipboardEvent("paste"))}pasteText(e,t){return Fn(this,e,null,!0,t||new ClipboardEvent("paste"))}destroy(){this.docView&&(function(e){e.domObserver.stop();for(let t in e.input.eventHandlers)e.dom.removeEventListener(t,e.input.eventHandlers[t]);clearTimeout(e.input.composingTimeout),clearTimeout(e.input.lastIOSEnterFallbackTimeout)}(this),this.destroyPluginViews(),this.mounted?(this.docView.update(this.state.doc,[],no(this),this),this.dom.textContent=""):this.dom.parentNode&&this.dom.parentNode.removeChild(this.dom),this.docView.destroy(),this.docView=null,Oo=null)}get isDestroyed(){return this.docView==null}dispatchEvent(e){return function(t,n){$o(t,n)||!Ee[n.type]||!t.editable&&n.type in Ne||Ee[n.type](t,n)}(this,e)}dispatch(e){let t=this._props.dispatchTransaction;t?t.call(this,e):this.updateState(this.state.apply(e))}domSelectionRange(){let e=this.domSelection();return Le&&this.root.nodeType===11&&function(t){let n=t.activeElement;for(;n&&n.shadowRoot;)n=n.shadowRoot.activeElement;return n}(this.dom.ownerDocument)==this.dom&&function(t,n){if(n.getComposedRanges){let s=n.getComposedRanges(t.root)[0];if(s)return ms(t,s)}let o;function i(s){s.preventDefault(),s.stopImmediatePropagation(),o=s.getTargetRanges()[0]}return t.dom.addEventListener("beforeinput",i,!0),document.execCommand("indent"),t.dom.removeEventListener("beforeinput",i,!0),o?ms(t,o):null}(this,e)||e}domSelection(){return this.root.getSelection()}}function Cs(r){let e=Object.create(null);return e.class="ProseMirror",e.contenteditable=String(r.editable),r.someProp("attributes",t=>{if(typeof t=="function"&&(t=t(r.state)),t)for(let n in t)n=="class"?e.class+=" "+t[n]:n=="style"?e.style=(e.style?e.style+";":"")+t[n]:e[n]||n=="contenteditable"||n=="nodeName"||(e[n]=String(t[n]))}),e.translate||(e.translate="no"),[Be.node(0,r.state.doc.content.size,e)]}function vs(r){if(r.markCursor){let e=document.createElement("img");e.className="ProseMirror-separator",e.setAttribute("mark-placeholder","true"),e.setAttribute("alt",""),r.cursorWrapper={dom:e,deco:Be.widget(r.state.selection.head,e,{raw:!0,marks:r.markCursor})}}else r.cursorWrapper=null}function ws(r){return!r.someProp("editable",e=>e(r.state)===!1)}function bs(r){let e=Object.create(null);function t(n){for(let o in n)Object.prototype.hasOwnProperty.call(e,o)||(e[o]=n[o])}return r.someProp("nodeViews",t),r.someProp("markViews",t),e}function xs(r){if(r.spec.state||r.spec.filterTransaction||r.spec.appendTransaction)throw new RangeError("Plugins passed directly to the view must not have a state component")}for(var zt={8:"Backspace",9:"Tab",10:"Enter",12:"NumLock",13:"Enter",16:"Shift",17:"Control",18:"Alt",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",44:"PrintScreen",45:"Insert",46:"Delete",59:";",61:"=",91:"Meta",92:"Meta",106:"*",107:"+",108:",",109:"-",110:".",111:"/",144:"NumLock",145:"ScrollLock",160:"Shift",161:"Shift",162:"Control",163:"Control",164:"Alt",165:"Alt",173:"-",186:";",187:"=",188:",",189:"-",190:".",191:"/",192:"`",219:"[",220:"\\",221:"]",222:"'"},kr={48:")",49:"!",50:"@",51:"#",52:"$",53:"%",54:"^",55:"&",56:"*",57:"(",59:":",61:"+",173:"_",186:":",187:"+",188:"<",189:"_",190:">",191:"?",192:"~",219:"{",220:"|",221:"}",222:'"'},oh=typeof navigator<"u"&&/Mac/.test(navigator.platform),ih=typeof navigator<"u"&&/MSIE \d|Trident\/(?:[7-9]|\d{2,})\..*rv:(\d+)/.exec(navigator.userAgent),we=0;we<10;we++)zt[48+we]=zt[96+we]=String(we);for(we=1;we<=24;we++)zt[we+111]="F"+we;for(we=65;we<=90;we++)zt[we]=String.fromCharCode(we+32),kr[we]=String.fromCharCode(we);for(var oo in zt)kr.hasOwnProperty(oo)||(kr[oo]=zt[oo]);const sh=typeof navigator<"u"&&/Mac|iP(hone|[oa]d)/.test(navigator.platform);function ah(r){let e,t,n,o,i=r.split(/-(?!$)/),s=i[i.length-1];s=="Space"&&(s=" ");for(let a=0;a<i.length-1;a++){let l=i[a];if(/^(cmd|meta|m)$/i.test(l))o=!0;else if(/^a(lt)?$/i.test(l))e=!0;else if(/^(c|ctrl|control)$/i.test(l))t=!0;else if(/^s(hift)?$/i.test(l))n=!0;else{if(!/^mod$/i.test(l))throw new Error("Unrecognized modifier name: "+l);sh?o=!0:t=!0}}return e&&(s="Alt-"+s),t&&(s="Ctrl-"+s),o&&(s="Meta-"+s),n&&(s="Shift-"+s),s}function io(r,e,t=!0){return e.altKey&&(r="Alt-"+r),e.ctrlKey&&(r="Ctrl-"+r),e.metaKey&&(r="Meta-"+r),t&&e.shiftKey&&(r="Shift-"+r),r}function Ya(r){let e=function(t){let n=Object.create(null);for(let o in t)n[ah(o)]=t[o];return n}(r);return function(t,n){let o,i=function(a){var l=!(oh&&a.metaKey&&a.shiftKey&&!a.ctrlKey&&!a.altKey||ih&&a.shiftKey&&a.key&&a.key.length==1||a.key=="Unidentified")&&a.key||(a.shiftKey?kr:zt)[a.keyCode]||a.key||"Unidentified";return l=="Esc"&&(l="Escape"),l=="Del"&&(l="Delete"),l=="Left"&&(l="ArrowLeft"),l=="Up"&&(l="ArrowUp"),l=="Right"&&(l="ArrowRight"),l=="Down"&&(l="ArrowDown"),l}(n),s=e[io(i,n)];if(s&&s(t.state,t.dispatch,t))return!0;if(i.length==1&&i!=" "){if(n.shiftKey){let a=e[io(i,n,!1)];if(a&&a(t.state,t.dispatch,t))return!0}if((n.shiftKey||n.altKey||n.metaKey||i.charCodeAt(0)>127)&&(o=zt[n.keyCode])&&o!=i){let a=e[io(o,n)];if(a&&a(t.state,t.dispatch,t))return!0}}return!1}}const Po=(r,e)=>!r.selection.empty&&(e&&e(r.tr.deleteSelection().scrollIntoView()),!0);function Xa(r,e){let{$cursor:t}=r.selection;return!t||(e?!e.endOfTextblock("backward",r):t.parentOffset>0)?null:t}const Qa=(r,e,t)=>{let n=Xa(r,t);if(!n)return!1;let o=ni(n);if(!o){let s=n.blockRange(),a=s&&On(s);return a!=null&&(e&&e(r.tr.lift(s,a).scrollIntoView()),!0)}let i=o.nodeBefore;if(!i.type.spec.isolating&&al(r,o,e))return!0;if(n.parent.content.size==0&&(Tn(i,"end")||V.isSelectable(i))){let s=Ar(r.doc,n.before(),n.after(),D.empty);if(s&&s.slice.size<s.to-s.from){if(e){let a=r.tr.step(s);a.setSelection(Tn(i,"end")?J.findFrom(a.doc.resolve(a.mapping.map(o.pos,-1)),-1):V.create(a.doc,o.pos-i.nodeSize)),e(a.scrollIntoView())}return!0}}return!(!i.isAtom||o.depth!=n.depth-1)&&(e&&e(r.tr.delete(o.pos-i.nodeSize,o.pos).scrollIntoView()),!0)};function ks(r,e,t){let n=e.nodeBefore,o=e.pos-1;for(;!n.isTextblock;o--){if(n.type.spec.isolating)return!1;let l=n.lastChild;if(!l)return!1;n=l}let i=e.nodeAfter,s=e.pos+1;for(;!i.isTextblock;s++){if(i.type.spec.isolating)return!1;let l=i.firstChild;if(!l)return!1;i=l}let a=Ar(r.doc,o,s,D.empty);if(!a||a.from!=o||a instanceof ge&&a.slice.size>=s-o)return!1;if(t){let l=r.tr.step(a);l.setSelection(K.create(l.doc,o)),t(l.scrollIntoView())}return!0}function Tn(r,e,t=!1){for(let n=r;n;n=e=="start"?n.firstChild:n.lastChild){if(n.isTextblock)return!0;if(t&&n.childCount!=1)return!1}return!1}const el=(r,e,t)=>{let{$head:n,empty:o}=r.selection,i=n;if(!o)return!1;if(n.parent.isTextblock){if(t?!t.endOfTextblock("backward",r):n.parentOffset>0)return!1;i=ni(n)}let s=i&&i.nodeBefore;return!(!s||!V.isSelectable(s))&&(e&&e(r.tr.setSelection(V.create(r.doc,i.pos-s.nodeSize)).scrollIntoView()),!0)};function ni(r){if(!r.parent.type.spec.isolating)for(let e=r.depth-1;e>=0;e--){if(r.index(e)>0)return r.doc.resolve(r.before(e+1));if(r.node(e).type.spec.isolating)break}return null}function tl(r,e){let{$cursor:t}=r.selection;return!t||(e?!e.endOfTextblock("forward",r):t.parentOffset<t.parent.content.size)?null:t}const nl=(r,e,t)=>{let n=tl(r,t);if(!n)return!1;let o=ri(n);if(!o)return!1;let i=o.nodeAfter;if(al(r,o,e))return!0;if(n.parent.content.size==0&&(Tn(i,"start")||V.isSelectable(i))){let s=Ar(r.doc,n.before(),n.after(),D.empty);if(s&&s.slice.size<s.to-s.from){if(e){let a=r.tr.step(s);a.setSelection(Tn(i,"start")?J.findFrom(a.doc.resolve(a.mapping.map(o.pos)),1):V.create(a.doc,a.mapping.map(o.pos))),e(a.scrollIntoView())}return!0}}return!(!i.isAtom||o.depth!=n.depth-1)&&(e&&e(r.tr.delete(o.pos,o.pos+i.nodeSize).scrollIntoView()),!0)},rl=(r,e,t)=>{let{$head:n,empty:o}=r.selection,i=n;if(!o)return!1;if(n.parent.isTextblock){if(t?!t.endOfTextblock("forward",r):n.parentOffset<n.parent.content.size)return!1;i=ri(n)}let s=i&&i.nodeAfter;return!(!s||!V.isSelectable(s))&&(e&&e(r.tr.setSelection(V.create(r.doc,i.pos)).scrollIntoView()),!0)};function ri(r){if(!r.parent.type.spec.isolating)for(let e=r.depth-1;e>=0;e--){let t=r.node(e);if(r.index(e)+1<t.childCount)return r.doc.resolve(r.after(e+1));if(t.type.spec.isolating)break}return null}const ol=(r,e)=>{let{$head:t,$anchor:n}=r.selection;return!(!t.parent.type.spec.code||!t.sameParent(n))&&(e&&e(r.tr.insertText(`
`).scrollIntoView()),!0)};function oi(r){for(let e=0;e<r.edgeCount;e++){let{type:t}=r.edge(e);if(t.isTextblock&&!t.hasRequiredAttrs())return t}return null}const il=(r,e)=>{let t=r.selection,{$from:n,$to:o}=t;if(t instanceof We||n.parent.inlineContent||o.parent.inlineContent)return!1;let i=oi(o.parent.contentMatchAt(o.indexAfter()));if(!i||!i.isTextblock)return!1;if(e){let s=(!n.parentOffset&&o.index()<o.parent.childCount?n:o).pos,a=r.tr.insert(s,i.createAndFill());a.setSelection(K.create(a.doc,s+1)),e(a.scrollIntoView())}return!0},sl=(r,e)=>{let{$cursor:t}=r.selection;if(!t||t.parent.content.size)return!1;if(t.depth>1&&t.after()!=t.end(-1)){let i=t.before();if(gt(r.doc,i))return e&&e(r.tr.split(i).scrollIntoView()),!0}let n=t.blockRange(),o=n&&On(n);return o!=null&&(e&&e(r.tr.lift(n,o).scrollIntoView()),!0)},lh=(r,e)=>{let{$from:t,$to:n}=r.selection;if(r.selection instanceof V&&r.selection.node.isBlock)return!(!t.parentOffset||!gt(r.doc,t.pos)||(e&&e(r.tr.split(t.pos).scrollIntoView()),0));if(!t.parent.isBlock)return!1;if(e){let o=n.parentOffset==n.parent.content.size,i=r.tr;(r.selection instanceof K||r.selection instanceof We)&&i.deleteSelection();let s=t.depth==0?null:oi(t.node(-1).contentMatchAt(t.indexAfter(-1))),a=o&&s?[{type:s}]:void 0,l=gt(i.doc,i.mapping.map(t.pos),1,a);if(a||l||!gt(i.doc,i.mapping.map(t.pos),1,s?[{type:s}]:void 0)||(s&&(a=[{type:s}]),l=!0),l&&(i.split(i.mapping.map(t.pos),1,a),!o&&!t.parentOffset&&t.parent.type!=s)){let c=i.mapping.map(t.before()),h=i.doc.resolve(c);s&&t.node(-1).canReplaceWith(h.index(),h.index()+1,s)&&i.setNodeMarkup(i.mapping.map(t.before()),s)}e(i.scrollIntoView())}return!0};function al(r,e,t){let n,o,i=e.nodeBefore,s=e.nodeAfter;if(i.type.spec.isolating||s.type.spec.isolating)return!1;if(function(d,p,f){let u=p.nodeBefore,m=p.nodeAfter,g=p.index();return!(!(u&&m&&u.type.compatibleContent(m.type))||(!u.content.size&&p.parent.canReplace(g-1,g)?(f&&f(d.tr.delete(p.pos-u.nodeSize,p.pos).scrollIntoView()),0):!p.parent.canReplace(g,g+1)||!m.isTextblock&&!an(d.doc,p.pos)||(f&&f(d.tr.clearIncompatible(p.pos,u.type,u.contentMatchAt(u.childCount)).join(p.pos).scrollIntoView()),0)))}(r,e,t))return!0;let a=e.parent.canReplace(e.index(),e.index()+1);if(a&&(n=(o=i.contentMatchAt(i.childCount)).findWrapping(s.type))&&o.matchType(n[0]||s.type).validEnd){if(t){let d=e.pos+s.nodeSize,p=S.empty;for(let m=n.length-1;m>=0;m--)p=S.from(n[m].create(null,p));p=S.from(i.copy(p));let f=r.tr.step(new ye(e.pos-1,d,e.pos,d,new D(p,1,0),n.length,!0)),u=d+2*n.length;an(f.doc,u)&&f.join(u),t(f.scrollIntoView())}return!0}let l=J.findFrom(e,1),c=l&&l.$from.blockRange(l.$to),h=c&&On(c);if(h!=null&&h>=e.depth)return t&&t(r.tr.lift(c,h).scrollIntoView()),!0;if(a&&Tn(s,"start",!0)&&Tn(i,"end")){let d=i,p=[];for(;p.push(d),!d.isTextblock;)d=d.lastChild;let f=s,u=1;for(;!f.isTextblock;f=f.firstChild)u++;if(d.canReplace(d.childCount,d.childCount,f.content)){if(t){let m=S.empty;for(let g=p.length-1;g>=0;g--)m=S.from(p[g].copy(m));t(r.tr.step(new ye(e.pos-p.length,e.pos+s.nodeSize,e.pos+u,e.pos+s.nodeSize-u,new D(m,p.length,0),0,!0)).scrollIntoView())}return!0}}return!1}function ll(r){return function(e,t){let n=e.selection,o=r<0?n.$from:n.$to,i=o.depth;for(;o.node(i).isInline;){if(!i)return!1;i--}return!!o.node(i).isTextblock&&(t&&t(e.tr.setSelection(K.create(e.doc,r<0?o.start(i):o.end(i)))),!0)}}const ch=ll(-1),hh=ll(1);function Ms(r,e=null){return function(t,n){let o=!1;for(let i=0;i<t.selection.ranges.length&&!o;i++){let{$from:{pos:s},$to:{pos:a}}=t.selection.ranges[i];t.doc.nodesBetween(s,a,(l,c)=>{if(o)return!1;if(l.isTextblock&&!l.hasMarkup(r,e))if(l.type==r)o=!0;else{let h=t.doc.resolve(c),d=h.index();o=h.parent.canReplaceWith(d,d+1,r)}})}if(!o)return!1;if(n){let i=t.tr;for(let s=0;s<t.selection.ranges.length;s++){let{$from:{pos:a},$to:{pos:l}}=t.selection.ranges[s];i.setBlockType(a,l,r,e)}n(i.scrollIntoView())}return!0}}function so(...r){return function(e,t,n){for(let o=0;o<r.length;o++)if(r[o](e,t,n))return!0;return!1}}function dh(r,e=null){return function(t,n){let{$from:o,$to:i}=t.selection,s=o.blockRange(i),a=!1,l=s;if(!s)return!1;if(s.depth>=2&&o.node(s.depth-1).type.compatibleContent(r)&&s.startIndex==0){if(o.index(s.depth-1)==0)return!1;let h=t.doc.resolve(s.start-2);l=new Cr(h,h,s.depth),s.endIndex<s.parent.childCount&&(s=new Cr(o,t.doc.resolve(i.end(s.depth)),s.depth)),a=!0}let c=va(l,r,e,s);return!!c&&(n&&n(function(h,d,p,f,u){let m=S.empty;for(let v=p.length-1;v>=0;v--)m=S.from(p[v].type.create(p[v].attrs,m));h.step(new ye(d.start-(f?2:0),d.end,d.start,d.end,new D(m,0,0),p.length,!0));let g=0;for(let v=0;v<p.length;v++)p[v].type==u&&(g=v+1);let y=p.length-g,C=d.start+p.length-(f?2:0),w=d.parent;for(let v=d.startIndex,x=d.endIndex,O=!0;v<x;v++,O=!1)!O&&gt(h.doc,C,y)&&(h.split(C,y),C+=2*y),C+=w.child(v).nodeSize;return h}(t.tr,s,c,a,r).scrollIntoView()),!0)}}function ph(r){return function(e,t){let{$from:n,$to:o}=e.selection,i=n.blockRange(o,s=>s.childCount>0&&s.firstChild.type==r);return!!i&&(!t||(n.node(i.depth-1).type==r?function(s,a,l,c){let h=s.tr,d=c.end,p=c.$to.end(c.depth);d<p&&(h.step(new ye(d-1,p,d,p,new D(S.from(l.create(null,c.parent.copy())),1,0),1,!0)),c=new Cr(h.doc.resolve(c.$from.pos),h.doc.resolve(p),c.depth));const f=On(c);if(f==null)return!1;h.lift(c,f);let u=h.mapping.map(d,-1)-1;return an(h.doc,u)&&h.join(u),a(h.scrollIntoView()),!0}(e,t,r,i):function(s,a,l){let c=s.tr,h=l.parent;for(let w=l.end,v=l.endIndex-1,x=l.startIndex;v>x;v--)w-=h.child(v).nodeSize,c.delete(w-1,w+1);let d=c.doc.resolve(l.start),p=d.nodeAfter;if(c.mapping.map(l.end)!=l.start+d.nodeAfter.nodeSize)return!1;let f=l.startIndex==0,u=l.endIndex==h.childCount,m=d.node(-1),g=d.index(-1);if(!m.canReplace(g+(f?0:1),g+1,p.content.append(u?S.empty:S.from(h))))return!1;let y=d.pos,C=y+p.nodeSize;return c.step(new ye(y-(f?1:0),C+(u?1:0),y+1,C-1,new D((f?S.empty:S.from(h.copy(S.empty))).append(u?S.empty:S.from(h.copy(S.empty))),f?0:1,u?0:1),f?0:1)),a(c.scrollIntoView()),!0}(e,t,i)))}}function $r(r){const{state:e,transaction:t}=r;let{selection:n}=t,{doc:o}=t,{storedMarks:i}=t;return{...e,apply:e.apply.bind(e),applyTransaction:e.applyTransaction.bind(e),plugins:e.plugins,schema:e.schema,reconfigure:e.reconfigure.bind(e),toJSON:e.toJSON.bind(e),get storedMarks(){return i},get selection(){return n},get doc(){return o},get tr(){return n=t.selection,o=t.doc,i=t.storedMarks,t}}}so(Po,Qa,el),so(Po,nl,rl),so(ol,il,sl,lh),typeof navigator<"u"?/Mac|iP(hone|[oa]d)/.test(navigator.platform):typeof os<"u"&&os.platform&&os.platform();let Rr=class{constructor(r){this.editor=r.editor,this.rawCommands=this.editor.extensionManager.commands,this.customState=r.state}get hasCustomState(){return!!this.customState}get state(){return this.customState||this.editor.state}get commands(){const{rawCommands:r,editor:e,state:t}=this,{view:n}=e,{tr:o}=t,i=this.buildProps(o);return Object.fromEntries(Object.entries(r).map(([s,a])=>[s,(...l)=>{const c=a(...l)(i);return o.getMeta("preventDispatch")||this.hasCustomState||n.dispatch(o),c}]))}get chain(){return()=>this.createChain()}get can(){return()=>this.createCan()}createChain(r,e=!0){const{rawCommands:t,editor:n,state:o}=this,{view:i}=n,s=[],a=!!r,l=r||o.tr,c={...Object.fromEntries(Object.entries(t).map(([h,d])=>[h,(...p)=>{const f=this.buildProps(l,e),u=d(...p)(f);return s.push(u),c}])),run:()=>(a||!e||l.getMeta("preventDispatch")||this.hasCustomState||i.dispatch(l),s.every(h=>h===!0))};return c}createCan(r){const{rawCommands:e,state:t}=this,n=!1,o=r||t.tr,i=this.buildProps(o,n);return{...Object.fromEntries(Object.entries(e).map(([a,l])=>[a,(...c)=>l(...c)({...i,dispatch:void 0})])),chain:()=>this.createChain(o,n)}}buildProps(r,e=!0){const{rawCommands:t,editor:n,state:o}=this,{view:i}=n,s={tr:r,editor:n,view:i,state:$r({state:o,transaction:r}),dispatch:e?()=>{}:void 0,chain:()=>this.createChain(r,e),can:()=>this.createCan(r),get commands(){return Object.fromEntries(Object.entries(t).map(([a,l])=>[a,(...c)=>l(...c)(s)]))}};return s}};class uh{constructor(){this.callbacks={}}on(e,t){return this.callbacks[e]||(this.callbacks[e]=[]),this.callbacks[e].push(t),this}emit(e,...t){const n=this.callbacks[e];return n&&n.forEach(o=>o.apply(this,t)),this}off(e,t){const n=this.callbacks[e];return n&&(t?this.callbacks[e]=n.filter(o=>o!==t):delete this.callbacks[e]),this}removeAllListeners(){this.callbacks={}}}function $(r,e,t){return r.config[e]===void 0&&r.parent?$(r.parent,e,t):typeof r.config[e]=="function"?r.config[e].bind({...t,parent:r.parent?$(r.parent,e,t):null}):r.config[e]}function Mr(r){return{baseExtensions:r.filter(e=>e.type==="extension"),nodeExtensions:r.filter(e=>e.type==="node"),markExtensions:r.filter(e=>e.type==="mark")}}function Ss(r){const e=[],{nodeExtensions:t,markExtensions:n}=Mr(r),o=[...t,...n],i={default:null,rendered:!0,renderHTML:null,parseHTML:null,keepOnSplit:!0,isRequired:!1};return r.forEach(s=>{const a=$(s,"addGlobalAttributes",{name:s.name,options:s.options,storage:s.storage});a&&a().forEach(l=>{l.types.forEach(c=>{Object.entries(l.attributes).forEach(([h,d])=>{e.push({type:c,name:h,attribute:{...i,...d}})})})})}),o.forEach(s=>{const a={name:s.name,options:s.options,storage:s.storage},l=$(s,"addAttributes",a);if(!l)return;const c=l();Object.entries(c).forEach(([h,d])=>{const p={...i,...d};typeof(p==null?void 0:p.default)=="function"&&(p.default=p.default()),p!=null&&p.isRequired&&(p==null?void 0:p.default)===void 0&&delete p.default,e.push({type:s.name,name:h,attribute:p})})}),e}function fe(r,e){if(typeof r=="string"){if(!e.nodes[r])throw Error(`There is no node type named '${r}'. Maybe you forgot to add the extension?`);return e.nodes[r]}return r}function Zt(...r){return r.filter(e=>!!e).reduce((e,t)=>{const n={...e};return Object.entries(t).forEach(([o,i])=>{if(n[o])if(o==="class"){const s=i?i.split(" "):[],a=n[o]?n[o].split(" "):[],l=s.filter(c=>!a.includes(c));n[o]=[...a,...l].join(" ")}else n[o]=o==="style"?[n[o],i].join("; "):i;else n[o]=i}),n},{})}function ao(r,e){return e.filter(t=>t.attribute.rendered).map(t=>t.attribute.renderHTML?t.attribute.renderHTML(r.attrs)||{}:{[t.name]:r.attrs[t.name]}).reduce((t,n)=>Zt(t,n),{})}function cl(r){return typeof r=="function"}function W(r,e=void 0,...t){return cl(r)?e?r.bind(e)(...t):r(...t):r}function Os(r,e){return r.style?r:{...r,getAttrs:t=>{const n=r.getAttrs?r.getAttrs(t):r.attrs;if(n===!1)return!1;const o=e.reduce((i,s)=>{const a=s.attribute.parseHTML?s.attribute.parseHTML(t):function(l){return typeof l!="string"?l:l.match(/^[+-]?(?:\d*\.)?\d+$/)?Number(l):l==="true"||l!=="false"&&l}(t.getAttribute(s.name));return a==null?i:{...i,[s.name]:a}},{});return{...n,...o}}}}function Es(r){return Object.fromEntries(Object.entries(r).filter(([e,t])=>(e!=="attrs"||!function(n={}){return Object.keys(n).length===0&&n.constructor===Object}(t))&&t!=null))}function lo(r,e){return e.nodes[r]||e.marks[r]||null}function Ts(r,e){return Array.isArray(e)?e.some(t=>(typeof t=="string"?t:t.name)===r.name):e}const fh=(r,e=500)=>{let t="";const n=r.parentOffset;return r.parent.nodesBetween(Math.max(0,n-e),n,(o,i,s,a)=>{var l,c;const h=((c=(l=o.type.spec).toText)===null||c===void 0?void 0:c.call(l,{node:o,pos:i,parent:s,index:a}))||o.textContent||"%leaf%";t+=h.slice(0,Math.max(0,n-i))}),t};function ii(r){return Object.prototype.toString.call(r)==="[object RegExp]"}class mh{constructor(e){this.find=e.find,this.handler=e.handler}}const gh=(r,e)=>{if(ii(e))return e.exec(r);const t=e(r);if(!t)return null;const n=[t.text];return n.index=t.index,n.input=r,n.data=t.data,t.replaceWith&&(t.text.includes(t.replaceWith)||console.warn('[tiptap warn]: "inputRuleMatch.replaceWith" must be part of "inputRuleMatch.text".'),n.push(t.replaceWith)),n};function er(r){var e;const{editor:t,from:n,to:o,text:i,rules:s,plugin:a}=r,{view:l}=t;if(l.composing)return!1;const c=l.state.doc.resolve(n);if(c.parent.type.spec.code||!((e=c.nodeBefore||c.nodeAfter)===null||e===void 0)&&e.marks.find(p=>p.type.spec.code))return!1;let h=!1;const d=fh(c)+i;return s.forEach(p=>{if(h)return;const f=gh(d,p.find);if(!f)return;const u=l.state.tr,m=$r({state:l.state,transaction:u}),g={from:n-(f[0].length-i.length),to:o},{commands:y,chain:C,can:w}=new Rr({editor:t,state:m});p.handler({state:m,range:g,match:f,commands:y,chain:C,can:w})!==null&&u.steps.length&&(u.setMeta(a,{transform:u,from:n,to:o,text:i}),l.dispatch(u),h=!0)}),h}function yh(r){const{editor:e,rules:t}=r,n=new He({state:{init:()=>null,apply(o,i){const s=o.getMeta(n);if(s)return s;const a=o.getMeta("applyInputRules");return a&&setTimeout(()=>{const{from:l,text:c}=a,h=l+c.length;er({editor:e,from:l,to:h,text:c,rules:t,plugin:n})}),o.selectionSet||o.docChanged?null:i}},props:{handleTextInput:(o,i,s,a)=>er({editor:e,from:i,to:s,text:a,rules:t,plugin:n}),handleDOMEvents:{compositionend:o=>(setTimeout(()=>{const{$cursor:i}=o.state.selection;i&&er({editor:e,from:i.pos,to:i.pos,text:"",rules:t,plugin:n})}),!1)},handleKeyDown(o,i){if(i.key!=="Enter")return!1;const{$cursor:s}=o.state.selection;return!!s&&er({editor:e,from:s.pos,to:s.pos,text:`
`,rules:t,plugin:n})}},isInputRules:!0});return n}const Ch=(r,e,t)=>{if(ii(e))return[...r.matchAll(e)];const n=e(r,t);return n?n.map(o=>{const i=[o.text];return i.index=o.index,i.input=r,i.data=o.data,o.replaceWith&&(o.text.includes(o.replaceWith)||console.warn('[tiptap warn]: "pasteRuleMatch.replaceWith" must be part of "pasteRuleMatch.text".'),i.push(o.replaceWith)),i}):[]};function vh(r){const{editor:e,rules:t}=r;let n=null,o=!1,i=!1,s=typeof ClipboardEvent<"u"?new ClipboardEvent("paste"):null,a=typeof DragEvent<"u"?new DragEvent("drop"):null;const l=({state:h,from:d,to:p,rule:f,pasteEvt:u})=>{const m=h.tr,g=$r({state:h,transaction:m});if(function(C){const{editor:w,state:v,from:x,to:O,rule:E,pasteEvent:M,dropEvent:k}=C,{commands:N,chain:I,can:L}=new Rr({editor:w,state:v}),z=[];return v.doc.nodesBetween(x,O,(A,F)=>{if(!A.isTextblock||A.type.spec.code)return;const _=Math.max(x,F),H=Math.min(O,F+A.content.size),G=A.textBetween(_-F,H-F,void 0,"￼");Ch(G,E.find,M).forEach(B=>{if(B.index===void 0)return;const ee=_+B.index+1,re=ee+B[0].length,ae={from:v.tr.mapping.map(ee),to:v.tr.mapping.map(re)},te=E.handler({state:v,range:ae,match:B,commands:N,chain:I,can:L,pasteEvent:M,dropEvent:k});z.push(te)})}),z.every(A=>A!==null)}({editor:e,state:g,from:Math.max(d-1,0),to:p.b-1,rule:f,pasteEvent:u,dropEvent:a})&&m.steps.length)return a=typeof DragEvent<"u"?new DragEvent("drop"):null,s=typeof ClipboardEvent<"u"?new ClipboardEvent("paste"):null,m};return t.map(h=>new He({view(d){const p=f=>{var u;n=!((u=d.dom.parentElement)===null||u===void 0)&&u.contains(f.target)?d.dom.parentElement:null};return window.addEventListener("dragstart",p),{destroy(){window.removeEventListener("dragstart",p)}}},props:{handleDOMEvents:{drop:(d,p)=>(i=n===d.dom.parentElement,a=p,!1),paste:(d,p)=>{var f;const u=(f=p.clipboardData)===null||f===void 0?void 0:f.getData("text/html");return s=p,o=!!(u!=null&&u.includes("data-pm-slice")),!1}}},appendTransaction:(d,p,f)=>{const u=d[0],m=u.getMeta("uiEvent")==="paste"&&!o,g=u.getMeta("uiEvent")==="drop"&&!i,y=u.getMeta("applyPasteRules"),C=!!y;if(!m&&!g&&!C)return;if(C){const{from:x,text:O}=y,E=x+O.length,M=(k=>{var N;const I=new ClipboardEvent("paste",{clipboardData:new DataTransfer});return(N=I.clipboardData)===null||N===void 0||N.setData("text/html",k),I})(O);return l({rule:h,state:f,from:x,to:{b:E},pasteEvt:M})}const w=p.doc.content.findDiffStart(f.doc.content),v=p.doc.content.findDiffEnd(f.doc.content);return typeof w=="number"&&v&&w!==v.b?l({rule:h,state:f,from:w,to:v,pasteEvt:s}):void 0}}))}class vn{constructor(e,t){this.splittableMarks=[],this.editor=t,this.extensions=vn.resolve(e),this.schema=function(n,o){var i;const s=Ss(n),{nodeExtensions:a,markExtensions:l}=Mr(n),c=(i=a.find(p=>$(p,"topNode")))===null||i===void 0?void 0:i.name,h=Object.fromEntries(a.map(p=>{const f=s.filter(w=>w.type===p.name),u={name:p.name,options:p.options,storage:p.storage,editor:o},m=Es({...n.reduce((w,v)=>{const x=$(v,"extendNodeSchema",u);return{...w,...x?x(p):{}}},{}),content:W($(p,"content",u)),marks:W($(p,"marks",u)),group:W($(p,"group",u)),inline:W($(p,"inline",u)),atom:W($(p,"atom",u)),selectable:W($(p,"selectable",u)),draggable:W($(p,"draggable",u)),code:W($(p,"code",u)),defining:W($(p,"defining",u)),isolating:W($(p,"isolating",u)),attrs:Object.fromEntries(f.map(w=>{var v;return[w.name,{default:(v=w==null?void 0:w.attribute)===null||v===void 0?void 0:v.default}]}))}),g=W($(p,"parseHTML",u));g&&(m.parseDOM=g.map(w=>Os(w,f)));const y=$(p,"renderHTML",u);y&&(m.toDOM=w=>y({node:w,HTMLAttributes:ao(w,f)}));const C=$(p,"renderText",u);return C&&(m.toText=C),[p.name,m]})),d=Object.fromEntries(l.map(p=>{const f=s.filter(C=>C.type===p.name),u={name:p.name,options:p.options,storage:p.storage,editor:o},m=Es({...n.reduce((C,w)=>{const v=$(w,"extendMarkSchema",u);return{...C,...v?v(p):{}}},{}),inclusive:W($(p,"inclusive",u)),excludes:W($(p,"excludes",u)),group:W($(p,"group",u)),spanning:W($(p,"spanning",u)),code:W($(p,"code",u)),attrs:Object.fromEntries(f.map(C=>{var w;return[C.name,{default:(w=C==null?void 0:C.attribute)===null||w===void 0?void 0:w.default}]}))}),g=W($(p,"parseHTML",u));g&&(m.parseDOM=g.map(C=>Os(C,f)));const y=$(p,"renderHTML",u);return y&&(m.toDOM=C=>y({mark:C,HTMLAttributes:ao(C,f)})),[p.name,m]}));return new pc({topNode:c,nodes:h,marks:d})}(this.extensions,t),this.setupExtensions()}static resolve(e){const t=vn.sort(vn.flatten(e)),n=function(o){const i=o.filter((s,a)=>o.indexOf(s)!==a);return[...new Set(i)]}(t.map(o=>o.name));return n.length&&console.warn(`[tiptap warn]: Duplicate extension names found: [${n.map(o=>`'${o}'`).join(", ")}]. This can lead to issues.`),t}static flatten(e){return e.map(t=>{const n=$(t,"addExtensions",{name:t.name,options:t.options,storage:t.storage});return n?[t,...this.flatten(n())]:t}).flat(10)}static sort(e){return e.sort((t,n)=>{const o=$(t,"priority")||100,i=$(n,"priority")||100;return o>i?-1:o<i?1:0})}get commands(){return this.extensions.reduce((e,t)=>{const n=$(t,"addCommands",{name:t.name,options:t.options,storage:t.storage,editor:this.editor,type:lo(t.name,this.schema)});return n?{...e,...n()}:e},{})}get plugins(){const{editor:e}=this,t=vn.sort([...this.extensions].reverse()),n=[],o=[],i=t.map(s=>{const a={name:s.name,options:s.options,storage:s.storage,editor:e,type:lo(s.name,this.schema)},l=[],c=$(s,"addKeyboardShortcuts",a);let h={};if(s.type==="mark"&&s.config.exitable&&(h.ArrowRight=()=>Tr.handleExit({editor:e,mark:s})),c){const m=Object.fromEntries(Object.entries(c()).map(([g,y])=>[g,()=>y({editor:e})]));h={...h,...m}}const d=new He({props:{handleKeyDown:Ya(h)}});l.push(d);const p=$(s,"addInputRules",a);Ts(s,e.options.enableInputRules)&&p&&n.push(...p());const f=$(s,"addPasteRules",a);Ts(s,e.options.enablePasteRules)&&f&&o.push(...f());const u=$(s,"addProseMirrorPlugins",a);if(u){const m=u();l.push(...m)}return l}).flat();return[yh({editor:e,rules:n}),...vh({editor:e,rules:o}),...i]}get attributes(){return Ss(this.extensions)}get nodeViews(){const{editor:e}=this,{nodeExtensions:t}=Mr(this.extensions);return Object.fromEntries(t.filter(n=>!!$(n,"addNodeView")).map(n=>{const o=this.attributes.filter(a=>a.type===n.name),i={name:n.name,options:n.options,storage:n.storage,editor:e,type:fe(n.name,this.schema)},s=$(n,"addNodeView",i);return s?[n.name,(a,l,c,h)=>{const d=ao(a,o);return s()({editor:e,node:a,getPos:c,decorations:h,HTMLAttributes:d,extension:n})}]:[]}))}setupExtensions(){this.extensions.forEach(e=>{var t;this.editor.extensionStorage[e.name]=e.storage;const n={name:e.name,options:e.options,storage:e.storage,editor:this.editor,type:lo(e.name,this.schema)};e.type==="mark"&&((t=W($(e,"keepOnSplit",n)))===null||t===void 0||t)&&this.splittableMarks.push(e.name);const o=$(e,"onBeforeCreate",n),i=$(e,"onCreate",n),s=$(e,"onUpdate",n),a=$(e,"onSelectionUpdate",n),l=$(e,"onTransaction",n),c=$(e,"onFocus",n),h=$(e,"onBlur",n),d=$(e,"onDestroy",n);o&&this.editor.on("beforeCreate",o),i&&this.editor.on("create",i),s&&this.editor.on("update",s),a&&this.editor.on("selectionUpdate",a),l&&this.editor.on("transaction",l),c&&this.editor.on("focus",c),h&&this.editor.on("blur",h),d&&this.editor.on("destroy",d)})}}function co(r){return function(e){return Object.prototype.toString.call(e).slice(8,-1)}(r)==="Object"&&r.constructor===Object&&Object.getPrototypeOf(r)===Object.prototype}function Pr(r,e){const t={...r};return co(r)&&co(e)&&Object.keys(e).forEach(n=>{co(e[n])?n in r?t[n]=Pr(r[n],e[n]):Object.assign(t,{[n]:e[n]}):Object.assign(t,{[n]:e[n]})}),t}class Ue{constructor(e={}){this.type="extension",this.name="extension",this.parent=null,this.child=null,this.config={name:this.name,defaultOptions:{}},this.config={...this.config,...e},this.name=this.config.name,e.defaultOptions&&Object.keys(e.defaultOptions).length>0&&console.warn(`[tiptap warn]: BREAKING CHANGE: "defaultOptions" is deprecated. Please use "addOptions" instead. Found in extension: "${this.name}".`),this.options=this.config.defaultOptions,this.config.addOptions&&(this.options=W($(this,"addOptions",{name:this.name}))),this.storage=W($(this,"addStorage",{name:this.name,options:this.options}))||{}}static create(e={}){return new Ue(e)}configure(e={}){const t=this.extend();return t.parent=this.parent,t.options=Pr(this.options,e),t.storage=W($(t,"addStorage",{name:t.name,options:t.options})),t}extend(e={}){const t=new Ue({...this.config,...e});return t.parent=this,this.child=t,t.name=e.name?e.name:t.parent.name,e.defaultOptions&&console.warn(`[tiptap warn]: BREAKING CHANGE: "defaultOptions" is deprecated. Please use "addOptions" instead. Found in extension: "${t.name}".`),t.options=W($(t,"addOptions",{name:t.name})),t.storage=W($(t,"addStorage",{name:t.name,options:t.options})),t}}function hl(r,e,t){const{from:n,to:o}=e,{blockSeparator:i=`

`,textSerializers:s={}}=t||{};let a="";return r.nodesBetween(n,o,(l,c,h,d)=>{var p;l.isBlock&&c>n&&(a+=i);const f=s==null?void 0:s[l.type.name];if(f)return h&&(a+=f({node:l,pos:c,parent:h,index:d,range:e})),!1;l.isText&&(a+=(p=l==null?void 0:l.text)===null||p===void 0?void 0:p.slice(Math.max(n,c)-c,o-c))}),a}function dl(r){return Object.fromEntries(Object.entries(r.nodes).filter(([,e])=>e.spec.toText).map(([e,t])=>[e,t.spec.toText]))}const wh=Ue.create({name:"clipboardTextSerializer",addOptions:()=>({blockSeparator:void 0}),addProseMirrorPlugins(){return[new He({key:new Ve("clipboardTextSerializer"),props:{clipboardTextSerializer:()=>{const{editor:r}=this,{state:e,schema:t}=r,{doc:n,selection:o}=e,{ranges:i}=o,s=Math.min(...i.map(c=>c.$from.pos)),a=Math.max(...i.map(c=>c.$to.pos)),l=dl(t);return hl(n,{from:s,to:a},{...this.options.blockSeparator!==void 0?{blockSeparator:this.options.blockSeparator}:{},textSerializers:l})}}})]}});function Sr(r,e,t={strict:!0}){const n=Object.keys(e);return!n.length||n.every(o=>t.strict?e[o]===r[o]:ii(e[o])?e[o].test(r[o]):e[o]===r[o])}function zo(r,e,t={}){return r.find(n=>n.type===e&&Sr(n.attrs,t))}function bh(r,e,t={}){return!!zo(r,e,t)}function Ns(r,e,t={}){if(!r||!e)return;let n=r.parent.childAfter(r.parentOffset);if(r.parentOffset===n.offset&&n.offset!==0&&(n=r.parent.childBefore(r.parentOffset)),!n.node)return;const o=zo([...n.node.marks],e,t);if(!o)return;let i=n.index,s=r.start()+n.offset,a=i+1,l=s+n.node.nodeSize;for(zo([...n.node.marks],e,t);i>0&&o.isInSet(r.parent.child(i-1).marks);)i-=1,s-=r.parent.child(i).nodeSize;for(;a<r.parent.childCount&&bh([...r.parent.child(a).marks],e,t);)l+=r.parent.child(a).nodeSize,a+=1;return{from:s,to:l}}function Et(r,e){if(typeof r=="string"){if(!e.marks[r])throw Error(`There is no mark type named '${r}'. Maybe you forgot to add the extension?`);return e.marks[r]}return r}function Ls(r){return r instanceof K}function Yt(r=0,e=0,t=0){return Math.min(Math.max(r,e),t)}function pl(r,e=null){if(!e)return null;const t=J.atStart(r),n=J.atEnd(r);if(e==="start"||e===!0)return t;if(e==="end")return n;const o=t.from,i=n.to;return e==="all"?K.create(r,Yt(0,o,i),Yt(r.content.size,o,i)):K.create(r,Yt(e,o,i),Yt(e,o,i))}function Fo(){return["iPad Simulator","iPhone Simulator","iPod Simulator","iPad","iPhone","iPod"].includes(navigator.platform)||navigator.userAgent.includes("Mac")&&"ontouchend"in document}const ul=r=>{const e=r.childNodes;for(let t=e.length-1;t>=0;t-=1){const n=e[t];n.nodeType===3&&n.nodeValue&&/^(\n\s\s|\n)$/.test(n.nodeValue)?r.removeChild(n):n.nodeType===1&&ul(n)}return r};function As(r){const e=`<body>${r}</body>`,t=new window.DOMParser().parseFromString(e,"text/html").body;return ul(t)}function Or(r,e,t){t={slice:!0,parseOptions:{},...t};const n=typeof r=="string";if(typeof r=="object"&&r!==null)try{return Array.isArray(r)&&r.length>0?S.fromArray(r.map(o=>e.nodeFromJSON(o))):e.nodeFromJSON(r)}catch(o){return console.warn("[tiptap warn]: Invalid content.","Passed value:",r,"Error:",o),Or("",e,t)}if(n){const o=Sn.fromSchema(e);return t.slice?o.parseSlice(As(r),t.parseOptions).content:o.parse(As(r),t.parseOptions)}return Or("",e,t)}function fl(){return typeof navigator<"u"&&/Mac/.test(navigator.platform)}function Bn(r,e,t={}){const{from:n,to:o,empty:i}=r.selection,s=e?fe(e,r.schema):null,a=[];r.doc.nodesBetween(n,o,(h,d)=>{if(h.isText)return;const p=Math.max(n,d),f=Math.min(o,d+h.nodeSize);a.push({node:h,from:p,to:f})});const l=o-n,c=a.filter(h=>!s||s.name===h.node.type.name).filter(h=>Sr(h.node.attrs,t,{strict:!1}));return i?!!c.length:c.reduce((h,d)=>h+d.to-d.from,0)>=l}function Er(r,e){return e.nodes[r]?"node":e.marks[r]?"mark":null}function Ds(r,e){const t=typeof e=="string"?[e]:e;return Object.keys(r).reduce((n,o)=>(t.includes(o)||(n[o]=r[o]),n),{})}function ml(r,e,t={}){return Or(r,e,{slice:!1,parseOptions:t})}function gl(r,e){const t=Et(e,r.schema),{from:n,to:o,empty:i}=r.selection,s=[];i?(r.storedMarks&&s.push(...r.storedMarks),s.push(...r.selection.$head.marks())):r.doc.nodesBetween(n,o,l=>{s.push(...l.marks)});const a=s.find(l=>l.type.name===t.name);return a?{...a.attrs}:{}}function si(r){return e=>function(t,n){for(let o=t.depth;o>0;o-=1){const i=t.node(o);if(n(i))return{pos:o>0?t.before(o):0,start:t.start(o),depth:o,node:i}}}(e.$from,r)}function xh(r,e){const t=Er(typeof e=="string"?e:e.name,r.schema);return t==="node"?function(n,o){const i=fe(o,n.schema),{from:s,to:a}=n.selection,l=[];n.doc.nodesBetween(s,a,h=>{l.push(h)});const c=l.reverse().find(h=>h.type.name===i.name);return c?{...c.attrs}:{}}(r,e):t==="mark"?gl(r,e):{}}function tr(r,e,t){return Object.fromEntries(Object.entries(t).filter(([n])=>{const o=r.find(i=>i.type===e&&i.name===n);return!!o&&o.attribute.keepOnSplit}))}function Bo(r,e,t={}){const{empty:n,ranges:o}=r.selection,i=e?Et(e,r.schema):null;if(n)return!!(r.storedMarks||r.selection.$from.marks()).filter(h=>!i||i.name===h.type.name).find(h=>Sr(h.attrs,t,{strict:!1}));let s=0;const a=[];if(o.forEach(({$from:h,$to:d})=>{const p=h.pos,f=d.pos;r.doc.nodesBetween(p,f,(u,m)=>{if(!u.isText&&!u.marks.length)return;const g=Math.max(p,m),y=Math.min(f,m+u.nodeSize);s+=y-g,a.push(...u.marks.map(C=>({mark:C,from:g,to:y})))})}),s===0)return!1;const l=a.filter(h=>!i||i.name===h.mark.type.name).filter(h=>Sr(h.mark.attrs,t,{strict:!1})).reduce((h,d)=>h+d.to-d.from,0),c=a.filter(h=>!i||h.mark.type!==i&&h.mark.type.excludes(i)).reduce((h,d)=>h+d.to-d.from,0);return(l>0?l+c:l)>=s}function Is(r,e){const{nodeExtensions:t}=Mr(e),n=t.find(i=>i.name===r);if(!n)return!1;const o=W($(n,"group",{name:n.name,options:n.options,storage:n.storage}));return typeof o=="string"&&o.split(" ").includes("list")}function _s(r,e){const t=r.storedMarks||r.selection.$to.parentOffset&&r.selection.$from.marks();if(t){const n=t.filter(o=>e==null?void 0:e.includes(o.type.name));r.tr.ensureMarks(n)}}const ho=(r,e)=>{const t=si(i=>i.type===e)(r.selection);if(!t)return!0;const n=r.doc.resolve(Math.max(0,t.pos-1)).before(t.depth);if(n===void 0)return!0;const o=r.doc.nodeAt(n);return t.node.type!==(o==null?void 0:o.type)||!an(r.doc,t.pos)||(r.join(t.pos),!0)},po=(r,e)=>{const t=si(i=>i.type===e)(r.selection);if(!t)return!0;const n=r.doc.resolve(t.start).after(t.depth);if(n===void 0)return!0;const o=r.doc.nodeAt(n);return t.node.type!==(o==null?void 0:o.type)||!an(r.doc,n)||(r.join(n),!0)};var kh=Object.freeze({__proto__:null,blur:()=>({editor:r,view:e})=>(requestAnimationFrame(()=>{var t;r.isDestroyed||(e.dom.blur(),(t=window==null?void 0:window.getSelection())===null||t===void 0||t.removeAllRanges())}),!0),clearContent:(r=!1)=>({commands:e})=>e.setContent("",r),clearNodes:()=>({state:r,tr:e,dispatch:t})=>{const{selection:n}=e,{ranges:o}=n;return!t||(o.forEach(({$from:i,$to:s})=>{r.doc.nodesBetween(i.pos,s.pos,(a,l)=>{if(a.type.isText)return;const{doc:c,mapping:h}=e,d=c.resolve(h.map(l)),p=c.resolve(h.map(l+a.nodeSize)),f=d.blockRange(p);if(!f)return;const u=On(f);if(a.type.isTextblock){const{defaultType:m}=d.parent.contentMatchAt(d.index());e.setNodeMarkup(f.start,m)}(u||u===0)&&e.lift(f,u)})}),!0)},command:r=>e=>r(e),createParagraphNear:()=>({state:r,dispatch:e})=>il(r,e),cut:(r,e)=>({editor:t,tr:n})=>{const{state:o}=t,i=o.doc.slice(r.from,r.to);n.deleteRange(r.from,r.to);const s=n.mapping.map(e);return n.insert(s,i.content),n.setSelection(new K(n.doc.resolve(s-1))),!0},deleteCurrentNode:()=>({tr:r,dispatch:e})=>{const{selection:t}=r,n=t.$anchor.node();if(n.content.size>0)return!1;const o=r.selection.$anchor;for(let i=o.depth;i>0;i-=1)if(o.node(i).type===n.type){if(e){const s=o.before(i),a=o.after(i);r.delete(s,a).scrollIntoView()}return!0}return!1},deleteNode:r=>({tr:e,state:t,dispatch:n})=>{const o=fe(r,t.schema),i=e.selection.$anchor;for(let s=i.depth;s>0;s-=1)if(i.node(s).type===o){if(n){const a=i.before(s),l=i.after(s);e.delete(a,l).scrollIntoView()}return!0}return!1},deleteRange:r=>({tr:e,dispatch:t})=>{const{from:n,to:o}=r;return t&&e.delete(n,o),!0},deleteSelection:()=>({state:r,dispatch:e})=>Po(r,e),enter:()=>({commands:r})=>r.keyboardShortcut("Enter"),exitCode:()=>({state:r,dispatch:e})=>((t,n)=>{let{$head:o,$anchor:i}=t.selection;if(!o.parent.type.spec.code||!o.sameParent(i))return!1;let s=o.node(-1),a=o.indexAfter(-1),l=oi(s.contentMatchAt(a));if(!l||!s.canReplaceWith(a,a,l))return!1;if(n){let c=o.after(),h=t.tr.replaceWith(c,c,l.createAndFill());h.setSelection(J.near(h.doc.resolve(c),1)),n(h.scrollIntoView())}return!0})(r,e),extendMarkRange:(r,e={})=>({tr:t,state:n,dispatch:o})=>{const i=Et(r,n.schema),{doc:s,selection:a}=t,{$from:l,from:c,to:h}=a;if(o){const d=Ns(l,i,e);if(d&&d.from<=c&&d.to>=h){const p=K.create(s,d.from,d.to);t.setSelection(p)}}return!0},first:r=>e=>{const t=typeof r=="function"?r(e):r;for(let n=0;n<t.length;n+=1)if(t[n](e))return!0;return!1},focus:(r=null,e={})=>({editor:t,view:n,tr:o,dispatch:i})=>{e={scrollIntoView:!0,...e};const s=()=>{Fo()&&n.dom.focus(),requestAnimationFrame(()=>{t.isDestroyed||(n.focus(),e!=null&&e.scrollIntoView&&t.commands.scrollIntoView())})};if(n.hasFocus()&&r===null||r===!1)return!0;if(i&&r===null&&!Ls(t.state.selection))return s(),!0;const a=pl(o.doc,r)||t.state.selection,l=t.state.selection.eq(a);return i&&(l||o.setSelection(a),l&&o.storedMarks&&o.setStoredMarks(o.storedMarks),s()),!0},forEach:(r,e)=>t=>r.every((n,o)=>e(n,{...t,index:o})),insertContent:(r,e)=>({tr:t,commands:n})=>n.insertContentAt({from:t.selection.from,to:t.selection.to},r,e),insertContentAt:(r,e,t)=>({tr:n,dispatch:o,editor:i})=>{if(o){t={parseOptions:{},updateSelection:!0,applyInputRules:!1,applyPasteRules:!1,...t};const s=Or(e,i.schema,{parseOptions:{preserveWhitespace:"full",...t.parseOptions}});if(s.toString()==="<>")return!0;let{from:a,to:l}=typeof r=="number"?{from:r,to:r}:{from:r.from,to:r.to},c=!0,h=!0;if((s.toString().startsWith("<")?s:[s]).forEach(p=>{p.check(),c=!!c&&p.isText&&p.marks.length===0,h=!!h&&p.isBlock}),a===l&&h){const{parent:p}=n.doc.resolve(a);p.isTextblock&&!p.type.spec.code&&!p.childCount&&(a-=1,l+=1)}let d;c?(d=Array.isArray(e)?e.map(p=>p.text||"").join(""):typeof e=="object"&&e&&e.text?e.text:e,n.insertText(d,a,l)):(d=s,n.replaceWith(a,l,d)),t.updateSelection&&function(p,f,u){const m=p.steps.length-1;if(m<f)return;const g=p.steps[m];if(!(g instanceof ge||g instanceof ye))return;const y=p.mapping.maps[m];let C=0;y.forEach((w,v,x,O)=>{C===0&&(C=O)}),p.setSelection(J.near(p.doc.resolve(C),u))}(n,n.steps.length-1,-1),t.applyInputRules&&n.setMeta("applyInputRules",{from:a,text:d}),t.applyPasteRules&&n.setMeta("applyPasteRules",{from:a,text:d})}return!0},joinUp:()=>({state:r,dispatch:e})=>((t,n)=>{let o,i=t.selection,s=i instanceof V;if(s){if(i.node.isTextblock||!an(t.doc,i.from))return!1;o=i.from}else if(o=Xn(t.doc,i.from,-1),o==null)return!1;if(n){let a=t.tr.join(o);s&&a.setSelection(V.create(a.doc,o-t.doc.resolve(o).nodeBefore.nodeSize)),n(a.scrollIntoView())}return!0})(r,e),joinDown:()=>({state:r,dispatch:e})=>((t,n)=>{let o,i=t.selection;if(i instanceof V){if(i.node.isTextblock||!an(t.doc,i.to))return!1;o=i.to}else if(o=Xn(t.doc,i.to,1),o==null)return!1;return n&&n(t.tr.join(o).scrollIntoView()),!0})(r,e),joinBackward:()=>({state:r,dispatch:e})=>Qa(r,e),joinForward:()=>({state:r,dispatch:e})=>nl(r,e),joinItemBackward:()=>({tr:r,state:e,dispatch:t})=>{try{const n=Xn(e.doc,e.selection.$from.pos,-1);return n!=null&&(r.join(n,2),t&&t(r),!0)}catch{return!1}},joinItemForward:()=>({state:r,dispatch:e,tr:t})=>{try{const n=Xn(r.doc,r.selection.$from.pos,1);return n!=null&&(t.join(n,2),e&&e(t),!0)}catch{return!1}},joinTextblockBackward:()=>({state:r,dispatch:e})=>((t,n,o)=>{let i=Xa(t,o);if(!i)return!1;let s=ni(i);return!!s&&ks(t,s,n)})(r,e),joinTextblockForward:()=>({state:r,dispatch:e})=>((t,n,o)=>{let i=tl(t,o);if(!i)return!1;let s=ri(i);return!!s&&ks(t,s,n)})(r,e),keyboardShortcut:r=>({editor:e,view:t,tr:n,dispatch:o})=>{const i=function(c){const h=c.split(/-(?!$)/);let d,p,f,u,m=h[h.length-1];m==="Space"&&(m=" ");for(let g=0;g<h.length-1;g+=1){const y=h[g];if(/^(cmd|meta|m)$/i.test(y))u=!0;else if(/^a(lt)?$/i.test(y))d=!0;else if(/^(c|ctrl|control)$/i.test(y))p=!0;else if(/^s(hift)?$/i.test(y))f=!0;else{if(!/^mod$/i.test(y))throw new Error(`Unrecognized modifier name: ${y}`);Fo()||fl()?u=!0:p=!0}}return d&&(m=`Alt-${m}`),p&&(m=`Ctrl-${m}`),u&&(m=`Meta-${m}`),f&&(m=`Shift-${m}`),m}(r).split(/-(?!$)/),s=i.find(c=>!["Alt","Ctrl","Meta","Shift"].includes(c)),a=new KeyboardEvent("keydown",{key:s==="Space"?" ":s,altKey:i.includes("Alt"),ctrlKey:i.includes("Ctrl"),metaKey:i.includes("Meta"),shiftKey:i.includes("Shift"),bubbles:!0,cancelable:!0}),l=e.captureTransaction(()=>{t.someProp("handleKeyDown",c=>c(t,a))});return l==null||l.steps.forEach(c=>{const h=c.map(n.mapping);h&&o&&n.maybeStep(h)}),!0},lift:(r,e={})=>({state:t,dispatch:n})=>!!Bn(t,fe(r,t.schema),e)&&((o,i)=>{let{$from:s,$to:a}=o.selection,l=s.blockRange(a),c=l&&On(l);return c!=null&&(i&&i(o.tr.lift(l,c).scrollIntoView()),!0)})(t,n),liftEmptyBlock:()=>({state:r,dispatch:e})=>sl(r,e),liftListItem:r=>({state:e,dispatch:t})=>ph(fe(r,e.schema))(e,t),newlineInCode:()=>({state:r,dispatch:e})=>ol(r,e),resetAttributes:(r,e)=>({tr:t,state:n,dispatch:o})=>{let i=null,s=null;const a=Er(typeof r=="string"?r:r.name,n.schema);return!!a&&(a==="node"&&(i=fe(r,n.schema)),a==="mark"&&(s=Et(r,n.schema)),o&&t.selection.ranges.forEach(l=>{n.doc.nodesBetween(l.$from.pos,l.$to.pos,(c,h)=>{i&&i===c.type&&t.setNodeMarkup(h,void 0,Ds(c.attrs,e)),s&&c.marks.length&&c.marks.forEach(d=>{s===d.type&&t.addMark(h,h+c.nodeSize,s.create(Ds(d.attrs,e)))})})}),!0)},scrollIntoView:()=>({tr:r,dispatch:e})=>(e&&r.scrollIntoView(),!0),selectAll:()=>({tr:r,commands:e})=>e.setTextSelection({from:0,to:r.doc.content.size}),selectNodeBackward:()=>({state:r,dispatch:e})=>el(r,e),selectNodeForward:()=>({state:r,dispatch:e})=>rl(r,e),selectParentNode:()=>({state:r,dispatch:e})=>((t,n)=>{let o,{$from:i,to:s}=t.selection,a=i.sharedDepth(s);return a!=0&&(o=i.before(a),n&&n(t.tr.setSelection(V.create(t.doc,o))),!0)})(r,e),selectTextblockEnd:()=>({state:r,dispatch:e})=>hh(r,e),selectTextblockStart:()=>({state:r,dispatch:e})=>ch(r,e),setContent:(r,e=!1,t={})=>({tr:n,editor:o,dispatch:i})=>{const{doc:s}=n,a=ml(r,o.schema,t);return i&&n.replaceWith(0,s.content.size,a).setMeta("preventUpdate",!e),!0},setMark:(r,e={})=>({tr:t,state:n,dispatch:o})=>{const{selection:i}=t,{empty:s,ranges:a}=i,l=Et(r,n.schema);if(o)if(s){const c=gl(n,l);t.addStoredMark(l.create({...c,...e}))}else a.forEach(c=>{const h=c.$from.pos,d=c.$to.pos;n.doc.nodesBetween(h,d,(p,f)=>{const u=Math.max(f,h),m=Math.min(f+p.nodeSize,d);p.marks.find(g=>g.type===l)?p.marks.forEach(g=>{l===g.type&&t.addMark(u,m,l.create({...g.attrs,...e}))}):t.addMark(u,m,l.create(e))})});return function(c,h,d){var p;const{selection:f}=h;let u=null;if(Ls(f)&&(u=f.$cursor),u){const g=(p=c.storedMarks)!==null&&p!==void 0?p:u.marks();return!!d.isInSet(g)||!g.some(y=>y.type.excludes(d))}const{ranges:m}=f;return m.some(({$from:g,$to:y})=>{let C=g.depth===0&&c.doc.inlineContent&&c.doc.type.allowsMarkType(d);return c.doc.nodesBetween(g.pos,y.pos,(w,v,x)=>{if(C)return!1;if(w.isInline){const O=!x||x.type.allowsMarkType(d),E=!!d.isInSet(w.marks)||!w.marks.some(M=>M.type.excludes(d));C=O&&E}return!C}),C})}(n,t,l)},setMeta:(r,e)=>({tr:t})=>(t.setMeta(r,e),!0),setNode:(r,e={})=>({state:t,dispatch:n,chain:o})=>{const i=fe(r,t.schema);return i.isTextblock?o().command(({commands:s})=>!!Ms(i,e)(t)||s.clearNodes()).command(({state:s})=>Ms(i,e)(s,n)).run():(console.warn('[tiptap warn]: Currently "setNode()" only supports text block nodes.'),!1)},setNodeSelection:r=>({tr:e,dispatch:t})=>{if(t){const{doc:n}=e,o=Yt(r,0,n.content.size),i=V.create(n,o);e.setSelection(i)}return!0},setTextSelection:r=>({tr:e,dispatch:t})=>{if(t){const{doc:n}=e,{from:o,to:i}=typeof r=="number"?{from:r,to:r}:r,s=K.atStart(n).from,a=K.atEnd(n).to,l=Yt(o,s,a),c=Yt(i,s,a),h=K.create(n,l,c);e.setSelection(h)}return!0},sinkListItem:r=>({state:e,dispatch:t})=>{return(o=fe(r,e.schema),function(i,s){let{$from:a,$to:l}=i.selection,c=a.blockRange(l,f=>f.childCount>0&&f.firstChild.type==o);if(!c)return!1;let h=c.startIndex;if(h==0)return!1;let d=c.parent,p=d.child(h-1);if(p.type!=o)return!1;if(s){let f=p.lastChild&&p.lastChild.type==d.type,u=S.from(f?o.create():null),m=new D(S.from(o.create(null,S.from(d.type.create(null,u)))),f?3:1,0),g=c.start,y=c.end;s(i.tr.step(new ye(g-(f?3:1),y,g,y,m,1,!0)).scrollIntoView())}return!0})(e,t);var o},splitBlock:({keepMarks:r=!0}={})=>({tr:e,state:t,dispatch:n,editor:o})=>{const{selection:i,doc:s}=e,{$from:a,$to:l}=i,c=tr(o.extensionManager.attributes,a.node().type.name,a.node().attrs);if(i instanceof V&&i.node.isBlock)return!(!a.parentOffset||!gt(s,a.pos))&&(n&&(r&&_s(t,o.extensionManager.splittableMarks),e.split(a.pos).scrollIntoView()),!0);if(!a.parent.isBlock)return!1;if(n){const h=l.parentOffset===l.parent.content.size;i instanceof K&&e.deleteSelection();const d=a.depth===0?void 0:function(u){for(let m=0;m<u.edgeCount;m+=1){const{type:g}=u.edge(m);if(g.isTextblock&&!g.hasRequiredAttrs())return g}return null}(a.node(-1).contentMatchAt(a.indexAfter(-1)));let p=h&&d?[{type:d,attrs:c}]:void 0,f=gt(e.doc,e.mapping.map(a.pos),1,p);if(p||f||!gt(e.doc,e.mapping.map(a.pos),1,d?[{type:d}]:void 0)||(f=!0,p=d?[{type:d,attrs:c}]:void 0),f&&(e.split(e.mapping.map(a.pos),1,p),d&&!h&&!a.parentOffset&&a.parent.type!==d)){const u=e.mapping.map(a.before()),m=e.doc.resolve(u);a.node(-1).canReplaceWith(m.index(),m.index()+1,d)&&e.setNodeMarkup(e.mapping.map(a.before()),d)}r&&_s(t,o.extensionManager.splittableMarks),e.scrollIntoView()}return!0},splitListItem:r=>({tr:e,state:t,dispatch:n,editor:o})=>{var i;const s=fe(r,t.schema),{$from:a,$to:l}=t.selection,c=t.selection.node;if(c&&c.isBlock||a.depth<2||!a.sameParent(l))return!1;const h=a.node(-1);if(h.type!==s)return!1;const d=o.extensionManager.attributes;if(a.parent.content.size===0&&a.node(-1).childCount===a.indexAfter(-1)){if(a.depth===2||a.node(-3).type!==s||a.index(-2)!==a.node(-2).childCount-1)return!1;if(n){let g=S.empty;const y=a.index(-1)?1:a.index(-2)?2:3;for(let E=a.depth-y;E>=a.depth-3;E-=1)g=S.from(a.node(E).copy(g));const C=a.indexAfter(-1)<a.node(-2).childCount?1:a.indexAfter(-2)<a.node(-3).childCount?2:3,w=tr(d,a.node().type.name,a.node().attrs),v=((i=s.contentMatch.defaultType)===null||i===void 0?void 0:i.createAndFill(w))||void 0;g=g.append(S.from(s.createAndFill(null,v)||void 0));const x=a.before(a.depth-(y-1));e.replace(x,a.after(-C),new D(g,4-y,0));let O=-1;e.doc.nodesBetween(x,e.doc.content.size,(E,M)=>{if(O>-1)return!1;E.isTextblock&&E.content.size===0&&(O=M+1)}),O>-1&&e.setSelection(K.near(e.doc.resolve(O))),e.scrollIntoView()}return!0}const p=l.pos===a.end()?h.contentMatchAt(0).defaultType:null,f=tr(d,h.type.name,h.attrs),u=tr(d,a.node().type.name,a.node().attrs);e.delete(a.pos,l.pos);const m=p?[{type:s,attrs:f},{type:p,attrs:u}]:[{type:s,attrs:f}];if(!gt(e.doc,a.pos,2))return!1;if(n){const{selection:g,storedMarks:y}=t,{splittableMarks:C}=o.extensionManager,w=y||g.$to.parentOffset&&g.$from.marks();if(e.split(a.pos,2,m).scrollIntoView(),!w||!n)return!0;const v=w.filter(x=>C.includes(x.type.name));e.ensureMarks(v)}return!0},toggleList:(r,e,t,n={})=>({editor:o,tr:i,state:s,dispatch:a,chain:l,commands:c,can:h})=>{const{extensions:d,splittableMarks:p}=o.extensionManager,f=fe(r,s.schema),u=fe(e,s.schema),{selection:m,storedMarks:g}=s,{$from:y,$to:C}=m,w=y.blockRange(C),v=g||m.$to.parentOffset&&m.$from.marks();if(!w)return!1;const x=si(O=>Is(O.type.name,d))(m);if(w.depth>=1&&x&&w.depth-x.depth<=1){if(x.node.type===f)return c.liftListItem(u);if(Is(x.node.type.name,d)&&f.validContent(x.node.content)&&a)return l().command(()=>(i.setNodeMarkup(x.pos,f),!0)).command(()=>ho(i,f)).command(()=>po(i,f)).run()}return t&&v&&a?l().command(()=>{const O=h().wrapInList(f,n),E=v.filter(M=>p.includes(M.type.name));return i.ensureMarks(E),!!O||c.clearNodes()}).wrapInList(f,n).command(()=>ho(i,f)).command(()=>po(i,f)).run():l().command(()=>!!h().wrapInList(f,n)||c.clearNodes()).wrapInList(f,n).command(()=>ho(i,f)).command(()=>po(i,f)).run()},toggleMark:(r,e={},t={})=>({state:n,commands:o})=>{const{extendEmptyMarkRange:i=!1}=t,s=Et(r,n.schema);return Bo(n,s,e)?o.unsetMark(s,{extendEmptyMarkRange:i}):o.setMark(s,e)},toggleNode:(r,e,t={})=>({state:n,commands:o})=>{const i=fe(r,n.schema),s=fe(e,n.schema);return Bn(n,i,t)?o.setNode(s):o.setNode(i,t)},toggleWrap:(r,e={})=>({state:t,commands:n})=>{const o=fe(r,t.schema);return Bn(t,o,e)?n.lift(o):n.wrapIn(o,e)},undoInputRule:()=>({state:r,dispatch:e})=>{const t=r.plugins;for(let n=0;n<t.length;n+=1){const o=t[n];let i;if(o.spec.isInputRules&&(i=o.getState(r))){if(e){const s=r.tr,a=i.transform;for(let l=a.steps.length-1;l>=0;l-=1)s.step(a.steps[l].invert(a.docs[l]));if(i.text){const l=s.doc.resolve(i.from).marks();s.replaceWith(i.from,i.to,r.schema.text(i.text,l))}else s.delete(i.from,i.to)}return!0}}return!1},unsetAllMarks:()=>({tr:r,dispatch:e})=>{const{selection:t}=r,{empty:n,ranges:o}=t;return n||e&&o.forEach(i=>{r.removeMark(i.$from.pos,i.$to.pos)}),!0},unsetMark:(r,e={})=>({tr:t,state:n,dispatch:o})=>{var i;const{extendEmptyMarkRange:s=!1}=e,{selection:a}=t,l=Et(r,n.schema),{$from:c,empty:h,ranges:d}=a;if(!o)return!0;if(h&&s){let{from:p,to:f}=a;const u=(i=c.marks().find(g=>g.type===l))===null||i===void 0?void 0:i.attrs,m=Ns(c,l,u);m&&(p=m.from,f=m.to),t.removeMark(p,f,l)}else d.forEach(p=>{t.removeMark(p.$from.pos,p.$to.pos,l)});return t.removeStoredMark(l),!0},updateAttributes:(r,e={})=>({tr:t,state:n,dispatch:o})=>{let i=null,s=null;const a=Er(typeof r=="string"?r:r.name,n.schema);return!!a&&(a==="node"&&(i=fe(r,n.schema)),a==="mark"&&(s=Et(r,n.schema)),o&&t.selection.ranges.forEach(l=>{const c=l.$from.pos,h=l.$to.pos;n.doc.nodesBetween(c,h,(d,p)=>{i&&i===d.type&&t.setNodeMarkup(p,void 0,{...d.attrs,...e}),s&&d.marks.length&&d.marks.forEach(f=>{if(s===f.type){const u=Math.max(p,c),m=Math.min(p+d.nodeSize,h);t.addMark(u,m,s.create({...f.attrs,...e}))}})})}),!0)},wrapIn:(r,e={})=>({state:t,dispatch:n})=>function(o,i=null){return function(s,a){let{$from:l,$to:c}=s.selection,h=l.blockRange(c),d=h&&va(h,o,i);return!!d&&(a&&a(s.tr.wrap(h,d).scrollIntoView()),!0)}}(fe(r,t.schema),e)(t,n),wrapInList:(r,e={})=>({state:t,dispatch:n})=>dh(fe(r,t.schema),e)(t,n)});const Mh=Ue.create({name:"commands",addCommands:()=>({...kh})}),Sh=Ue.create({name:"editable",addProseMirrorPlugins(){return[new He({key:new Ve("editable"),props:{editable:()=>this.editor.options.editable}})]}}),Oh=Ue.create({name:"focusEvents",addProseMirrorPlugins(){const{editor:r}=this;return[new He({key:new Ve("focusEvents"),props:{handleDOMEvents:{focus:(e,t)=>{r.isFocused=!0;const n=r.state.tr.setMeta("focus",{event:t}).setMeta("addToHistory",!1);return e.dispatch(n),!1},blur:(e,t)=>{r.isFocused=!1;const n=r.state.tr.setMeta("blur",{event:t}).setMeta("addToHistory",!1);return e.dispatch(n),!1}}}})]}}),Eh=Ue.create({name:"keymap",addKeyboardShortcuts(){const r=()=>this.editor.commands.first(({commands:i})=>[()=>i.undoInputRule(),()=>i.command(({tr:s})=>{const{selection:a,doc:l}=s,{empty:c,$anchor:h}=a,{pos:d,parent:p}=h,f=h.parent.isTextblock&&d>0?s.doc.resolve(d-1):h,u=f.parent.type.spec.isolating,m=h.pos-h.parentOffset,g=u&&f.parent.childCount===1?m===h.pos:J.atStart(l).from===d;return!(!c||!p.type.isTextblock||p.textContent.length||!g||g&&h.parent.type.name==="paragraph")&&i.clearNodes()}),()=>i.deleteSelection(),()=>i.joinBackward(),()=>i.selectNodeBackward()]),e=()=>this.editor.commands.first(({commands:i})=>[()=>i.deleteSelection(),()=>i.deleteCurrentNode(),()=>i.joinForward(),()=>i.selectNodeForward()]),t={Enter:()=>this.editor.commands.first(({commands:i})=>[()=>i.newlineInCode(),()=>i.createParagraphNear(),()=>i.liftEmptyBlock(),()=>i.splitBlock()]),"Mod-Enter":()=>this.editor.commands.exitCode(),Backspace:r,"Mod-Backspace":r,"Shift-Backspace":r,Delete:e,"Mod-Delete":e,"Mod-a":()=>this.editor.commands.selectAll()},n={...t},o={...t,"Ctrl-h":r,"Alt-Backspace":r,"Ctrl-d":e,"Ctrl-Alt-Backspace":e,"Alt-Delete":e,"Alt-d":e,"Ctrl-a":()=>this.editor.commands.selectTextblockStart(),"Ctrl-e":()=>this.editor.commands.selectTextblockEnd()};return Fo()||fl()?o:n},addProseMirrorPlugins(){return[new He({key:new Ve("clearDocument"),appendTransaction:(r,e,t)=>{if(!(r.some(d=>d.docChanged)&&!e.doc.eq(t.doc)))return;const{empty:n,from:o,to:i}=e.selection,s=J.atStart(e.doc).from,a=J.atEnd(e.doc).to;if(n||!(o===s&&i===a)||t.doc.textBetween(0,t.doc.content.size," "," ").length!==0)return;const l=t.tr,c=$r({state:t,transaction:l}),{commands:h}=new Rr({editor:this.editor,state:c});return h.clearNodes(),l.steps.length?l:void 0}})]}}),Th=Ue.create({name:"tabindex",addProseMirrorPlugins(){return[new He({key:new Ve("tabindex"),props:{attributes:this.editor.isEditable?{tabindex:"0"}:{}}})]}});class jt{constructor(e,t,n=!1,o=null){this.currentNode=null,this.actualDepth=null,this.isBlock=n,this.resolvedPos=e,this.editor=t,this.currentNode=o}get name(){return this.node.type.name}get node(){return this.currentNode||this.resolvedPos.node()}get element(){return this.editor.view.domAtPos(this.pos).node}get depth(){var e;return(e=this.actualDepth)!==null&&e!==void 0?e:this.resolvedPos.depth}get pos(){return this.resolvedPos.pos}get content(){return this.node.content}set content(e){let t=this.from,n=this.to;if(this.isBlock){if(this.content.size===0)return void console.error(`You can’t set content on a block node. Tried to set content on ${this.name} at ${this.pos}`);t=this.from+1,n=this.to-1}this.editor.commands.insertContentAt({from:t,to:n},e)}get attributes(){return this.node.attrs}get textContent(){return this.node.textContent}get size(){return this.node.nodeSize}get from(){return this.isBlock?this.pos:this.resolvedPos.start(this.resolvedPos.depth)}get range(){return{from:this.from,to:this.to}}get to(){return this.isBlock?this.pos+this.size:this.resolvedPos.end(this.resolvedPos.depth)+(this.node.isText?0:1)}get parent(){if(this.depth===0)return null;const e=this.resolvedPos.start(this.resolvedPos.depth-1),t=this.resolvedPos.doc.resolve(e);return new jt(t,this.editor)}get before(){let e=this.resolvedPos.doc.resolve(this.from-(this.isBlock?1:2));return e.depth!==this.depth&&(e=this.resolvedPos.doc.resolve(this.from-3)),new jt(e,this.editor)}get after(){let e=this.resolvedPos.doc.resolve(this.to+(this.isBlock?2:1));return e.depth!==this.depth&&(e=this.resolvedPos.doc.resolve(this.to+3)),new jt(e,this.editor)}get children(){const e=[];return this.node.content.forEach((t,n)=>{const o=t.isBlock&&!t.isTextblock,i=this.pos+n+1,s=this.resolvedPos.doc.resolve(i);if(!o&&s.depth<=this.depth)return;const a=new jt(s,this.editor,o,o?t:null);o&&(a.actualDepth=this.depth+1),e.push(new jt(s,this.editor,o,o?t:null))}),e}get firstChild(){return this.children[0]||null}get lastChild(){const e=this.children;return e[e.length-1]||null}closest(e,t={}){let n=null,o=this.parent;for(;o&&!n;){if(o.node.type.name===e)if(Object.keys(t).length>0){const i=o.node.attrs,s=Object.keys(t);for(let a=0;a<s.length;a+=1){const l=s[a];if(i[l]!==t[l])break}}else n=o;o=o.parent}return n}querySelector(e,t={}){return this.querySelectorAll(e,t,!0)[0]||null}querySelectorAll(e,t={},n=!1){let o=[];if(!this.children||this.children.length===0)return o;const i=Object.keys(t);return this.children.forEach(s=>{n&&o.length>0||(s.node.type.name===e&&i.every(a=>t[a]===s.node.attrs[a])&&o.push(s),n&&o.length>0||(o=o.concat(s.querySelectorAll(e,t,n))))}),o}setAttribute(e){const t=this.editor.state.selection;this.editor.chain().setTextSelection(this.from).updateAttributes(this.node.type.name,e).setTextSelection(t.from).run()}}class Nh extends uh{constructor(e={}){super(),this.isFocused=!1,this.extensionStorage={},this.options={element:document.createElement("div"),content:"",injectCSS:!0,injectNonce:void 0,extensions:[],autofocus:!1,editable:!0,editorProps:{},parseOptions:{},coreExtensionOptions:{},enableInputRules:!0,enablePasteRules:!0,enableCoreExtensions:!0,onBeforeCreate:()=>null,onCreate:()=>null,onUpdate:()=>null,onSelectionUpdate:()=>null,onTransaction:()=>null,onFocus:()=>null,onBlur:()=>null,onDestroy:()=>null},this.isCapturingTransaction=!1,this.capturedTransaction=null,this.setOptions(e),this.createExtensionManager(),this.createCommandManager(),this.createSchema(),this.on("beforeCreate",this.options.onBeforeCreate),this.emit("beforeCreate",{editor:this}),this.createView(),this.injectCSS(),this.on("create",this.options.onCreate),this.on("update",this.options.onUpdate),this.on("selectionUpdate",this.options.onSelectionUpdate),this.on("transaction",this.options.onTransaction),this.on("focus",this.options.onFocus),this.on("blur",this.options.onBlur),this.on("destroy",this.options.onDestroy),window.setTimeout(()=>{this.isDestroyed||(this.commands.focus(this.options.autofocus),this.emit("create",{editor:this}))},0)}get storage(){return this.extensionStorage}get commands(){return this.commandManager.commands}chain(){return this.commandManager.chain()}can(){return this.commandManager.can()}injectCSS(){this.options.injectCSS&&document&&(this.css=function(e,t,n){const o=document.querySelector("style[data-tiptap-style]");if(o!==null)return o;const i=document.createElement("style");return t&&i.setAttribute("nonce",t),i.setAttribute("data-tiptap-style",""),i.innerHTML=e,document.getElementsByTagName("head")[0].appendChild(i),i}(`.ProseMirror {
  position: relative;
}

.ProseMirror {
  word-wrap: break-word;
  white-space: pre-wrap;
  white-space: break-spaces;
  -webkit-font-variant-ligatures: none;
  font-variant-ligatures: none;
  font-feature-settings: "liga" 0; /* the above doesn't seem to work in Edge */
}

.ProseMirror [contenteditable="false"] {
  white-space: normal;
}

.ProseMirror [contenteditable="false"] [contenteditable="true"] {
  white-space: pre-wrap;
}

.ProseMirror pre {
  white-space: pre-wrap;
}

img.ProseMirror-separator {
  display: inline !important;
  border: none !important;
  margin: 0 !important;
  width: 1px !important;
  height: 1px !important;
}

.ProseMirror-gapcursor {
  display: none;
  pointer-events: none;
  position: absolute;
  margin: 0;
}

.ProseMirror-gapcursor:after {
  content: "";
  display: block;
  position: absolute;
  top: -2px;
  width: 20px;
  border-top: 1px solid black;
  animation: ProseMirror-cursor-blink 1.1s steps(2, start) infinite;
}

@keyframes ProseMirror-cursor-blink {
  to {
    visibility: hidden;
  }
}

.ProseMirror-hideselection *::selection {
  background: transparent;
}

.ProseMirror-hideselection *::-moz-selection {
  background: transparent;
}

.ProseMirror-hideselection * {
  caret-color: transparent;
}

.ProseMirror-focused .ProseMirror-gapcursor {
  display: block;
}

.tippy-box[data-animation=fade][data-state=hidden] {
  opacity: 0
}`,this.options.injectNonce))}setOptions(e={}){this.options={...this.options,...e},this.view&&this.state&&!this.isDestroyed&&(this.options.editorProps&&this.view.setProps(this.options.editorProps),this.view.updateState(this.state))}setEditable(e,t=!0){this.setOptions({editable:e}),t&&this.emit("update",{editor:this,transaction:this.state.tr})}get isEditable(){return this.options.editable&&this.view&&this.view.editable}get state(){return this.view.state}registerPlugin(e,t){const n=cl(t)?t(e,[...this.state.plugins]):[...this.state.plugins,e],o=this.state.reconfigure({plugins:n});this.view.updateState(o)}unregisterPlugin(e){if(this.isDestroyed)return;const t=typeof e=="string"?`${e}$`:e.key,n=this.state.reconfigure({plugins:this.state.plugins.filter(o=>!o.key.startsWith(t))});this.view.updateState(n)}createExtensionManager(){var e,t;const n=[...this.options.enableCoreExtensions?[Sh,wh.configure({blockSeparator:(t=(e=this.options.coreExtensionOptions)===null||e===void 0?void 0:e.clipboardTextSerializer)===null||t===void 0?void 0:t.blockSeparator}),Mh,Oh,Eh,Th]:[],...this.options.extensions].filter(o=>["extension","node","mark"].includes(o==null?void 0:o.type));this.extensionManager=new vn(n,this)}createCommandManager(){this.commandManager=new Rr({editor:this})}createSchema(){this.schema=this.extensionManager.schema}createView(){const e=ml(this.options.content,this.schema,this.options.parseOptions),t=pl(e,this.options.autofocus);this.view=new rh(this.options.element,{...this.options.editorProps,dispatchTransaction:this.dispatchTransaction.bind(this),state:Cn.create({doc:e,selection:t||void 0})});const n=this.state.reconfigure({plugins:this.extensionManager.plugins});this.view.updateState(n),this.createNodeViews(),this.prependClass(),this.view.dom.editor=this}createNodeViews(){this.view.setProps({nodeViews:this.extensionManager.nodeViews})}prependClass(){this.view.dom.className=`tiptap ${this.view.dom.className}`}captureTransaction(e){this.isCapturingTransaction=!0,e(),this.isCapturingTransaction=!1;const t=this.capturedTransaction;return this.capturedTransaction=null,t}dispatchTransaction(e){if(this.view.isDestroyed)return;if(this.isCapturingTransaction)return this.capturedTransaction?void e.steps.forEach(s=>{var a;return(a=this.capturedTransaction)===null||a===void 0?void 0:a.step(s)}):void(this.capturedTransaction=e);const t=this.state.apply(e),n=!this.state.selection.eq(t.selection);this.view.updateState(t),this.emit("transaction",{editor:this,transaction:e}),n&&this.emit("selectionUpdate",{editor:this,transaction:e});const o=e.getMeta("focus"),i=e.getMeta("blur");o&&this.emit("focus",{editor:this,event:o.event,transaction:e}),i&&this.emit("blur",{editor:this,event:i.event,transaction:e}),e.docChanged&&!e.getMeta("preventUpdate")&&this.emit("update",{editor:this,transaction:e})}getAttributes(e){return xh(this.state,e)}isActive(e,t){const n=typeof e=="string"?e:null,o=typeof e=="string"?t:e;return function(i,s,a={}){if(!s)return Bn(i,null,a)||Bo(i,null,a);const l=Er(s,i.schema);return l==="node"?Bn(i,s,a):l==="mark"&&Bo(i,s,a)}(this.state,n,o)}getJSON(){return this.state.doc.toJSON()}getHTML(){return function(e,t){const n=cn.fromSchema(t).serializeFragment(e),o=document.implementation.createHTMLDocument().createElement("div");return o.appendChild(n),o.innerHTML}(this.state.doc.content,this.schema)}getText(e){const{blockSeparator:t=`

`,textSerializers:n={}}=e||{};return function(o,i){return hl(o,{from:0,to:o.content.size},i)}(this.state.doc,{blockSeparator:t,textSerializers:{...dl(this.schema),...n}})}get isEmpty(){return function(e){var t;const n=(t=e.type.createAndFill())===null||t===void 0?void 0:t.toJSON(),o=e.toJSON();return JSON.stringify(n)===JSON.stringify(o)}(this.state.doc)}getCharacterCount(){return console.warn('[tiptap warn]: "editor.getCharacterCount()" is deprecated. Please use "editor.storage.characterCount.characters()" instead.'),this.state.doc.content.size-2}destroy(){this.emit("destroy"),this.view&&this.view.destroy(),this.removeAllListeners()}get isDestroyed(){var e;return!(!((e=this.view)===null||e===void 0)&&e.docView)}$node(e,t){var n;return((n=this.$doc)===null||n===void 0?void 0:n.querySelector(e,t))||null}$nodes(e,t){var n;return((n=this.$doc)===null||n===void 0?void 0:n.querySelectorAll(e,t))||null}$pos(e){const t=this.state.doc.resolve(e);return new jt(t,this)}get $doc(){return this.$pos(0)}}function bp(r){return new mh({find:r.find,handler:({state:e,range:t,match:n})=>{const o=W(r.getAttributes,void 0,n)||{},{tr:i}=e,s=t.from;let a=t.to;const l=r.type.create(o);if(n[1]){let c=s+n[0].lastIndexOf(n[1]);c>a?c=a:a=c+n[1].length;const h=n[0][n[0].length-1];i.insertText(h,s+n[0].length-1),i.replaceWith(c,a,l)}else n[0]&&i.insert(s-1,r.type.create(o)).delete(i.mapping.map(s),i.mapping.map(a));i.scrollIntoView()}})}class Tr{constructor(e={}){this.type="mark",this.name="mark",this.parent=null,this.child=null,this.config={name:this.name,defaultOptions:{}},this.config={...this.config,...e},this.name=this.config.name,e.defaultOptions&&Object.keys(e.defaultOptions).length>0&&console.warn(`[tiptap warn]: BREAKING CHANGE: "defaultOptions" is deprecated. Please use "addOptions" instead. Found in extension: "${this.name}".`),this.options=this.config.defaultOptions,this.config.addOptions&&(this.options=W($(this,"addOptions",{name:this.name}))),this.storage=W($(this,"addStorage",{name:this.name,options:this.options}))||{}}static create(e={}){return new Tr(e)}configure(e={}){const t=this.extend();return t.options=Pr(this.options,e),t.storage=W($(t,"addStorage",{name:t.name,options:t.options})),t}extend(e={}){const t=new Tr({...this.config,...e});return t.parent=this,this.child=t,t.name=e.name?e.name:t.parent.name,e.defaultOptions&&console.warn(`[tiptap warn]: BREAKING CHANGE: "defaultOptions" is deprecated. Please use "addOptions" instead. Found in extension: "${t.name}".`),t.options=W($(t,"addOptions",{name:t.name})),t.storage=W($(t,"addStorage",{name:t.name,options:t.options})),t}static handleExit({editor:e,mark:t}){const{tr:n}=e.state,o=e.state.selection.$from;if(o.pos===o.end()){const i=o.marks();if(!i.find(a=>(a==null?void 0:a.type.name)===t.name))return!1;const s=i.find(a=>(a==null?void 0:a.type.name)===t.name);return s&&n.removeStoredMark(s),n.insertText(" ",o.pos),e.view.dispatch(n),!0}return!1}}class Ft{constructor(e={}){this.type="node",this.name="node",this.parent=null,this.child=null,this.config={name:this.name,defaultOptions:{}},this.config={...this.config,...e},this.name=this.config.name,e.defaultOptions&&Object.keys(e.defaultOptions).length>0&&console.warn(`[tiptap warn]: BREAKING CHANGE: "defaultOptions" is deprecated. Please use "addOptions" instead. Found in extension: "${this.name}".`),this.options=this.config.defaultOptions,this.config.addOptions&&(this.options=W($(this,"addOptions",{name:this.name}))),this.storage=W($(this,"addStorage",{name:this.name,options:this.options}))||{}}static create(e={}){return new Ft(e)}configure(e={}){const t=this.extend();return t.options=Pr(this.options,e),t.storage=W($(t,"addStorage",{name:t.name,options:t.options})),t}extend(e={}){const t=new Ft({...this.config,...e});return t.parent=this,this.child=t,t.name=e.name?e.name:t.parent.name,e.defaultOptions&&console.warn(`[tiptap warn]: BREAKING CHANGE: "defaultOptions" is deprecated. Please use "addOptions" instead. Found in extension: "${t.name}".`),t.options=W($(t,"addOptions",{name:t.name})),t.storage=W($(t,"addStorage",{name:t.name,options:t.options})),t}}const Lh=Ft.create({name:"paragraph",priority:1e3,addOptions:()=>({HTMLAttributes:{}}),group:"block",content:"inline*",parseHTML:()=>[{tag:"p"}],renderHTML({HTMLAttributes:r}){return["p",Zt(this.options.HTMLAttributes,r),0]},addCommands(){return{setParagraph:()=>({commands:r})=>r.setNode(this.name)}},addKeyboardShortcuts(){return{"Mod-Alt-0":()=>this.editor.commands.setParagraph()}}}),Ah=Ft.create({name:"hardBreak",addOptions:()=>({keepMarks:!0,HTMLAttributes:{}}),inline:!0,group:"inline",selectable:!1,parseHTML:()=>[{tag:"br"}],renderHTML({HTMLAttributes:r}){return["br",Zt(this.options.HTMLAttributes,r)]},renderText:()=>`
`,addCommands(){return{setHardBreak:()=>({commands:r,chain:e,state:t,editor:n})=>r.first([()=>r.exitCode(),()=>r.command(()=>{const{selection:o,storedMarks:i}=t;if(o.$from.parent.type.spec.isolating)return!1;const{keepMarks:s}=this.options,{splittableMarks:a}=n.extensionManager,l=i||o.$to.parentOffset&&o.$from.marks();return e().insertContent({type:this.name}).command(({tr:c,dispatch:h})=>{if(h&&l&&s){const d=l.filter(p=>a.includes(p.type.name));c.ensureMarks(d)}return!0}).run()})])}},addKeyboardShortcuts(){return{"Mod-Enter":()=>this.editor.commands.setHardBreak(),"Shift-Enter":()=>this.editor.commands.setHardBreak()}}}),Dh=Ft.create({name:"text",group:"inline"}),Ih=Ft.create({name:"doc",topNode:!0,content:"block+"});var Nr=200,me=function(){};me.prototype.append=function(r){return r.length?(r=me.from(r),!this.length&&r||r.length<Nr&&this.leafAppend(r)||this.length<Nr&&r.leafPrepend(this)||this.appendInner(r)):this},me.prototype.prepend=function(r){return r.length?me.from(r).append(this):this},me.prototype.appendInner=function(r){return new _h(this,r)},me.prototype.slice=function(r,e){return r===void 0&&(r=0),e===void 0&&(e=this.length),r>=e?me.empty:this.sliceInner(Math.max(0,r),Math.min(this.length,e))},me.prototype.get=function(r){if(!(r<0||r>=this.length))return this.getInner(r)},me.prototype.forEach=function(r,e,t){e===void 0&&(e=0),t===void 0&&(t=this.length),e<=t?this.forEachInner(r,e,t,0):this.forEachInvertedInner(r,e,t,0)},me.prototype.map=function(r,e,t){e===void 0&&(e=0),t===void 0&&(t=this.length);var n=[];return this.forEach(function(o,i){return n.push(r(o,i))},e,t),n},me.from=function(r){return r instanceof me?r:r&&r.length?new yl(r):me.empty};var yl=function(r){function e(n){r.call(this),this.values=n}r&&(e.__proto__=r),e.prototype=Object.create(r&&r.prototype),e.prototype.constructor=e;var t={length:{configurable:!0},depth:{configurable:!0}};return e.prototype.flatten=function(){return this.values},e.prototype.sliceInner=function(n,o){return n==0&&o==this.length?this:new e(this.values.slice(n,o))},e.prototype.getInner=function(n){return this.values[n]},e.prototype.forEachInner=function(n,o,i,s){for(var a=o;a<i;a++)if(n(this.values[a],s+a)===!1)return!1},e.prototype.forEachInvertedInner=function(n,o,i,s){for(var a=o-1;a>=i;a--)if(n(this.values[a],s+a)===!1)return!1},e.prototype.leafAppend=function(n){if(this.length+n.length<=Nr)return new e(this.values.concat(n.flatten()))},e.prototype.leafPrepend=function(n){if(this.length+n.length<=Nr)return new e(n.flatten().concat(this.values))},t.length.get=function(){return this.values.length},t.depth.get=function(){return 0},Object.defineProperties(e.prototype,t),e}(me);me.empty=new yl([]);var _h=function(r){function e(t,n){r.call(this),this.left=t,this.right=n,this.length=t.length+n.length,this.depth=Math.max(t.depth,n.depth)+1}return r&&(e.__proto__=r),e.prototype=Object.create(r&&r.prototype),e.prototype.constructor=e,e.prototype.flatten=function(){return this.left.flatten().concat(this.right.flatten())},e.prototype.getInner=function(t){return t<this.left.length?this.left.get(t):this.right.get(t-this.left.length)},e.prototype.forEachInner=function(t,n,o,i){var s=this.left.length;return!(n<s&&this.left.forEachInner(t,n,Math.min(o,s),i)===!1)&&!(o>s&&this.right.forEachInner(t,Math.max(n-s,0),Math.min(this.length,o)-s,i+s)===!1)&&void 0},e.prototype.forEachInvertedInner=function(t,n,o,i){var s=this.left.length;return!(n>s&&this.right.forEachInvertedInner(t,n-s,Math.max(o,s)-s,i+s)===!1)&&!(o<s&&this.left.forEachInvertedInner(t,Math.min(n,s),o,i)===!1)&&void 0},e.prototype.sliceInner=function(t,n){if(t==0&&n==this.length)return this;var o=this.left.length;return n<=o?this.left.slice(t,n):t>=o?this.right.slice(t-o,n-o):this.left.slice(t,o).append(this.right.slice(0,n-o))},e.prototype.leafAppend=function(t){var n=this.right.leafAppend(t);if(n)return new e(this.left,n)},e.prototype.leafPrepend=function(t){var n=this.left.leafPrepend(t);if(n)return new e(n,this.right)},e.prototype.appendInner=function(t){return this.left.depth>=Math.max(this.right.depth,t.depth)+1?new e(this.left,new e(this.right,t)):new e(this,t)},e}(me);class Xe{constructor(e,t){this.items=e,this.eventCount=t}popEvent(e,t){if(this.eventCount==0)return null;let n,o,i=this.items.length;for(;;i--)if(this.items.get(i-1).selection){--i;break}t&&(n=this.remapping(i,this.items.length),o=n.maps.length);let s,a,l=e.tr,c=[],h=[];return this.items.forEach((d,p)=>{if(!d.step)return n||(n=this.remapping(i,p+1),o=n.maps.length),o--,void h.push(d);if(n){h.push(new rt(d.map));let f,u=d.step.map(n.slice(o));u&&l.maybeStep(u).doc&&(f=l.mapping.maps[l.mapping.maps.length-1],c.push(new rt(f,void 0,void 0,c.length+h.length))),o--,f&&n.appendMap(f,o)}else l.maybeStep(d.step);return d.selection?(s=n?d.selection.map(n.slice(o)):d.selection,a=new Xe(this.items.slice(0,i).append(h.reverse().concat(c)),this.eventCount-1),!1):void 0},this.items.length,0),{remaining:a,transform:l,selection:s}}addTransform(e,t,n,o){let i=[],s=this.eventCount,a=this.items,l=!o&&a.length?a.get(a.length-1):null;for(let h=0;h<e.steps.length;h++){let d,p=e.steps[h].invert(e.docs[h]),f=new rt(e.mapping.maps[h],p,t);(d=l&&l.merge(f))&&(f=d,h?i.pop():a=a.slice(0,a.length-1)),i.push(f),t&&(s++,t=void 0),o||(l=f)}let c=s-n.depth;return c>$h&&(a=function(h,d){let p;return h.forEach((f,u)=>{if(f.selection&&d--==0)return p=u,!1}),h.slice(p)}(a,c),s-=c),new Xe(a.append(i),s)}remapping(e,t){let n=new Hn;return this.items.forEach((o,i)=>{let s=o.mirrorOffset!=null&&i-o.mirrorOffset>=e?n.maps.length-o.mirrorOffset:void 0;n.appendMap(o.map,s)},e,t),n}addMaps(e){return this.eventCount==0?this:new Xe(this.items.append(e.map(t=>new rt(t))),this.eventCount)}rebased(e,t){if(!this.eventCount)return this;let n=[],o=Math.max(0,this.items.length-t),i=e.mapping,s=e.steps.length,a=this.eventCount;this.items.forEach(p=>{p.selection&&a--},o);let l=t;this.items.forEach(p=>{let f=i.getMirror(--l);if(f==null)return;s=Math.min(s,f);let u=i.maps[f];if(p.step){let m=e.steps[f].invert(e.docs[f]),g=p.selection&&p.selection.map(i.slice(l+1,f));g&&a++,n.push(new rt(u,m,g))}else n.push(new rt(u))},o);let c=[];for(let p=t;p<s;p++)c.push(new rt(i.maps[p]));let h=this.items.slice(0,o).append(c).append(n),d=new Xe(h,a);return d.emptyItemCount()>500&&(d=d.compress(this.items.length-n.length)),d}emptyItemCount(){let e=0;return this.items.forEach(t=>{t.step||e++}),e}compress(e=this.items.length){let t=this.remapping(0,e),n=t.maps.length,o=[],i=0;return this.items.forEach((s,a)=>{if(a>=e)o.push(s),s.selection&&i++;else if(s.step){let l=s.step.map(t.slice(n)),c=l&&l.getMap();if(n--,c&&t.appendMap(c,n),l){let h=s.selection&&s.selection.map(t.slice(n));h&&i++;let d,p=new rt(c.invert(),l,h),f=o.length-1;(d=o.length&&o[f].merge(p))?o[f]=d:o.push(p)}}else s.map&&n--},this.items.length,0),new Xe(me.from(o.reverse()),i)}}Xe.empty=new Xe(me.empty,0);class rt{constructor(e,t,n,o){this.map=e,this.step=t,this.selection=n,this.mirrorOffset=o}merge(e){if(this.step&&e.step&&!e.selection){let t=e.step.merge(this.step);if(t)return new rt(t.getMap().invert(),t,this.selection)}}}class Mt{constructor(e,t,n,o,i){this.done=e,this.undone=t,this.prevRanges=n,this.prevTime=o,this.prevComposition=i}}const $h=20;function $s(r){let e=[];return r.forEach((t,n,o,i)=>e.push(o,i)),e}function uo(r,e){if(!r)return null;let t=[];for(let n=0;n<r.length;n+=2){let o=e.map(r[n],1),i=e.map(r[n+1],-1);o<=i&&t.push(o,i)}return t}let fo=!1,Rs=null;function ir(r){let e=r.plugins;if(Rs!=e){fo=!1,Rs=e;for(let t=0;t<e.length;t++)if(e[t].spec.historyPreserveItems){fo=!0;break}}return fo}const Xt=new Ve("history"),Rh=new Ve("closeHistory");function Ph(r={}){return r={depth:r.depth||100,newGroupDelay:r.newGroupDelay||500},new He({key:Xt,state:{init:()=>new Mt(Xe.empty,Xe.empty,null,0,-1),apply:(e,t,n)=>function(o,i,s,a){let l,c=s.getMeta(Xt);if(c)return c.historyState;s.getMeta(Rh)&&(o=new Mt(o.done,o.undone,null,0,-1));let h=s.getMeta("appendedTransaction");if(s.steps.length==0)return o;if(h&&h.getMeta(Xt))return h.getMeta(Xt).redo?new Mt(o.done.addTransform(s,void 0,a,ir(i)),o.undone,$s(s.mapping.maps[s.steps.length-1]),o.prevTime,o.prevComposition):new Mt(o.done,o.undone.addTransform(s,void 0,a,ir(i)),null,o.prevTime,o.prevComposition);if(s.getMeta("addToHistory")===!1||h&&h.getMeta("addToHistory")===!1)return(l=s.getMeta("rebased"))?new Mt(o.done.rebased(s,l),o.undone.rebased(s,l),uo(o.prevRanges,s.mapping),o.prevTime,o.prevComposition):new Mt(o.done.addMaps(s.mapping.maps),o.undone.addMaps(s.mapping.maps),uo(o.prevRanges,s.mapping),o.prevTime,o.prevComposition);{let d=s.getMeta("composition"),p=o.prevTime==0||!h&&o.prevComposition!=d&&(o.prevTime<(s.time||0)-a.newGroupDelay||!function(u,m){if(!m)return!1;if(!u.docChanged)return!0;let g=!1;return u.mapping.maps[0].forEach((y,C)=>{for(let w=0;w<m.length;w+=2)y<=m[w+1]&&C>=m[w]&&(g=!0)}),g}(s,o.prevRanges)),f=h?uo(o.prevRanges,s.mapping):$s(s.mapping.maps[s.steps.length-1]);return new Mt(o.done.addTransform(s,p?i.selection.getBookmark():void 0,a,ir(i)),Xe.empty,f,s.time,d??o.prevComposition)}}(t,n,e,r)},config:r,props:{handleDOMEvents:{beforeinput(e,t){let n=t.inputType,o=n=="historyUndo"?vl:n=="historyRedo"?wl:null;return!!o&&(t.preventDefault(),o(e.state,e.dispatch))}}}})}function Cl(r,e){return(t,n)=>{let o=Xt.getState(t);if(!o||(r?o.undone:o.done).eventCount==0)return!1;if(n){let i=function(s,a,l){let c=ir(a),h=Xt.get(a).spec.config,d=(l?s.undone:s.done).popEvent(a,c);if(!d)return null;let p=d.selection.resolve(d.transform.doc),f=(l?s.done:s.undone).addTransform(d.transform,a.selection.getBookmark(),h,c),u=new Mt(l?f:d.remaining,l?d.remaining:f,null,0,-1);return d.transform.setSelection(p).setMeta(Xt,{redo:l,historyState:u})}(o,t,r);i&&n(e?i.scrollIntoView():i)}return!0}}const vl=Cl(!1,!0),wl=Cl(!0,!0),zh=Ue.create({name:"history",addOptions:()=>({depth:100,newGroupDelay:500}),addCommands:()=>({undo:()=>({state:r,dispatch:e})=>vl(r,e),redo:()=>({state:r,dispatch:e})=>wl(r,e)}),addProseMirrorPlugins(){return[Ph(this.options)]},addKeyboardShortcuts(){return{"Mod-z":()=>this.editor.commands.undo(),"Shift-Mod-z":()=>this.editor.commands.redo(),"Mod-y":()=>this.editor.commands.redo(),"Mod-я":()=>this.editor.commands.undo(),"Shift-Mod-я":()=>this.editor.commands.redo()}}});class Fh{constructor(e){b(this,"_registeredPlugins",Te([]));b(this,"_defaultPlugins");b(this,"_allPlugins");b(this,"_getDefaultPlugins",e=>[Ih.extend({addKeyboardShortcuts:()=>({...e.keyboardShortcuts})}),Lh,Dh,Ah,zh.configure({depth:100})].map(t=>({tipTapExtension:t})));b(this,"registerPlugin",e=>(this._registeredPlugins.update(t=>[...t,e]),()=>{this._registeredPlugins.update(t=>t.filter(n=>n!==e))}));b(this,"onPluginsChanged",e=>this._allPlugins.subscribe(e));this._opts=e,this._defaultPlugins=Ge(this._opts,this._getDefaultPlugins),this._allPlugins=Ge([this._defaultPlugins,this._registeredPlugins],([t,n])=>[...t,...n])}get tipTapExtensions(){return ce(this._allPlugins).map(e=>e.tipTapExtension)}}function Ps(r){return r.length===0?[]:[{type:"text",text:r}]}function zs(r){if(r!==void 0){if(typeof r=="string"){const e=r.split(`
`).map((t,n)=>n===0?Ps(t):[{type:"hardBreak"},...Ps(t)]).flat();return e.length>0?e:void 0}return Array.isArray(r)?r.length>0?r:void 0:r}}class Bh{constructor(e){b(this,"_setupFns",[]);b(this,"_editor");b(this,"registerEditor",e=>{this._editor=e,this._runSetupFns()});b(this,"unregisterEditor",()=>{this._editor=void 0});b(this,"can",()=>{var e;return(e=this._editor)==null?void 0:e.can()});b(this,"chain",()=>{var e;return(e=this._editor)==null?void 0:e.chain()});b(this,"_queueOrRun",e=>{this._editor?e():this._setupFns.push(e)});b(this,"_runSetupFns",()=>{this._setupFns.forEach(e=>{e()}),this._setupFns=[]});b(this,"hide",()=>this._queueOrRun(this._hide));b(this,"show",()=>this._queueOrRun(this._show));b(this,"focus",()=>this._queueOrRun(this._focus));b(this,"requestFocus",()=>this._queueOrRun(this._requestFocus));b(this,"blur",()=>this._queueOrRun(this._blur));b(this,"scrollToCursor",()=>this._queueOrRun(this._scrollToCursor));b(this,"setEditable",e=>this._queueOrRun(()=>this._setEditable(e)));b(this,"clearContent",()=>this._queueOrRun(this._clearContent));b(this,"setContent",(e,t)=>this._queueOrRun(()=>this._setContent(e,t)));b(this,"insertContent",e=>this._queueOrRun(()=>this._insertContent(e)));b(this,"_hide",()=>{this._editor&&(this._editor.view.dom.style.display="none")});b(this,"_show",()=>{this._editor&&this._editor.view.dom.style.removeProperty("display")});b(this,"_focus",()=>{var e;return(e=this._editor)==null?void 0:e.commands.focus()});b(this,"_requestFocus",async()=>{await Rl(),this._editor&&this._editor.commands.focus()});b(this,"_blur",()=>{var e;return(e=this._editor)==null?void 0:e.commands.blur()});b(this,"_scrollToCursor",()=>{var e;return(e=this._editor)==null?void 0:e.commands.scrollIntoView()});b(this,"_setEditable",e=>{var t,n;(t=this._editor)==null||t.setEditable(e),e&&((n=this._editor)==null||n.commands.setTextSelection(this._editor.state.doc.content.size))});b(this,"_clearContent",()=>{var t;const e=ce(this._opts.content);e&&e.rawText!==""&&((t=this._editor)==null||t.commands.clearContent(!0))});b(this,"_setContent",(e,t)=>{var o;const n=ce(this._opts.content);e!==(n==null?void 0:n.rawText)&&e!==(n==null?void 0:n.richTextJsonRepr)&&((e=zs(e??[]))!==void 0?(o=this._editor)==null||o.chain().setContent(e,!0,{preserveWhitespace:!0}).setTextSelection((t==null?void 0:t.cursorPosition)==="start"?0:1e20).run():this._clearContent())});b(this,"_insertContent",e=>{var n;const t=zs(e);t!==void 0&&((n=this._editor)==null||n.commands.insertContent(t,{parseOptions:{preserveWhitespace:!0}}))});this._opts=e}get commands(){var e;return(e=this._editor)==null?void 0:e.commands}}class Vh{constructor(){b(this,"_isFocused",Te(!1));b(this,"_isEditable",Te(!1));b(this,"_content",Te(void 0));b(this,"_disposers",[]);b(this,"_isFooterClicked",!1);b(this,"_footerClickTimeout");b(this,"_footerAwareFocused",Te(!1));b(this,"registerEditor",e=>{this._isFocused.set(e.isFocused),this._isEditable.set(e.isEditable),this._content.set({richTextJsonRepr:e.getJSON(),rawText:e.getText()}),this._footerAwareFocused.set(e.isFocused);const t=()=>{this._isFocused.set(!0),this._isFooterClicked||this._footerAwareFocused.set(!0)},n=()=>{this._isFocused.set(!1),this._isFooterClicked||this._footerAwareFocused.set(!1)},o=()=>this._isEditable.set(e.isEditable),i=()=>{this._content.set({richTextJsonRepr:e.getJSON(),rawText:e.getText()})};e.on("focus",t),e.on("blur",n),e.on("update",o),e.on("update",i),this._disposers.push(()=>e.off("focus",t),()=>e.off("blur",n),()=>e.off("update",o),()=>e.off("update",i))});b(this,"unregisterEditor",()=>{this._isFocused.set(!1),this._isEditable.set(!1),this._footerAwareFocused.set(!1),this._footerClickTimeout&&(clearTimeout(this._footerClickTimeout),this._footerClickTimeout=void 0),this._disposers.forEach(e=>e()),this._disposers=[]});b(this,"handleFooterClick",()=>{this._isFooterClicked=!0,this._footerClickTimeout&&clearTimeout(this._footerClickTimeout),this._footerClickTimeout=setTimeout(()=>{this._isFooterClicked=!1,this._footerClickTimeout=void 0},200)});b(this,"onFocus",e=>this.onFocusChanged(t=>{t&&e()}));b(this,"onBlur",e=>this.onFocusChanged(t=>{!t&&e()}));b(this,"onFocusChanged",e=>this._isFocused.subscribe(e));b(this,"onEditableChanged",e=>this._isEditable.subscribe(e));b(this,"onContentChanged",e=>this._content.subscribe(t=>t&&e(t)))}get isFocused(){return this._isFocused}get footerAwareFocused(){return this._footerAwareFocused}get isEditable(){return this._isEditable}get content(){return this._content}}const Wt=class Wt{constructor(){b(this,"_opts",Te({}));b(this,"_editor");b(this,"_rootNode");b(this,"pluginManager",new Fh(this._opts));b(this,"eventManager",new Vh);b(this,"commandManager",new Bh({content:this.eventManager.content}));b(this,"instanceId",`augment-rich-text-editor-${Wt._getNextInstanceIdx()}`);b(this,"registerRoot",(e,t)=>(this._destroyEditor(),this._rootNode=e,this._opts.set(t),this._initializeEditor(),{update:n=>{this._opts.set(n)},destroy:()=>{this._destroyEditor()}}));b(this,"_registerEditorWithManagers",e=>{this.eventManager.registerEditor(e),this.commandManager.registerEditor(e)});b(this,"_unregisterEditorFromManagers",()=>{this.commandManager.unregisterEditor(),this.eventManager.unregisterEditor()});b(this,"_reinitializeEditor",()=>{const e=ce(this.eventManager.content);this._destroyEditor(),this._initializeEditor(),e!==void 0&&this.commandManager.setContent(e.richTextJsonRepr)});b(this,"_initializeEditor",()=>{if(this._rootNode===void 0||this._editor!==void 0)return;const e=ce(this._opts),t={element:document.createElement("div"),editable:e.editable??!0,injectCSS:!0,extensions:this.pluginManager.tipTapExtensions,onCreate:({editor:n})=>{this._registerEditorWithManagers(n),ce(this._opts).focusOnInit&&this.commandManager.focus();const o=this._attachCopyHandler();n.on("destroy",o);const i=ce(this._opts).onFocus;i&&n.on("focus",i);const s=ce(this._opts).onBlur;s&&n.on("blur",s)},onDestroy:()=>{this._unregisterEditorFromManagers()},onSelectionUpdate:()=>{(ce(this._opts).editable??1)&&this.commandManager.scrollToCursor()},editorProps:{handlePaste:(n,o)=>{var s;if(this._isEventFromRichTextEditor(o))return!1;const i=(s=o.clipboardData)==null?void 0:s.getData("text/plain");return!!i&&(this.commandManager.insertContent(i),!0)},attributes:{style:"min-height: 100%; outline: none;","data-testid":"design-system-rich-text-editor-tiptap"}}};return this._editor=new Nh(t),this._rootNode.appendChild(this._editor.view.dom),this._rootNode.addEventListener("keydown",n=>{n.key==="z"&&n.metaKey===!0&&n.stopPropagation()}),this._editor});b(this,"_attachCopyHandler",()=>{var e,t;return(e=this._rootNode)==null||e.addEventListener("copy",this._copyHandler),(t=this._rootNode)==null||t.addEventListener("cut",this._copyHandler),()=>{var n,o;(n=this._rootNode)==null||n.removeEventListener("copy",this._copyHandler),(o=this._rootNode)==null||o.removeEventListener("cut",this._copyHandler)}});b(this,"_copyHandler",e=>{var t;(t=e.clipboardData)==null||t.setData("application/x-augment/rich-text","true")});b(this,"_isEventFromRichTextEditor",e=>{var t;return((t=e.clipboardData)==null?void 0:t.getData("application/x-augment/rich-text"))==="true"});b(this,"_destroyEditor",()=>{var e,t;this._unregisterEditorFromManagers(),(e=this._editor)==null||e.view.dom.remove(),(t=this._editor)==null||t.destroy(),this._editor=void 0});this.pluginManager.onPluginsChanged(this._reinitializeEditor)}get rootNode(){var e;return(e=this._editor)==null?void 0:e.view.dom}};b(Wt,"CONTEXT_KEY","augment-rich-text-editor"),b(Wt,"INSTANCE_IDX",0),b(Wt,"_getNextInstanceIdx",()=>Wt.INSTANCE_IDX++);let et=Wt;function xp(){const r=Ye(et.CONTEXT_KEY);if(!r)throw new Error(`No editor context '${et.CONTEXT_KEY}' found.`);return r}var Hh=Y('<div class="paste-error-container svelte-spp06o"><!></div>'),qh=Y('<div class="c-rich-text-editor-augment__banner svelte-69ukhr"><!></div>'),jh=Y('<div class="c-rich-text-editor-augment__active-button svelte-69ukhr"><!></div>'),Kh=Y('<div class="c-rich-text-editor-augment__footer-wrapper svelte-69ukhr"><!></div>'),Jh=Y('<div class="l-rich-text-editor-augment svelte-69ukhr" role="button" tabindex="-1"><!> <div class="c-rich-text-editor-augment__editor svelte-69ukhr"><!></div>  <!></div>'),Wh=Y("<!> <!>",1),Uh=Y("<div><!></div>");const kp={Content:function(r,e){Ie(e,!1);const[t,n]=bn();let o=j(e,"onContentChanged",8,()=>{}),i=j(e,"content",8,"");const s=Te(!1),a=Te("");let l;const c=Ye(et.CONTEXT_KEY);let h,d=Ct(void 0);Pl(()=>{h=function(m={}){const g=m.maxPasteSize??1e5,y=m.onExceedsPasteLimit,C=function(w){const v=w.clipboardData||window.clipboardData;if(!v)return;const x=v.getData("text/plain");!x||x.length<=g||(w.preventDefault(),console.warn(`Paste operation blocked: Content exceeds ${g} character limit (${x.length} characters)`),y&&y(x.length,g))};return document.addEventListener("paste",C,!0),()=>{document.removeEventListener("paste",C,!0)}}({onExceedsPasteLimit:(m,g)=>{Vr(a,`Cannot paste: Content exceeds ${g.toLocaleString()} character limit (${m.toLocaleString()} characters)`),Vr(s,!0),l&&clearTimeout(l),l=window.setTimeout(()=>{Vr(s,!1)},3e3)}})}),Wn(()=>{h&&h(),clearTimeout(l)}),Fe(()=>(Q(d),T(o())),()=>{var m;(m=Q(d))==null||m(),Qe(d,c.eventManager.onContentChanged(o()))}),Fe(()=>T(i()),()=>{c.commandManager.setContent(i())}),vt(),ze();var p=De(),f=ie(p),u=m=>{var g=Hh(),y=ne(g);Kl(y,{variant:"soft",color:"error",size:1,children:(C,w)=>{var v=St();ve(()=>je(v,it(a,"$pasteErrorMessage",t))),R(C,v)},$$slots:{default:!0,icon:(C,w)=>{Xs(C,{slot:"icon"})}}}),R(m,g)};U(f,m=>{it(s,"$showPasteError",t)&&m(u)}),R(r,p),_e(),n()},Root:function(r,e){const t=Wo(e);Ie(e,!1);const[n,o]=bn(),i=Ct();let s=j(e,"editable",24,()=>{}),a=j(e,"focusOnInit",24,()=>{}),l=j(e,"onFocus",24,()=>{}),c=j(e,"onBlur",24,()=>{}),h=j(e,"size",8,1),d=j(e,"variant",8,"default"),p=j(e,"showActionButton",8,!1);const f=new et;Gs(et.CONTEXT_KEY,f),a()&&f.commandManager.requestFocus();const u=()=>f.commandManager.requestFocus(),m=()=>f.commandManager.focus(),g=()=>f.commandManager.blur(),y=()=>f.commandManager.clearContent(),C=()=>f.eventManager.footerAwareFocused;function w(){f.eventManager.handleFooterClick()}Fe(()=>{},()=>{zl(Qe(i,f.eventManager.footerAwareFocused),"$isFocusedState",n)}),vt(),ze();var v=Uh();let x;var O=ne(v);Jl(O,{class:"c-rich-text-editor-augment__card",insetContent:!0,variant:"ghost",get size(){return h()},children:(M,k)=>{var N=Wh(),I=ie(N),L=_=>{var H=qh(),G=ne(H);Se(G,e,"banner",{},null),R(_,H)};U(I,_=>{P(()=>t.banner)&&_(L)});var z=le(I,2),A=_=>{var H=jh(),G=ne(H);Se(G,e,"activeButton",{},null),R(_,H)},F=_=>{var H=Jh(),G=ne(H);Se(G,e,"header",{},null);var B=le(G,2),ee=ne(B);Se(ee,e,"default",{},null),ql(B,(te,se)=>{var Z;return(Z=f.registerRoot)==null?void 0:Z.call(f,te,se)},()=>({editable:s(),focusOnInit:a(),onFocus:l(),onBlur:c()}));var re=le(B,2),ae=te=>{var se=Kh(),Z=ne(se);Se(Z,e,"footer",{},null),ke("click",se,w),ke("mousedown",se,w),R(te,se)};U(re,te=>{P(()=>t.footer)&&te(ae)}),ke("keydown",H,m),ke("click",H,m),ke("click",H,function(te){nt.call(this,e,te)}),ke("dblclick",H,function(te){nt.call(this,e,te)}),R(_,H)};U(z,_=>{p()?_(A):_(F,!1)}),R(M,N)},$$slots:{default:!0}}),ve(M=>x=Mn(v,1,`c-rich-text-editor-augment c-rich-text-editor-augment--${d()}`,"svelte-69ukhr",x,M),[()=>({"c-rich-text-editor-augment--editable":s(),"c-rich-text-editor-augment--focused":it(Q(i),"$isFocusedState",n)})],Je),R(r,v),st(e,"requestFocus",u),st(e,"forceFocus",m),st(e,"blur",g),st(e,"clearContent",y),st(e,"isFocused",C);var E=_e({requestFocus:u,forceFocus:m,blur:g,clearContent:y,isFocused:C});return o(),E}};function Gh(r){var e;const{char:t,allowSpaces:n,allowedPrefixes:o,startOfLine:i,$position:s}=r,a=t.replace(/[-/\\^$*+?.()|[\]{}]/g,"\\$&"),l=new RegExp(`\\s${a}$`),c=i?"^":"",h=n?new RegExp(`${c}${a}.*?(?=\\s${a}|$)`,"gm"):new RegExp(`${c}(?:^)?${a}[^\\s${a}]*`,"gm"),d=((e=s.nodeBefore)===null||e===void 0?void 0:e.isText)&&s.nodeBefore.text;if(!d)return null;const p=s.pos-d.length,f=Array.from(d.matchAll(h)).pop();if(!f||f.input===void 0||f.index===void 0)return null;const u=f.input.slice(Math.max(0,f.index-1),f.index),m=new RegExp(`^[${o==null?void 0:o.join("")}\0]?$`).test(u);if(o!==null&&!m)return null;const g=p+f.index;let y=g+f[0].length;return n&&l.test(d.slice(y-1,y+1))&&(f[0]+=" ",y+=1),g<s.pos&&y>=s.pos?{range:{from:g,to:y},query:f[0].slice(t.length),text:f[0]}:null}const Zh=new Ve("suggestion");function Yh({pluginKey:r=Zh,editor:e,char:t="@",allowSpaces:n=!1,allowedPrefixes:o=[" "],startOfLine:i=!1,decorationTag:s="span",decorationClass:a="suggestion",command:l=()=>null,items:c=()=>[],render:h=()=>({}),allow:d=()=>!0,findSuggestionMatch:p=Gh}){let f;const u=h==null?void 0:h(),m=new He({key:r,view(){return{update:async(g,y)=>{var C,w,v,x,O,E,M;const k=(C=this.key)===null||C===void 0?void 0:C.getState(y),N=(w=this.key)===null||w===void 0?void 0:w.getState(g.state),I=k.active&&N.active&&k.range.from!==N.range.from,L=!k.active&&N.active,z=k.active&&!N.active,A=L||I,F=!L&&!z&&k.query!==N.query&&!I,_=z||I;if(!A&&!F&&!_)return;const H=_&&!A?k:N,G=g.dom.querySelector(`[data-decoration-id="${H.decorationId}"]`);f={editor:e,range:H.range,query:H.query,text:H.text,items:[],command:B=>l({editor:e,range:H.range,props:B}),decorationNode:G,clientRect:G?()=>{var B;const{decorationId:ee}=(B=this.key)===null||B===void 0?void 0:B.getState(e.state),re=g.dom.querySelector(`[data-decoration-id="${ee}"]`);return(re==null?void 0:re.getBoundingClientRect())||null}:null},A&&((v=u==null?void 0:u.onBeforeStart)===null||v===void 0||v.call(u,f)),F&&((x=u==null?void 0:u.onBeforeUpdate)===null||x===void 0||x.call(u,f)),(F||A)&&(f.items=await c({editor:e,query:H.query})),_&&((O=u==null?void 0:u.onExit)===null||O===void 0||O.call(u,f)),F&&((E=u==null?void 0:u.onUpdate)===null||E===void 0||E.call(u,f)),A&&((M=u==null?void 0:u.onStart)===null||M===void 0||M.call(u,f))},destroy:()=>{var g;f&&((g=u==null?void 0:u.onExit)===null||g===void 0||g.call(u,f))}}},state:{init:()=>({active:!1,range:{from:0,to:0},query:null,text:null,composing:!1}),apply(g,y,C,w){const{isEditable:v}=e,{composing:x}=e.view,{selection:O}=g,{empty:E,from:M}=O,k={...y};if(k.composing=x,v&&(E||e.view.composing)){!(M<y.range.from||M>y.range.to)||x||y.composing||(k.active=!1);const N=p({char:t,allowSpaces:n,allowedPrefixes:o,startOfLine:i,$position:O.$from}),I=`id_${Math.floor(4294967295*Math.random())}`;N&&d({editor:e,state:w,range:N.range})?(k.active=!0,k.decorationId=y.decorationId?y.decorationId:I,k.range=N.range,k.query=N.query,k.text=N.text):k.active=!1}else k.active=!1;return k.active||(k.decorationId=null,k.range={from:0,to:0},k.query=null,k.text=null),k}},props:{handleKeyDown(g,y){var C;const{active:w,range:v}=m.getState(g.state);return w&&((C=u==null?void 0:u.onKeyDown)===null||C===void 0?void 0:C.call(u,{view:g,event:y,range:v}))||!1},decorations(g){const{active:y,range:C,decorationId:w}=m.getState(g);return y?de.create(g.doc,[Be.inline(C.from,C.to,{nodeName:s,class:a,"data-decoration-id":w})]):null}}});return m}const Xh=new Ve("mention"),Qh=Ft.create({name:"mention",addOptions(){return{HTMLAttributes:{},renderText({options:r,node:e}){var t;return`${r.suggestion.char}${(t=e.attrs.label)!==null&&t!==void 0?t:e.attrs.id}`},deleteTriggerWithBackspace:!1,renderHTML({options:r,node:e}){var t;return["span",Zt(this.HTMLAttributes,r.HTMLAttributes),`${r.suggestion.char}${(t=e.attrs.label)!==null&&t!==void 0?t:e.attrs.id}`]},suggestion:{char:"@",pluginKey:Xh,command:({editor:r,range:e,props:t})=>{var n,o;const i=r.view.state.selection.$to.nodeAfter;!((n=i==null?void 0:i.text)===null||n===void 0)&&n.startsWith(" ")&&(e.to+=1),r.chain().focus().insertContentAt(e,[{type:this.name,attrs:t},{type:"text",text:" "}]).run(),(o=window.getSelection())===null||o===void 0||o.collapseToEnd()},allow:({state:r,range:e})=>{const t=r.doc.resolve(e.from),n=r.schema.nodes[this.name];return!!t.parent.type.contentMatch.matchType(n)}}}},group:"inline",inline:!0,selectable:!1,atom:!0,addAttributes:()=>({id:{default:null,parseHTML:r=>r.getAttribute("data-id"),renderHTML:r=>r.id?{"data-id":r.id}:{}},label:{default:null,parseHTML:r=>r.getAttribute("data-label"),renderHTML:r=>r.label?{"data-label":r.label}:{}}}),parseHTML(){return[{tag:`span[data-type="${this.name}"]`}]},renderHTML({node:r,HTMLAttributes:e}){if(this.options.renderLabel!==void 0)return console.warn("renderLabel is deprecated use renderText and renderHTML instead"),["span",Zt({"data-type":this.name},this.options.HTMLAttributes,e),this.options.renderLabel({options:this.options,node:r})];const t={...this.options};t.HTMLAttributes=Zt({"data-type":this.name},this.options.HTMLAttributes,e);const n=this.options.renderHTML({options:t,node:r});return typeof n=="string"?["span",Zt({"data-type":this.name},this.options.HTMLAttributes,e),n]:n},renderText({node:r}){return this.options.renderLabel!==void 0?(console.warn("renderLabel is deprecated use renderText and renderHTML instead"),this.options.renderLabel({options:this.options,node:r})):this.options.renderText({options:this.options,node:r})},addKeyboardShortcuts(){return{Backspace:()=>this.editor.commands.command(({tr:r,state:e})=>{let t=!1;const{selection:n}=e,{empty:o,anchor:i}=n;return!!o&&(e.doc.nodesBetween(i-1,i,(s,a)=>{if(s.type.name===this.name)return t=!0,r.insertText(this.options.deleteTriggerWithBackspace?"":this.options.suggestion.char||"",a,a+s.nodeSize),!1}),t)})}},addProseMirrorPlugins(){return[Yh({editor:this.editor,...this.options.suggestion})]}});function wt(r){return Array.isArray?Array.isArray(r):kl(r)==="[object Array]"}function ct(r){return typeof r=="string"}function bl(r){return typeof r=="number"}function ed(r){return r===!0||r===!1||function(e){return xl(e)&&e!==null}(r)&&kl(r)=="[object Boolean]"}function xl(r){return typeof r=="object"}function Re(r){return r!=null}function mo(r){return!r.trim().length}function kl(r){return r==null?r===void 0?"[object Undefined]":"[object Null]":Object.prototype.toString.call(r)}const td=r=>`Missing ${r} property in key`,nd=r=>`Property 'weight' in key '${r}' must be a positive integer`,Fs=Object.prototype.hasOwnProperty;class rd{constructor(e){this._keys=[],this._keyMap={};let t=0;e.forEach(n=>{let o=Ml(n);this._keys.push(o),this._keyMap[o.id]=o,t+=o.weight}),this._keys.forEach(n=>{n.weight/=t})}get(e){return this._keyMap[e]}keys(){return this._keys}toJSON(){return JSON.stringify(this._keys)}}function Ml(r){let e=null,t=null,n=null,o=1,i=null;if(ct(r)||wt(r))n=r,e=Bs(r),t=Vo(r);else{if(!Fs.call(r,"name"))throw new Error(td("name"));const s=r.name;if(n=s,Fs.call(r,"weight")&&(o=r.weight,o<=0))throw new Error(nd(s));e=Bs(s),t=Vo(s),i=r.getFn}return{path:e,id:t,weight:o,src:n,getFn:i}}function Bs(r){return wt(r)?r:r.split(".")}function Vo(r){return wt(r)?r.join("."):r}const od={useExtendedSearch:!1,getFn:function(r,e){let t=[],n=!1;const o=(i,s,a)=>{if(Re(i))if(s[a]){const l=i[s[a]];if(!Re(l))return;if(a===s.length-1&&(ct(l)||bl(l)||ed(l)))t.push(function(c){return c==null?"":function(h){if(typeof h=="string")return h;let d=h+"";return d=="0"&&1/h==-1/0?"-0":d}(c)}(l));else if(wt(l)){n=!0;for(let c=0,h=l.length;c<h;c+=1)o(l[c],s,a+1)}else s.length&&o(l,s,a+1)}else t.push(i)};return o(r,ct(e)?e.split("."):e,0),n?t:t[0]},ignoreLocation:!1,ignoreFieldNorm:!1,fieldNormWeight:1};var q={isCaseSensitive:!1,includeScore:!1,keys:[],shouldSort:!0,sortFn:(r,e)=>r.score===e.score?r.idx<e.idx?-1:1:r.score<e.score?-1:1,includeMatches:!1,findAllMatches:!1,minMatchCharLength:1,location:0,threshold:.6,distance:100,...od};const id=/[^ ]+/g;class ai{constructor({getFn:e=q.getFn,fieldNormWeight:t=q.fieldNormWeight}={}){this.norm=function(n=1,o=3){const i=new Map,s=Math.pow(10,o);return{get(a){const l=a.match(id).length;if(i.has(l))return i.get(l);const c=1/Math.pow(l,.5*n),h=parseFloat(Math.round(c*s)/s);return i.set(l,h),h},clear(){i.clear()}}}(t,3),this.getFn=e,this.isCreated=!1,this.setIndexRecords()}setSources(e=[]){this.docs=e}setIndexRecords(e=[]){this.records=e}setKeys(e=[]){this.keys=e,this._keysMap={},e.forEach((t,n)=>{this._keysMap[t.id]=n})}create(){!this.isCreated&&this.docs.length&&(this.isCreated=!0,ct(this.docs[0])?this.docs.forEach((e,t)=>{this._addString(e,t)}):this.docs.forEach((e,t)=>{this._addObject(e,t)}),this.norm.clear())}add(e){const t=this.size();ct(e)?this._addString(e,t):this._addObject(e,t)}removeAt(e){this.records.splice(e,1);for(let t=e,n=this.size();t<n;t+=1)this.records[t].i-=1}getValueForItemAtKeyId(e,t){return e[this._keysMap[t]]}size(){return this.records.length}_addString(e,t){if(!Re(e)||mo(e))return;let n={v:e,i:t,n:this.norm.get(e)};this.records.push(n)}_addObject(e,t){let n={i:t,$:{}};this.keys.forEach((o,i)=>{let s=o.getFn?o.getFn(e):this.getFn(e,o.path);if(Re(s)){if(wt(s)){let a=[];const l=[{nestedArrIndex:-1,value:s}];for(;l.length;){const{nestedArrIndex:c,value:h}=l.pop();if(Re(h))if(ct(h)&&!mo(h)){let d={v:h,i:c,n:this.norm.get(h)};a.push(d)}else wt(h)&&h.forEach((d,p)=>{l.push({nestedArrIndex:p,value:d})})}n.$[i]=a}else if(ct(s)&&!mo(s)){let a={v:s,n:this.norm.get(s)};n.$[i]=a}}}),this.records.push(n)}toJSON(){return{keys:this.keys,records:this.records}}}function Sl(r,e,{getFn:t=q.getFn,fieldNormWeight:n=q.fieldNormWeight}={}){const o=new ai({getFn:t,fieldNormWeight:n});return o.setKeys(r.map(Ml)),o.setSources(e),o.create(),o}function nr(r,{errors:e=0,currentLocation:t=0,expectedLocation:n=0,distance:o=q.distance,ignoreLocation:i=q.ignoreLocation}={}){const s=e/r.length;if(i)return s;const a=Math.abs(n-t);return o?s+a/o:a?1:s}const Kt=32;function sd(r,e,t,{location:n=q.location,distance:o=q.distance,threshold:i=q.threshold,findAllMatches:s=q.findAllMatches,minMatchCharLength:a=q.minMatchCharLength,includeMatches:l=q.includeMatches,ignoreLocation:c=q.ignoreLocation}={}){if(e.length>Kt)throw new Error(`Pattern length exceeds max of ${Kt}.`);const h=e.length,d=r.length,p=Math.max(0,Math.min(n,d));let f=i,u=p;const m=a>1||l,g=m?Array(d):[];let y;for(;(y=r.indexOf(e,u))>-1;){let E=nr(e,{currentLocation:y,expectedLocation:p,distance:o,ignoreLocation:c});if(f=Math.min(E,f),u=y+h,m){let M=0;for(;M<h;)g[y+M]=1,M+=1}}u=-1;let C=[],w=1,v=h+d;const x=1<<h-1;for(let E=0;E<h;E+=1){let M=0,k=v;for(;M<k;)nr(e,{errors:E,currentLocation:p+k,expectedLocation:p,distance:o,ignoreLocation:c})<=f?M=k:v=k,k=Math.floor((v-M)/2+M);v=k;let N=Math.max(1,p-k+1),I=s?d:Math.min(p+k,d)+h,L=Array(I+2);L[I+1]=(1<<E)-1;for(let z=I;z>=N;z-=1){let A=z-1,F=t[r.charAt(A)];if(m&&(g[A]=+!!F),L[z]=(L[z+1]<<1|1)&F,E&&(L[z]|=(C[z+1]|C[z])<<1|1|C[z+1]),L[z]&x&&(w=nr(e,{errors:E,currentLocation:A,expectedLocation:p,distance:o,ignoreLocation:c}),w<=f)){if(f=w,u=A,u<=p)break;N=Math.max(1,2*p-u)}}if(nr(e,{errors:E+1,currentLocation:p,expectedLocation:p,distance:o,ignoreLocation:c})>f)break;C=L}const O={isMatch:u>=0,score:Math.max(.001,w)};if(m){const E=function(M=[],k=q.minMatchCharLength){let N=[],I=-1,L=-1,z=0;for(let A=M.length;z<A;z+=1){let F=M[z];F&&I===-1?I=z:F||I===-1||(L=z-1,L-I+1>=k&&N.push([I,L]),I=-1)}return M[z-1]&&z-I>=k&&N.push([I,z-1]),N}(g,a);E.length?l&&(O.indices=E):O.isMatch=!1}return O}function ad(r){let e={};for(let t=0,n=r.length;t<n;t+=1){const o=r.charAt(t);e[o]=(e[o]||0)|1<<n-t-1}return e}class Ol{constructor(e,{location:t=q.location,threshold:n=q.threshold,distance:o=q.distance,includeMatches:i=q.includeMatches,findAllMatches:s=q.findAllMatches,minMatchCharLength:a=q.minMatchCharLength,isCaseSensitive:l=q.isCaseSensitive,ignoreLocation:c=q.ignoreLocation}={}){if(this.options={location:t,threshold:n,distance:o,includeMatches:i,findAllMatches:s,minMatchCharLength:a,isCaseSensitive:l,ignoreLocation:c},this.pattern=l?e:e.toLowerCase(),this.chunks=[],!this.pattern.length)return;const h=(p,f)=>{this.chunks.push({pattern:p,alphabet:ad(p),startIndex:f})},d=this.pattern.length;if(d>Kt){let p=0;const f=d%Kt,u=d-f;for(;p<u;)h(this.pattern.substr(p,Kt),p),p+=Kt;if(f){const m=d-Kt;h(this.pattern.substr(m),m)}}else h(this.pattern,0)}searchIn(e){const{isCaseSensitive:t,includeMatches:n}=this.options;if(t||(e=e.toLowerCase()),this.pattern===e){let u={isMatch:!0,score:0};return n&&(u.indices=[[0,e.length-1]]),u}const{location:o,distance:i,threshold:s,findAllMatches:a,minMatchCharLength:l,ignoreLocation:c}=this.options;let h=[],d=0,p=!1;this.chunks.forEach(({pattern:u,alphabet:m,startIndex:g})=>{const{isMatch:y,score:C,indices:w}=sd(e,u,m,{location:o+g,distance:i,threshold:s,findAllMatches:a,minMatchCharLength:l,includeMatches:n,ignoreLocation:c});y&&(p=!0),d+=C,y&&w&&(h=[...h,...w])});let f={isMatch:p,score:p?d/this.chunks.length:1};return p&&n&&(f.indices=h),f}}class Tt{constructor(e){this.pattern=e}static isMultiMatch(e){return Vs(e,this.multiRegex)}static isSingleMatch(e){return Vs(e,this.singleRegex)}search(){}}function Vs(r,e){const t=r.match(e);return t?t[1]:null}class El extends Tt{constructor(e,{location:t=q.location,threshold:n=q.threshold,distance:o=q.distance,includeMatches:i=q.includeMatches,findAllMatches:s=q.findAllMatches,minMatchCharLength:a=q.minMatchCharLength,isCaseSensitive:l=q.isCaseSensitive,ignoreLocation:c=q.ignoreLocation}={}){super(e),this._bitapSearch=new Ol(e,{location:t,threshold:n,distance:o,includeMatches:i,findAllMatches:s,minMatchCharLength:a,isCaseSensitive:l,ignoreLocation:c})}static get type(){return"fuzzy"}static get multiRegex(){return/^"(.*)"$/}static get singleRegex(){return/^(.*)$/}search(e){return this._bitapSearch.searchIn(e)}}class Tl extends Tt{constructor(e){super(e)}static get type(){return"include"}static get multiRegex(){return/^'"(.*)"$/}static get singleRegex(){return/^'(.*)$/}search(e){let t,n=0;const o=[],i=this.pattern.length;for(;(t=e.indexOf(this.pattern,n))>-1;)n=t+i,o.push([t,n-1]);const s=!!o.length;return{isMatch:s,score:s?0:1,indices:o}}}const Ho=[class extends Tt{constructor(r){super(r)}static get type(){return"exact"}static get multiRegex(){return/^="(.*)"$/}static get singleRegex(){return/^=(.*)$/}search(r){const e=r===this.pattern;return{isMatch:e,score:e?0:1,indices:[0,this.pattern.length-1]}}},Tl,class extends Tt{constructor(r){super(r)}static get type(){return"prefix-exact"}static get multiRegex(){return/^\^"(.*)"$/}static get singleRegex(){return/^\^(.*)$/}search(r){const e=r.startsWith(this.pattern);return{isMatch:e,score:e?0:1,indices:[0,this.pattern.length-1]}}},class extends Tt{constructor(r){super(r)}static get type(){return"inverse-prefix-exact"}static get multiRegex(){return/^!\^"(.*)"$/}static get singleRegex(){return/^!\^(.*)$/}search(r){const e=!r.startsWith(this.pattern);return{isMatch:e,score:e?0:1,indices:[0,r.length-1]}}},class extends Tt{constructor(r){super(r)}static get type(){return"inverse-suffix-exact"}static get multiRegex(){return/^!"(.*)"\$$/}static get singleRegex(){return/^!(.*)\$$/}search(r){const e=!r.endsWith(this.pattern);return{isMatch:e,score:e?0:1,indices:[0,r.length-1]}}},class extends Tt{constructor(r){super(r)}static get type(){return"suffix-exact"}static get multiRegex(){return/^"(.*)"\$$/}static get singleRegex(){return/^(.*)\$$/}search(r){const e=r.endsWith(this.pattern);return{isMatch:e,score:e?0:1,indices:[r.length-this.pattern.length,r.length-1]}}},class extends Tt{constructor(r){super(r)}static get type(){return"inverse-exact"}static get multiRegex(){return/^!"(.*)"$/}static get singleRegex(){return/^!(.*)$/}search(r){const e=r.indexOf(this.pattern)===-1;return{isMatch:e,score:e?0:1,indices:[0,r.length-1]}}},El],Hs=Ho.length,ld=/ +(?=(?:[^\"]*\"[^\"]*\")*[^\"]*$)/,cd=new Set([El.type,Tl.type]);class hd{constructor(e,{isCaseSensitive:t=q.isCaseSensitive,includeMatches:n=q.includeMatches,minMatchCharLength:o=q.minMatchCharLength,ignoreLocation:i=q.ignoreLocation,findAllMatches:s=q.findAllMatches,location:a=q.location,threshold:l=q.threshold,distance:c=q.distance}={}){this.query=null,this.options={isCaseSensitive:t,includeMatches:n,minMatchCharLength:o,findAllMatches:s,ignoreLocation:i,location:a,threshold:l,distance:c},this.pattern=t?e:e.toLowerCase(),this.query=function(h,d={}){return h.split("|").map(p=>{let f=p.trim().split(ld).filter(m=>m&&!!m.trim()),u=[];for(let m=0,g=f.length;m<g;m+=1){const y=f[m];let C=!1,w=-1;for(;!C&&++w<Hs;){const v=Ho[w];let x=v.isMultiMatch(y);x&&(u.push(new v(x,d)),C=!0)}if(!C)for(w=-1;++w<Hs;){const v=Ho[w];let x=v.isSingleMatch(y);if(x){u.push(new v(x,d));break}}}return u})}(this.pattern,this.options)}static condition(e,t){return t.useExtendedSearch}searchIn(e){const t=this.query;if(!t)return{isMatch:!1,score:1};const{includeMatches:n,isCaseSensitive:o}=this.options;e=o?e:e.toLowerCase();let i=0,s=[],a=0;for(let l=0,c=t.length;l<c;l+=1){const h=t[l];s.length=0,i=0;for(let d=0,p=h.length;d<p;d+=1){const f=h[d],{isMatch:u,indices:m,score:g}=f.search(e);if(!u){a=0,i=0,s.length=0;break}if(i+=1,a+=g,n){const y=f.constructor.type;cd.has(y)?s=[...s,...m]:s.push(m)}}if(i){let d={isMatch:!0,score:a/i};return n&&(d.indices=s),d}}return{isMatch:!1,score:1}}}const qo=[];function jo(r,e){for(let t=0,n=qo.length;t<n;t+=1){let o=qo[t];if(o.condition(r,e))return new o(r,e)}return new Ol(r,e)}const li="$and",dd="$or",qs="$path",pd="$val",go=r=>!(!r[li]&&!r[dd]),js=r=>({[li]:Object.keys(r).map(e=>({[e]:r[e]}))});function Nl(r,e,{auto:t=!0}={}){const n=o=>{let i=Object.keys(o);const s=(l=>!!l[qs])(o);if(!s&&i.length>1&&!go(o))return n(js(o));if((l=>!wt(l)&&xl(l)&&!go(l))(o)){const l=s?o[qs]:i[0],c=s?o[pd]:o[l];if(!ct(c))throw new Error((d=>`Invalid value for key ${d}`)(l));const h={keyId:Vo(l),pattern:c};return t&&(h.searcher=jo(c,e)),h}let a={children:[],operator:i[0]};return i.forEach(l=>{const c=o[l];wt(c)&&c.forEach(h=>{a.children.push(n(h))})}),a};return go(r)||(r=js(r)),n(r)}function ud(r,e){const t=r.matches;e.matches=[],Re(t)&&t.forEach(n=>{if(!Re(n.indices)||!n.indices.length)return;const{indices:o,value:i}=n;let s={indices:o,value:i};n.key&&(s.key=n.key.src),n.idx>-1&&(s.refIndex=n.idx),e.matches.push(s)})}function fd(r,e){e.score=r.score}class yn{constructor(e,t={},n){this.options={...q,...t},this.options.useExtendedSearch,this._keyStore=new rd(this.options.keys),this.setCollection(e,n)}setCollection(e,t){if(this._docs=e,t&&!(t instanceof ai))throw new Error("Incorrect 'index' type");this._myIndex=t||Sl(this.options.keys,this._docs,{getFn:this.options.getFn,fieldNormWeight:this.options.fieldNormWeight})}add(e){Re(e)&&(this._docs.push(e),this._myIndex.add(e))}remove(e=()=>!1){const t=[];for(let n=0,o=this._docs.length;n<o;n+=1){const i=this._docs[n];e(i,n)&&(this.removeAt(n),n-=1,o-=1,t.push(i))}return t}removeAt(e){this._docs.splice(e,1),this._myIndex.removeAt(e)}getIndex(){return this._myIndex}search(e,{limit:t=-1}={}){const{includeMatches:n,includeScore:o,shouldSort:i,sortFn:s,ignoreFieldNorm:a}=this.options;let l=ct(e)?ct(this._docs[0])?this._searchStringList(e):this._searchObjectList(e):this._searchLogical(e);return function(c,{ignoreFieldNorm:h=q.ignoreFieldNorm}){c.forEach(d=>{let p=1;d.matches.forEach(({key:f,norm:u,score:m})=>{const g=f?f.weight:null;p*=Math.pow(m===0&&g?Number.EPSILON:m,(g||1)*(h?1:u))}),d.score=p})}(l,{ignoreFieldNorm:a}),i&&l.sort(s),bl(t)&&t>-1&&(l=l.slice(0,t)),function(c,h,{includeMatches:d=q.includeMatches,includeScore:p=q.includeScore}={}){const f=[];return d&&f.push(ud),p&&f.push(fd),c.map(u=>{const{idx:m}=u,g={item:h[m],refIndex:m};return f.length&&f.forEach(y=>{y(u,g)}),g})}(l,this._docs,{includeMatches:n,includeScore:o})}_searchStringList(e){const t=jo(e,this.options),{records:n}=this._myIndex,o=[];return n.forEach(({v:i,i:s,n:a})=>{if(!Re(i))return;const{isMatch:l,score:c,indices:h}=t.searchIn(i);l&&o.push({item:i,idx:s,matches:[{score:c,value:i,norm:a,indices:h}]})}),o}_searchLogical(e){const t=Nl(e,this.options),n=(a,l,c)=>{if(!a.children){const{keyId:d,searcher:p}=a,f=this._findMatches({key:this._keyStore.get(d),value:this._myIndex.getValueForItemAtKeyId(l,d),searcher:p});return f&&f.length?[{idx:c,item:l,matches:f}]:[]}const h=[];for(let d=0,p=a.children.length;d<p;d+=1){const f=a.children[d],u=n(f,l,c);if(u.length)h.push(...u);else if(a.operator===li)return[]}return h},o=this._myIndex.records,i={},s=[];return o.forEach(({$:a,i:l})=>{if(Re(a)){let c=n(t,a,l);c.length&&(i[l]||(i[l]={idx:l,item:a,matches:[]},s.push(i[l])),c.forEach(({matches:h})=>{i[l].matches.push(...h)}))}}),s}_searchObjectList(e){const t=jo(e,this.options),{keys:n,records:o}=this._myIndex,i=[];return o.forEach(({$:s,i:a})=>{if(!Re(s))return;let l=[];n.forEach((c,h)=>{l.push(...this._findMatches({key:c,value:s[h],searcher:t}))}),l.length&&i.push({idx:a,item:s,matches:l})}),i}_findMatches({key:e,value:t,searcher:n}){if(!Re(t))return[];let o=[];if(wt(t))t.forEach(({v:i,i:s,n:a})=>{if(!Re(i))return;const{isMatch:l,score:c,indices:h}=n.searchIn(i);l&&o.push({score:c,key:e,value:i,idx:s,norm:a,indices:h})});else{const{v:i,n:s}=t,{isMatch:a,score:l,indices:c}=n.searchIn(i);a&&o.push({score:l,key:e,value:i,norm:s,indices:c})}return o}}function Ks(r,e){const t=[];return e.$doc.content.descendants(n=>{n.type.name===r&&t.push(n)}),t}function Js(r,e,t,n={}){const o=[],i=(s,a)=>{var l;(l=n.onNewMention)==null||l.call(n,s,a),o.push(s)};return e.descendants((s,a)=>{if(s.type.name!==r)return;if(t===null)return void i(s,a);const l=Math.abs(e.nodeSize-t.nodeSize),c=t.nodeSize-2,h=l+s.nodeSize+1,d=Math.max(0,a-h),p=Math.min(c,a+h);let f=!1;t.nodesBetween(d,p,u=>{var m;if(u.type.name===r&&u.attrs.id===s.attrs.id)return f=!0,(m=n.onExistingMention)==null||m.call(n,u,a),!1}),f||i(s,a)}),o}function Ll(r,e){const t=new MutationObserver(n=>{for(const o of n)if(e.shouldTrigger(o))return e.callback(),void t.disconnect()});return t.observe(r,{childList:!0,subtree:!0,...e.mutationObserverInit}),e.timeout&&setTimeout(()=>t.disconnect(),e.timeout),()=>t.disconnect()}function Ws(r,e,t,n){return Ll(r,{shouldTrigger:o=>new Set(o.addedNodes).has(e),callback:t,timeout:n})}yn.version="7.0.0",yn.createIndex=Sl,yn.parseIndex=function(r,{getFn:e=q.getFn,fieldNormWeight:t=q.fieldNormWeight}={}){const{keys:n,records:o}=r,i=new ai({getFn:e,fieldNormWeight:t});return i.setKeys(n),i.setIndexRecords(o),i},yn.config=q,yn.parseQuery=Nl,function(...r){qo.push(...r)}(hd);class wn{constructor(e){b(this,"_disposers",[]);b(this,"_editor");b(this,"_tooltipData",Te(void 0));b(this,"onCreate",e=>{this._editor=e});b(this,"onDispose",()=>{this._editor=void 0,this._disposers.forEach(e=>e()),this._disposers=[]});b(this,"createMentionChip",({options:e,node:t})=>{var i,s;if(t.type.name!==this._mentionPluginId)throw new Error("Expected a mention node");const n=document.createElement("span");n.innerText=`${((i=e.suggestion)==null?void 0:i.char)??"@"}${t.attrs.label}`;for(const[a,l]of Object.entries(e.HTMLAttributes))typeof l=="string"?n.setAttribute(a,l.toString()):console.warn(`Unexpected HTML attribute value type: [${a}] = ${l}`);const o=(s=this._editor)==null?void 0:s.view.dom;return o&&this._setupMountUnmountCycle(o,n,()=>{this._tooltipData.set({data:t.attrs.data,anchorElement:n})}),n});b(this,"hideTooltip",()=>{this._tooltipData.set(void 0)});this._mentionPluginId=e}get tooltipData(){return this._tooltipData}_attachChipEventListeners(e,t){e.addEventListener("mouseenter",t),e.addEventListener("mouseover",t),e.addEventListener("mouseleave",this.hideTooltip)}_detachChipEventListeners(e,t){e.removeEventListener("mouseenter",t),e.removeEventListener("mouseover",t),e.removeEventListener("mouseleave",this.hideTooltip)}_removeFromDisposers(e){e&&(this._disposers=this._disposers.filter(t=>t!==e))}_setupMountUnmountCycle(e,t,n){let o,i;const s=()=>{this._removeFromDisposers(o),this._detachChipEventListeners(t,n),i=Ws(e,t,a),this._disposers.push(i)},a=()=>{var l,c;this._removeFromDisposers(i),this._attachChipEventListeners(t,n),l=t,o=Ll(e,{shouldTrigger:h=>new Set(h.removedNodes).has(l),callback:s,timeout:c}),this._disposers.push(o)};Ws(e,t,a)}}b(wn,"CHIP_CLASS_NAME","c-mention-chip"),b(wn,"CHIP_DATA_ATTR_KEY","data-augment-mention-chip-tooltip");class md{constructor(e={}){b(this,"_menuData",Te(void 0));b(this,"_mentionables",Te([]));b(this,"query",Ge(this._menuData,e=>e==null?void 0:e.tiptapExtensionProps.query));b(this,"activeIdx",Ge([this._mentionables,this._menuData],yd));b(this,"activeItem",Ge([this._mentionables,this.activeIdx],gd));b(this,"referenceClientRect",Ge(this._menuData,e=>e==null?void 0:e.referenceClientRect));b(this,"tiptapExtensionProps",Ge(this._menuData,e=>e==null?void 0:e.tiptapExtensionProps));b(this,"isMenuActive",Ge(this._menuData,e=>e!==void 0));b(this,"mentionables",this._mentionables);b(this,"updateOptions",e=>{this._opts={...this._opts,...e}});b(this,"updateMentionables",e=>{this._mentionables.set(e)});b(this,"replaceQueryWithMentionNode",e=>{var o;const t=ce(this.isMenuActive),n=(o=ce(this.tiptapExtensionProps))==null?void 0:o.command;return!(!t||!n)&&(n(e),!0)});b(this,"selectItem",e=>{var n,o;return!(ce(this._mentionables).findIndex(i=>i.id===e.id)===-1||!this._opts.onSelectMentionable)&&((o=(n=this._opts).onSelectMentionable)==null||o.call(n,e),!0)});b(this,"_incrementActiveIdx",()=>{const e=ce(this.activeIdx)??0;this._setActiveIdx(e+1)});b(this,"_decrementActiveIdx",()=>{const e=ce(this.activeIdx)??0;this._setActiveIdx(e-1)});b(this,"_setActiveIdx",e=>{this._menuData.update(t=>t&&{...t,activeIdx:e})});b(this,"onUpdateSuggestion",e=>{var n;const t=(n=e.clientRect)==null?void 0:n.call(e);t&&this._menuData.update(o=>({referenceClientRect:t,tiptapExtensionProps:e,activeIdx:(o==null?void 0:o.activeIdx)??0}))});b(this,"exitMenu",()=>{this._menuData.set(void 0),this._mentionables.set([])});b(this,"_handleKeyIfActive",e=>()=>!!ce(this.isMenuActive)&&(e(),!0));b(this,"onArrowUp",this._handleKeyIfActive(this._decrementActiveIdx));b(this,"onArrowDown",this._handleKeyIfActive(this._incrementActiveIdx));b(this,"selectActiveItem",this._handleKeyIfActive(()=>{const e=ce(this.activeItem);return!!e&&this.selectItem(e)}));this._opts=e}}function gd([r,e]){if(e!==void 0&&r.length!==0)return r[e]}function yd([r,e]){if(!((e==null?void 0:e.activeIdx)===void 0||r.length===0))return(e.activeIdx%r.length+r.length)%r.length%r.length}const mt=class mt{constructor(e){b(this,"_triggerCharacter");b(this,"_editor");b(this,"_mention");b(this,"_chipController");b(this,"_mentionableMenuContext",new md);b(this,"insertMentionNode",e=>!!this._editor&&(this._mentionableMenuContext.replaceQueryWithMentionNode(e.data)||this._editor.commands.insertContent({type:this._mentionPluginId,attrs:e}),!0));b(this,"_onCreate",e=>{var n,o;const t=Ks(this._mentionPluginId,e).map(i=>i.attrs.data);(o=(n=this._options).onMentionItemsUpdated)==null||o.call(n,{added:t,removed:[],current:t}),this._editor=e,this._chipController.onCreate(e)});b(this,"_onDestroy",()=>{var e,t;if(this._editor){const n=Ks(this._mentionPluginId,this._editor).map(o=>o.attrs.data);(t=(e=this._options).onMentionItemsUpdated)==null||t.call(e,{added:[],removed:n,current:[]}),this._editor=void 0,this._chipController.onDispose()}this._chipController.hideTooltip(),this._mentionableMenuContext.exitMenu()});b(this,"_onProseMirrorUpdate",(e,t)=>{var a,l;if(e===t)return;const n=c=>c.attrs.data,o=[],i=Js(this._mentionPluginId,e,t).map(n),s=Js(this._mentionPluginId,t,e,{onNewMention:c=>o.push(n(c)),onExistingMention:c=>o.push(n(c))}).map(n);(l=(a=this._options).onMentionItemsUpdated)==null||l.call(a,{added:s,removed:i,current:o})});this._options=e;const t=e.triggerCharacter??"@";this._triggerCharacter=t;const n=new Ve(this._mentionListenerPluginId),o=new Ve(this._mentionPluginId),i=e.allowedPrefixes??[" ","	",`
`],s=e.renderText??(g=>`${t}${g.name??g.id}`);this._chipController=new wn(this._mentionPluginId);const a=this._onCreate.bind(this),l=this._onProseMirrorUpdate.bind(this),c=this._onDestroy.bind(this),h=this._chipController.createMentionChip,d=this._mentionableMenuContext.onUpdateSuggestion,p=this._mentionableMenuContext.exitMenu,f=this._mentionableMenuContext.onArrowUp,u=this._mentionableMenuContext.onArrowDown,m=this._mentionableMenuContext.selectActiveItem;this._mention=Qh.extend({name:this._mentionPluginId,onCreate(){a(this.editor)},onDestroy(){c()},addKeyboardShortcuts:()=>({ArrowUp:f,ArrowDown:u,Enter:m,Tab:m}),addAttributes(){var g;return{...(g=this.parent)==null?void 0:g.call(this),data:{default:null,keepOnSplit:!1,parseHTML:y=>{const C=y.getAttribute(wn.CHIP_DATA_ATTR_KEY);return C?JSON.parse(C):null},renderHTML:y=>y.data?{[wn.CHIP_DATA_ATTR_KEY]:JSON.stringify(y.data)}:{}}}},addProseMirrorPlugins(){var g;return[...((g=this.parent)==null?void 0:g.call(this))??[],new He({key:n,view:()=>({update:(y,C)=>{l(C.doc,y.state.doc)}})})]},addOptions(){var y;const g=(y=this.parent)==null?void 0:y.call(this);return{...g,HTMLAttributes:{class:wn.CHIP_CLASS_NAME},renderHTML:h,renderText:({node:C})=>{const w=C.attrs.data;if(!w){const v={id:C.attrs.id||"unknown",label:C.attrs.label||"Unknown mention",name:C.attrs.label||C.attrs.id||"unknown"};return console.warn("Mention data missing, using fallback:",v),s(v)}return s(w)},suggestion:{...g==null?void 0:g.suggestion,pluginKey:o,char:t,allowedPrefixes:i,command:({editor:C,range:w,props:v})=>{var x,O;C&&w&&((O=(x=g.suggestion)==null?void 0:x.command)==null||O.call(x,{editor:C,range:w,props:{id:v.id,name:v.name??v.id,label:v.label,data:v}}))},render:()=>({onStart:d,onUpdate:d,onExit:p})}}}})}get chipController(){return this._chipController}get mentionableMenuContext(){return this._mentionableMenuContext}get _mentionPluginId(){return this._options.pluginId?this._options.pluginId:this._triggerCharacter==="@"?mt.DEFAULT_MENTION_PLUGIN_ID:mt.MENTION_PLUGIN_ID_BASE.replace("{}",this._triggerCharacter)}get _mentionListenerPluginId(){return mt.MENTION_LISTENER_PLUGIN_ID_BASE.replace("{}",this._mentionPluginId)}get tipTapExtension(){return this._mention}};b(mt,"CONTEXT_KEY","augment-svelte-mention-plugin"),b(mt,"MENTION_LISTENER_PLUGIN_ID_BASE","{}-listener"),b(mt,"MENTION_PLUGIN_ID_BASE","augment-prosemirror-mention-{}"),b(mt,"DEFAULT_MENTION_PLUGIN_ID","mention");let Jt=mt;var Cd=Y("<!> <!>",1),vd=Y("<!> <!>",1);const yo={ChipTooltip:function(r,e){const t=Wo(e);Ie(e,!1);const[n,o]=bn(),i=()=>it(s,"$chipTooltipData",n),s=Ye(Jt.CONTEXT_KEY).chipController.tooltipData;let a=Ct(void 0);Fe(()=>(i(),Q(a)),()=>{var c,h;i()===void 0?(c=Q(a))==null||c.requestClose():(h=Q(a))==null||h.requestOpen()}),vt(),ze();const l=Je(()=>(i(),P(()=>{var c;return(c=i())==null?void 0:c.anchorElement.getBoundingClientRect()})));Co(Wl(r,{triggerOn:[],get referenceClientRect(){return Q(l)},$$slots:{content:(c,h)=>{var d=Cd(),p=ie(d),f=g=>{var y=De(),C=ie(y);Se(C,e,"mentionable",{get mentionable(){return i(),P(()=>i().data)}},null),R(g,y)};U(p,g=>{i(),P(()=>i()&&t.mentionable)&&g(f)});var u=le(p,2),m=g=>{var y=De(),C=ie(y);Se(C,e,"default",{get mentionable(){return i(),P(()=>i().data)}},null),R(g,y)};U(u,g=>{i(),P(()=>i()&&t.default)&&g(m)}),R(c,d)}},$$legacy:!0}),c=>Qe(a,c),()=>Q(a)),_e(),o()},Root:function(r,e){Ie(e,!1);let t=j(e,"onMentionItemsUpdated",24,()=>{}),n=j(e,"triggerCharacter",24,()=>{}),o=j(e,"allowedPrefixes",24,()=>{}),i=j(e,"pluginId",24,()=>{}),s=j(e,"renderText",24,()=>{});const a=p=>{l.insertMentionNode({id:p.id,label:p.label,data:p})},l=new Jt({onMentionItemsUpdated:p=>{var f;return(f=t())==null?void 0:f(p)},triggerCharacter:n(),allowedPrefixes:o(),pluginId:i(),renderText:s()});Gs(Jt.CONTEXT_KEY,l);const c=Ye(et.CONTEXT_KEY).pluginManager.registerPlugin(l);Wn(c),ze();var h=De(),d=ie(h);return Se(d,e,"default",{},null),R(r,h),st(e,"insertMention",a),_e({insertMention:a})},Menu:{Root:function(r,e){Ie(e,!1);const[t,n]=bn(),o=()=>it(g,"$query",t);let i=j(e,"mentionables",8),s=j(e,"onQueryUpdate",8),a=j(e,"onSelectMentionable",8,x=>(f.insertMentionNode({...x,data:x}),!0)),l=Ct();const c=()=>{var x;return(x=Q(l))==null?void 0:x.requestOpen()},h=()=>{var x;return(x=Q(l))==null?void 0:x.requestClose()},d=x=>{var O;return(O=Q(l))==null?void 0:O.focusIdx(x)},p=Ye(et.CONTEXT_KEY),f=Ye(Jt.CONTEXT_KEY),u=f.mentionableMenuContext,{referenceClientRect:m,query:g,activeItem:y,isMenuActive:C,exitMenu:w}=u;Fe(()=>(T(s()),o()),()=>{s()(o())}),Fe(()=>T(a()),()=>{u.updateOptions({onSelectMentionable:a()})}),Fe(()=>T(i()),()=>{u.updateMentionables(i())}),vt(),ze(),Co(Lt.Root(r,{get open(){return it(C,"$isMenuActive",t)},onOpenChange:async x=>{x||p.commandManager.requestFocus()},children:(x,O)=>{var E=vd(),M=ie(E);Lt.Trigger(M,{get referenceClientRect(){return it(m,"$referenceClientRect",t)}});var k=le(M,2);Lt.Content(k,{side:"top",align:"start",size:1,get onClickOutside(){return w},get onEscapeKeyDown(){return w},children:(N,I)=>{var L=De(),z=ie(L);Se(z,e,"default",{get activeItem(){return it(y,"$activeItem",t)},get query(){return o()}},null),R(N,L)},$$slots:{default:!0}}),R(x,E)},$$slots:{default:!0},$$legacy:!0}),x=>Qe(l,x),()=>Q(l)),st(e,"requestOpen",c),st(e,"requestClose",h),st(e,"focusIdx",d);var v=_e({requestOpen:c,requestClose:h,focusIdx:d});return n(),v},Item:function(r,e){Ie(e,!1);const[t,n]=bn(),o=Ye(Jt.CONTEXT_KEY),{replaceQueryWithMentionNode:i,activeItem:s}=o.mentionableMenuContext;let a=j(e,"mentionable",8);ze();const l=Je(()=>it(s,"$activeItem",t)===a());Ul(r,{onSelect:()=>i(a()),get highlight(){return Q(l)},children:(c,h)=>{var d=De(),p=ie(d);Se(p,e,"default",{},null),R(c,d)},$$slots:{default:!0}}),_e(),n()},Separator:Lt.Separator,Label:Lt.Label}},Nt=class Nt{constructor(e,t){b(this,"_disposers",[]);b(this,"_allMentionables",Te([]));b(this,"_breadcrumbIds",Te([]));b(this,"_userQuery",Te(""));b(this,"_active",Te(!1));b(this,"_allGroups",Ge([this._active,this._allMentionables],([e,t])=>e?Gl(t):[]));b(this,"_currentGroup",Ge([this._breadcrumbIds,this._allGroups],([e,t])=>{if(e.length===0)return;const n=e[e.length-1];return t.find(o=>Ht(o)&&o.id===n)}));b(this,"dispose",()=>{for(const e of this._disposers)e()});b(this,"openDropdown",()=>{this._active.set(!0)});b(this,"closeDropdown",()=>{this._active.set(!1),this._resetState()});b(this,"toggleDropdown",()=>ce(this._active)?(this.closeDropdown(),!1):(this.openDropdown(),!0));b(this,"pushBreadcrumb",e=>{ce(this._active)&&this._breadcrumbIds.update(t=>[...t,e.id])});b(this,"popBreadcrumb",()=>{ce(this._active)&&this._breadcrumbIds.update(e=>e.slice(0,-1))});b(this,"selectMentionable",e=>{var o;const t=this._chatModel.extensionClient,n=this._chatModel.specialContextInputModel;return Ht(e)&&e.type==="breadcrumb"?(this.pushBreadcrumb(e),!0):e.type==="breadcrumb-back"?(this.popBreadcrumb(),!0):vo(e)?(n.markAllActive(),this.closeDropdown(),t.reportWebviewClientEvent(ci.chatRestoreDefaultContext),!0):e.clearContext?(n.markAllInactive(),this.closeDropdown(),t.reportWebviewClientEvent(ci.chatClearContext),!0):e.userGuidelines?(t.openSettingsPage("guidelines"),this.closeDropdown(),!0):((o=this._insertMentionNode)==null||o.call(this,e),this.closeDropdown(),!0)});b(this,"_displayItems",Ge([this._active,this._breadcrumbIds,this._userQuery,this._currentGroup,this.allGroups],([e,t,n,o,i])=>{if(!e)return[];if(t.length>0&&o)return[{...o,type:"breadcrumb-back"},...o.group.items.slice(0,Nt.SINGLE_GROUP_MAX_ITEMS).map(s=>({...s,type:"item"}))];if(n.length>0){const s=wd(ce(this._userQuery)).map(a=>({...a,type:"item"}));return i.flatMap(a=>[{...a,type:"breadcrumb"},...a.group.items.slice(0,Nt.MULTI_GROUP_MAX_ITEMS).map(l=>({...l,type:"item"}))]).concat(s)}return[{...Qs,type:"item"},...i.map(s=>({...s,type:"breadcrumb"})),{...ea,type:"item"},{...ta,type:"item"}]}));b(this,"_refreshSeqNum",0);b(this,"_refreshMentionables",Yl(async()=>{if(!ce(this._active))return;this._refreshSeqNum++;const e=this._refreshSeqNum,t=this._chatModel.currentConversationModel&&Zl(this._chatModel.currentConversationModel),n=ce(this._userQuery),o=await this._chatModel.extensionClient.getSuggestions(n,t);e===this._refreshSeqNum&&this._allMentionables.set(Al({query:n,mentionables:o}))},Nt.REFRESH_THROTTLE_MS,{leading:!0,trailing:!0}));this._chatModel=e,this._insertMentionNode=t,this._disposers.push(this._userQuery.subscribe(this._refreshMentionables)),this._disposers.push(this._active.subscribe(this._refreshMentionables))}get allGroups(){return this._allGroups}get currentGroup(){return this._currentGroup}get breadcrumbIds(){return this._breadcrumbIds}get displayItems(){return this._displayItems}get active(){return this._active}get userQuery(){return this._userQuery}_resetState(){this._breadcrumbIds.set([]),this._userQuery.set("")}};b(Nt,"REFRESH_THROTTLE_MS",600),b(Nt,"SINGLE_GROUP_MAX_ITEMS",12),b(Nt,"MULTI_GROUP_MAX_ITEMS",6);let Ko=Nt;const Al=({query:r,mentionables:e,returnAllIfNoResults:t=!0,threshold:n=1})=>{if(r.length<=1)return e;const o=new yn(e,{keys:["label"],threshold:n,minMatchCharLength:0,ignoreLocation:!0,includeScore:!0,useExtendedSearch:!1,shouldSort:!0,findAllMatches:!0}).search(r);return o.length===0&&t?e:o.map(i=>i.item)},wd=r=>Al({query:r,mentionables:[Qs,ea,ta],returnAllIfNoResults:!1,threshold:.6});var bd=bt('<svg width="113" height="112" viewBox="0 0 113 112" fill="none" xmlns="http://www.w3.org/2000/svg"><g id="Page-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd"><path d="M2.08,107.33 C3.82,110.98 6.43,114.11 9.73,115.85 C13.73,118.11 17.73,118.28 20.16,118.28 C20.86,118.28 21.38,118.28 22.08,118.28 C22.6,118.28 22.95,118.28 23.12,118.28 L90.23,118.28 C90.58,118.28 90.92,118.28 91.27,118.28 C91.97,118.28 92.49,118.28 93.18,118.28 C95.62,118.28 99.62,118.11 103.61,115.85 C106.92,113.93 109.53,110.98 111.26,107.33 M111.26,107.33 C112.66,104.02 113.35,100.55 113.35,96.9 L113.35,87.33 C115.09,86.29 116.65,85.07 117.87,83.16 C119.96,80.38 121,76.9 121,73.08 C121,69.25 119.78,65.43 117.52,62.47 C116.31,60.91 114.92,59.86 113.35,59 L113.35,46.13 C113.35,40.22 111.79,34.66 107.96,30.48 C104.31,26.31 99.44,24.57 94.23,24.57 L92.49,24.57 L92.31,24.57 C92.66,24.57 91.27,15.18 91.1,14.31 C90.4,11.01 89.18,7.71 87.45,4.93 C81.19,-5.85 67.97,-4.98 56.5,-4.98 C45.03,-4.98 31.81,-5.68 25.55,4.93 C23.82,7.88 22.77,11.01 21.9,14.31 C21.73,15.18 20.16,24.57 20.69,24.57 L20.51,24.57 L18.77,24.57 C13.56,24.57 8.86,26.31 5.04,30.48 C1.39,34.66 -0.35,40.22 -0.35,46.13 L-0.35,59 C-1.92,59.86 -3.31,61.08 -4.52,62.47 C-6.78,65.43 -8,69.08 -8,73.08 C-8,76.73 -6.96,80.21 -4.87,83.16 C-3.65,84.9 -2.09,86.29 -0.35,87.33 L-0.35,96.9 C-0.35,100.55 0.35,104.02 1.74,107.33" id="Shape" fill-rule="nonzero"></path><path d="M27.29,66.65 L36.86,66.65 C40.86,66.65 43.98,69.78 43.98,73.78 C43.98,77.77 40.86,80.9 36.86,80.9 L27.29,80.9 C23.3,80.9 20.17,77.77 20.17,73.78 C20.17,69.95 23.47,66.65 27.29,66.65 Z" id="Path" fill-rule="nonzero"></path><path d="M75.45,66.65 L85.01,66.65 C89.01,66.65 92.14,69.78 92.14,73.78 C92.14,77.77 89.01,80.9 85.01,80.9 L75.45,80.9 C71.45,80.9 68.32,77.77 68.32,73.78 C68.32,69.95 71.63,66.65 75.45,66.65 Z" id="Path" fill-rule="nonzero"></path><path d="M17.38,110.98 C15.64,110.98 14.08,110.63 12.86,110.11 C11.65,109.41 10.78,108.54 10.08,107.33 C9.39,106.11 9.21,104.55 9.21,102.63 L9.21,84.03 C9.21,81.77 8.69,80.03 7.82,78.99 C6.95,77.95 5.39,77.42 2.95,77.42 C2.26,77.42 1.74,77.08 1.21,76.56 C0.69,76.03 0.52,75.34 0.52,74.64 C0.52,73.95 0.69,73.25 1.21,72.73 C1.74,72.21 2.26,72.03 2.95,71.86 C5.21,71.86 6.78,71.34 7.82,70.3 C8.69,69.25 9.21,67.69 9.21,65.43 L9.21,46.65 C9.21,43.87 9.91,41.78 11.3,40.39 C12.69,39 14.78,38.31 17.56,38.31 L39.46,38.31 C40.33,38.31 41.03,38.48 41.55,39 C42.07,39.52 42.42,40.05 42.42,40.91 C42.42,41.61 42.24,42.31 41.72,42.83 C41.2,43.35 40.68,43.7 39.81,43.7 L19.47,43.7 C18.25,43.7 17.38,44.04 16.86,44.57 C16.34,45.09 15.99,46.13 15.99,47.35 L15.99,66.12 C15.99,67.69 15.64,69.25 14.95,70.64 C14.25,72.03 13.38,73.08 12.34,73.77 C11.3,74.47 9.91,74.99 8.52,74.99 L8.52,74.47 C10.08,74.47 11.3,74.82 12.34,75.69 C13.38,76.38 14.25,77.42 14.95,78.82 C15.64,80.21 15.99,81.6 15.99,83.34 L15.99,102.11 C15.99,103.33 16.34,104.37 16.86,104.89 C17.38,105.59 18.43,105.76 19.47,105.76 L39.81,105.76 C40.51,105.76 41.2,106.11 41.72,106.63 C42.24,107.15 42.42,107.85 42.42,108.54 C42.42,109.24 42.07,109.94 41.55,110.46 C41.03,110.98 40.33,111.33 39.46,111.33 L17.38,110.98 Z" id="Path" fill="currentColor" fill-rule="nonzero"></path><path d="M74.06,110.98 C73.19,110.98 72.49,110.63 71.97,110.11 C71.45,109.59 71.1,108.89 71.1,108.2 C71.1,107.5 71.28,106.81 71.8,106.29 C72.32,105.76 72.84,105.42 73.71,105.42 L94.05,105.42 C95.27,105.42 96.14,105.07 96.66,104.55 C97.18,104.03 97.53,102.98 97.53,101.77 L97.53,83.16 C97.53,81.6 97.88,80.03 98.57,78.64 C99.27,77.25 100.14,76.21 101.18,75.51 C102.22,74.82 103.61,74.3 105.01,74.3 L105.01,74.64 C103.44,74.64 102.22,74.3 101.18,73.43 C100.14,72.73 99.27,71.51 98.57,70.3 C97.88,68.91 97.53,67.52 97.53,65.78 L97.53,47 C97.53,45.78 97.18,44.74 96.66,44.22 C95.96,43.7 95.1,43.35 94.05,43.35 L73.71,43.35 C73.02,43.35 72.32,43 71.8,42.48 C71.28,41.96 71.1,41.26 71.1,40.57 C71.1,39.87 71.45,39.18 71.97,38.66 C72.49,38.13 73.19,37.96 74.06,37.96 L95.96,37.96 C98.57,37.96 100.66,38.66 102.22,40.05 C103.61,41.44 104.31,43.52 104.31,46.31 L104.31,64.91 C104.31,67.17 104.83,68.73 105.7,69.78 C106.57,70.82 108.13,71.34 110.57,71.34 C111.26,71.34 111.79,71.69 112.31,72.21 C112.83,72.73 113,73.25 113,74.12 C113,74.82 112.83,75.51 112.31,76.04 C111.96,76.56 111.26,76.9 110.57,76.9 C108.31,76.9 106.74,77.43 105.7,78.47 C104.83,79.51 104.31,81.25 104.31,83.51 L104.31,102.11 C104.31,103.85 103.96,105.42 103.44,106.81 C102.75,108.02 101.88,109.07 100.66,109.59 C99.44,110.28 97.88,110.46 96.14,110.46 L74.06,110.98 Z" id="Path" fill="currentColor" fill-rule="nonzero"></path><path d="M77.88,64.04 L75.1,67.69 C73.19,70.12 72.15,70.82 70.24,70.82 C69.19,70.82 68.32,70.3 68.32,69.43 C68.32,68.73 68.67,68.21 69.02,67.52 L73.36,61.43 C74.41,59.87 75.62,59 77.54,59 L78.06,59 C79.97,59 81.19,59.87 82.23,61.43 L86.58,67.52 C87.1,68.21 87.27,68.91 87.27,69.43 C87.27,70.3 86.4,70.82 85.36,70.82 C83.45,70.82 82.4,70.12 80.49,67.69 L77.88,64.04 Z" id="Path" fill="currentColor" fill-rule="nonzero"></path><path d="M37.2,64.04 L34.42,67.69 C32.51,70.12 31.46,70.82 29.55,70.82 C28.51,70.82 27.64,70.3 27.64,69.43 C27.64,68.73 27.99,68.21 28.34,67.52 L32.68,61.43 C33.73,59.87 34.94,59 36.85,59 L37.38,59 C39.29,59 40.51,59.87 41.55,61.43 L45.89,67.52 C46.42,68.21 46.59,68.91 46.59,69.43 C46.59,70.3 45.72,70.82 44.68,70.82 C42.77,70.82 41.72,70.12 39.81,67.69 L37.2,64.04 Z" id="Path" fill="currentColor" fill-rule="nonzero"></path><path d="M81.88,6.84 L75.8,23.01 C75.45,23.88 74.41,24.05 72.67,23.53 C70.93,23.01 70.24,22.14 70.58,21.27 C71.8,17.1 72.67,13.97 73.36,11.71 C74.06,9.45 74.58,7.71 74.93,6.67 C75.28,5.62 75.45,5.1 75.45,4.93 C75.45,4.75 75.62,4.58 75.62,4.58 C75.97,3.36 77.36,3.19 79.45,3.89 C81.36,4.75 82.23,5.62 81.88,6.84 Z" id="Path" fill="currentColor" fill-rule="nonzero"></path><path d="M30.07,6.84 L36.16,23.01 C36.51,23.88 37.55,24.05 39.29,23.53 C41.03,23.01 41.72,22.14 41.37,21.27 C40.16,17.1 39.29,13.97 38.59,11.71 C37.9,9.45 37.38,7.71 37.03,6.67 C36.68,5.62 36.51,5.1 36.51,4.93 C36.51,4.75 36.33,4.58 36.33,4.58 C35.98,3.36 34.59,3.19 32.51,3.89 C30.42,4.75 29.55,5.62 30.07,6.84 Z" id="Path" fill="currentColor" fill-rule="nonzero"></path><path d="M59.46,3.01 L58.76,20.4 C58.76,21.44 57.89,21.79 55.98,21.79 C54.07,21.79 53.2,21.27 53.2,20.4 C53.02,16.05 52.85,12.75 52.85,10.32 C52.85,7.88 52.68,6.14 52.68,5.1 C52.68,4.06 52.68,3.54 52.68,3.19 C52.68,2.84 52.68,2.84 52.68,2.84 C52.68,1.62 53.89,0.93 56.15,0.93 C58.41,1.1 59.46,1.62 59.46,3.01 Z" id="Path" fill="currentColor" fill-rule="nonzero"></path><path d="M42.07,79.6 C41.55,79.08 41.03,78.73 39.99,78.73 C39.29,78.73 38.77,78.91 38.25,79.25 C37.73,79.6 37.55,80.12 37.55,80.64 C37.55,80.99 37.55,81.17 37.73,81.34 C37.73,81.51 37.9,81.69 37.9,81.86 C42.42,88.29 48.51,91.77 56.85,91.77 C65.02,91.77 71.28,88.29 75.8,81.86 C75.97,81.69 75.97,81.51 75.97,81.34 C75.97,81.17 76.15,80.82 76.15,80.64 C76.15,80.12 75.8,79.6 75.45,79.25 C74.93,78.91 74.41,78.73 73.71,78.73 C72.84,78.73 72.15,79.08 71.63,79.6 C69.72,82.04 67.46,83.77 65.02,84.99 C62.59,86.21 59.98,86.73 56.68,86.73 C53.55,86.73 50.77,86.21 48.33,84.99 C45.9,83.77 43.98,82.04 42.07,79.6 Z" id="Path" stroke="currentColor" stroke-width="0.452891" fill="currentColor" fill-rule="nonzero"></path></g></svg>');function Us(r){var e=bd();R(r,e)}var xd=bt('<svg width="121" height="115" viewBox="0 0 121 115" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M18.0699 114.34C16.1299 114.34 14.5 114.004 13.17 113.331C11.85 112.659 10.8501 111.637 10.1801 110.286C9.50005 108.934 9.16003 107.284 9.16003 105.34V85.3291C9.16003 82.8631 8.67996 81.0691 7.70996 79.9481C6.73996 78.8331 5.02005 78.2421 2.55005 78.1861C1.79005 78.1861 1.16995 77.8931 0.699951 77.3011C0.229951 76.7161 0 76.0371 0 75.2771C0 74.4551 0.229951 73.7761 0.699951 73.2471C1.16995 72.7171 1.79005 72.4241 2.55005 72.3681C5.02005 72.3061 6.73996 71.7211 7.70996 70.6061C8.67996 69.4911 9.16003 67.7221 9.16003 65.3121V45.3011C9.16003 42.3611 9.92995 40.1251 11.45 38.5991C12.98 37.0731 15.1799 36.3071 18.0699 36.3071H41.59C42.48 36.3071 43.2 36.5751 43.8 37.0981C44.38 37.6281 44.6801 38.3061 44.6801 39.1221C44.6801 39.8881 44.4301 40.5491 43.9301 41.1091C43.4401 41.6701 42.78 41.9501 41.95 41.9501H20.1C18.87 41.9501 17.92 42.2741 17.28 42.9221C16.64 43.5691 16.3101 44.5661 16.3101 45.9241V66.0281C16.3101 67.7911 15.96 69.3911 15.25 70.8301C14.55 72.2751 13.6101 73.4021 12.4301 74.2311C11.2501 75.0531 9.88004 75.4641 8.29004 75.4641V75.1091C9.88004 75.1091 11.2501 75.5201 12.4301 76.3421C13.6101 77.1641 14.55 78.2981 15.25 79.7431C15.96 81.1811 16.3101 82.7821 16.3101 84.5451V104.736C16.3101 106.094 16.64 107.091 17.28 107.738C17.93 108.392 18.87 108.71 20.1 108.71H41.95C42.77 108.71 43.4301 108.99 43.9301 109.551C44.4401 110.111 44.6801 110.771 44.6801 111.538C44.6801 112.304 44.39 112.958 43.8 113.518C43.2 114.079 42.48 114.359 41.59 114.359H18.0699V114.34Z" fill="currentColor"></path><path d="M78.84 114.339C77.95 114.339 77.22 114.059 76.63 113.499C76.04 112.938 75.75 112.278 75.75 111.518C75.75 110.758 76 110.092 76.5 109.531C76.99 108.971 77.65 108.69 78.48 108.69H100.32C101.56 108.69 102.5 108.373 103.15 107.719C103.79 107.071 104.12 106.075 104.12 104.717V84.5251C104.12 82.7621 104.47 81.1621 105.18 79.7231C105.88 78.2781 106.82 77.1511 108 76.3231C109.18 75.5001 110.55 75.0891 112.14 75.0891V75.444C110.55 75.444 109.18 75.0331 108 74.2111C106.82 73.3891 105.88 72.256 105.18 70.811C104.47 69.372 104.12 67.771 104.12 66.009V45.9041C104.12 44.5531 103.79 43.556 103.15 42.902C102.5 42.254 101.56 41.9301 100.32 41.9301H78.48C77.66 41.9301 77 41.6501 76.5 41.0901C76 40.5351 75.75 39.8691 75.75 39.1031C75.75 38.2811 76.04 37.608 76.63 37.079C77.22 36.549 77.95 36.2881 78.84 36.2881H102.36C105.25 36.2881 107.44 37.0541 108.98 38.5801C110.51 40.1061 111.27 42.3421 111.27 45.2811V65.2921C111.27 67.7031 111.75 69.4721 112.72 70.5861C113.69 71.7011 115.41 72.2931 117.88 72.3491C118.64 72.4051 119.26 72.6981 119.73 73.2271C120.2 73.7571 120.43 74.4351 120.43 75.2581C120.43 76.0241 120.2 76.696 119.73 77.282C119.26 77.867 118.64 78.1661 117.88 78.1661C115.41 78.2221 113.69 78.8141 112.72 79.9291C111.75 81.0441 111.27 82.8371 111.27 85.3101V105.321C111.27 107.264 110.93 108.908 110.25 110.266C109.57 111.624 108.58 112.633 107.26 113.312C105.93 113.984 104.3 114.321 102.36 114.321H78.84V114.339Z" fill="currentColor"></path><path d="M79.92 76.025C83.88 76.025 87.09 72.8159 87.09 68.8569C87.09 64.8979 83.88 61.689 79.92 61.689C75.96 61.689 72.75 64.8979 72.75 68.8569C72.75 72.8159 75.96 76.025 79.92 76.025Z" fill="currentColor"></path><path d="M39.8301 76.025C43.7901 76.025 46.9901 72.8159 46.9901 68.8569C46.9901 64.8979 43.7901 61.689 39.8301 61.689C35.8701 61.689 32.6602 64.8979 32.6602 68.8569C32.6602 72.8159 35.8701 76.025 39.8301 76.025Z" fill="currentColor"></path><path d="M59.8701 94.9769C59.7201 94.9769 59.5701 94.9769 59.4301 94.9769C59.1501 94.9769 58.8701 94.9609 58.5901 94.9459H58.5101C57.4801 94.8899 56.45 94.7669 55.45 94.5819L55.0901 94.515C54.9501 94.49 54.8001 94.4589 54.6601 94.4279C49.1801 93.2799 44.1001 90.3729 40.3601 86.2509C39.8101 85.6359 39.85 84.877 40.07 84.369C40.32 83.81 40.8101 83.4669 41.3401 83.4669C41.4701 83.4669 41.5901 83.4869 41.7201 83.5179C47.4801 85.1129 53.4301 86.764 59.3101 86.835H60.4301C66.3101 86.764 72.2501 85.1179 78.0001 83.5229C78.1501 83.4819 78.2801 83.4619 78.4101 83.4619C78.9401 83.4619 79.4301 83.81 79.6801 84.364C79.9001 84.872 79.9401 85.6309 79.3901 86.2459C75.6501 90.3679 70.5701 93.2749 65.0901 94.4229C64.9301 94.4589 64.7601 94.4899 64.6001 94.5259L64.3001 94.5819C63.2901 94.7669 62.2601 94.8839 61.2401 94.9409H61.1601C60.8801 94.9609 60.61 94.9669 60.33 94.9719C60.18 94.9719 60.0301 94.9719 59.8801 94.9719L59.8701 94.9769Z" fill="currentColor"></path><path d="M70.1802 51.6431L70.8801 42.201L62.9901 47.5721C62.4701 47.9181 62.1301 48.0911 61.6101 48.0911C60.6501 48.0911 59.8701 47.225 59.8701 46.272C59.8701 45.493 60.4801 44.8871 61.0901 44.6271L70.0101 40.3821L61.0901 36.1381C60.3901 35.7911 59.8701 35.2721 59.8701 34.4921C59.8701 33.5391 60.6501 32.76 61.6101 32.673C62.2101 32.673 62.4701 32.8461 62.9901 33.1931L70.8801 38.563L70.1802 29.121C70.1002 28.082 70.8802 27.2161 71.9202 27.2161C72.9502 27.2161 73.7301 28.082 73.6501 29.121L72.9501 38.563L80.8401 33.1931C81.3601 32.8461 81.7001 32.673 82.2201 32.673C83.1801 32.673 83.9601 33.5391 83.9601 34.4921C83.9601 35.4451 83.3501 35.8781 82.7401 36.1381L73.8201 40.3821L82.7401 44.6271C83.3501 44.8871 83.9601 45.32 83.9601 46.272C83.9601 47.225 83.1801 48.0911 82.2201 48.0911C81.7001 48.0911 81.3601 47.9181 80.8401 47.5721L72.9501 42.201L73.6501 51.6431C73.7301 52.6821 72.9502 53.5491 71.9202 53.5491C70.8802 53.5491 70.1002 52.6821 70.1802 51.6431Z" fill="currentColor"></path><path fill-rule="evenodd" clip-rule="evenodd" d="M73.65 51.6429C73.73 52.6819 72.95 53.5489 71.92 53.5489C70.88 53.5489 70.1001 52.6819 70.1801 51.6429L70.88 42.2009L62.99 47.5719C62.47 47.9179 62.13 48.0909 61.61 48.0909C60.65 48.0909 59.87 47.2249 59.87 46.2719C59.87 45.4929 60.48 44.8869 61.09 44.6269L70.01 40.3819L61.09 36.1379C60.39 35.7909 59.87 35.2719 59.87 34.4919C59.87 33.5389 60.65 32.7599 61.61 32.6729C62.21 32.6729 62.47 32.8459 62.99 33.1929L70.88 38.5629L70.1801 29.1208C70.1001 28.0818 70.88 27.2159 71.92 27.2159C72.95 27.2159 73.73 28.0818 73.65 29.1208L72.95 38.5629L80.84 33.1929C81.36 32.8459 81.7 32.6729 82.22 32.6729C83.18 32.6729 83.96 33.5389 83.96 34.4919C83.96 35.4449 83.35 35.8779 82.74 36.1379L73.8199 40.3819L82.74 44.6269C83.35 44.8869 83.96 45.3199 83.96 46.2719C83.96 47.2249 83.18 48.0909 82.22 48.0909C81.7 48.0909 81.36 47.9179 80.84 47.5719L72.95 42.2009L73.65 51.6429ZM75.04 45.8169L75.46 51.5029C75.62 53.5679 74.04 55.3629 71.92 55.3629C69.79 55.3629 68.21 53.5679 68.37 51.5029L68.79 45.8169L64 49.0809C63.39 49.4879 62.67 49.9059 61.61 49.9059C59.54 49.9059 58.0601 48.1109 58.0601 46.2719C58.0601 44.5309 59.34 43.4159 60.33 42.9759L65.79 40.3819L60.28 37.7609C59.31 37.2759 58.0601 36.2689 58.0601 34.4919C58.0601 32.5089 59.6499 31.0289 61.4399 30.8659L61.52 30.8589H61.61C62.11 30.8589 62.57 30.9339 63.04 31.1309C63.41 31.2859 63.73 31.5049 63.95 31.6519C63.97 31.6629 63.98 31.6729 64 31.6829L64.01 31.6929L68.79 34.9469L68.37 29.2619C68.21 27.1959 69.79 25.4009 71.92 25.4009C74.04 25.4009 75.62 27.1959 75.46 29.2619L75.04 34.9469L79.83 31.6829C80.44 31.2759 81.16 30.8589 82.22 30.8589C84.29 30.8589 85.77 32.6529 85.77 34.4919C85.77 35.3979 85.4699 36.1709 84.9399 36.7699C84.4699 37.3069 83.91 37.6099 83.49 37.7899L78.04 40.3819L83.49 42.9739C83.91 43.1549 84.4699 43.4579 84.9399 43.9949C85.4699 44.5929 85.77 45.3669 85.77 46.2719C85.77 48.1109 84.29 49.9059 82.22 49.9059C81.16 49.9059 80.44 49.4879 79.83 49.0809L79.8199 49.0709L75.04 45.8169Z" fill="currentColor"></path><path d="M41.1499 37.1279L41.8499 27.6859L33.9598 33.0569C33.4398 33.4029 33.0998 33.5759 32.5798 33.5759C31.6198 33.5759 30.8398 32.71 30.8398 31.757C30.8398 30.978 31.4499 30.3709 32.0599 30.1119L40.9799 25.8669L32.0599 21.6229C31.3599 21.2759 30.8398 20.7559 30.8398 19.9769C30.8398 19.0239 31.6198 18.244 32.5798 18.158C33.1798 18.158 33.4398 18.3309 33.9598 18.6779L41.8499 24.0479L41.1499 14.606C41.0699 13.567 41.8499 12.7009 42.8799 12.7009C43.9199 12.7009 44.6999 13.567 44.6199 14.606L43.9199 24.0479L51.8099 18.6779C52.3299 18.3309 52.6698 18.158 53.1898 18.158C54.1498 18.158 54.9299 19.0239 54.9299 19.9769C54.9299 20.9299 54.3198 21.3629 53.7098 21.6229L44.7899 25.8669L53.7098 30.1119C54.3198 30.3709 54.9299 30.805 54.9299 31.757C54.9299 32.71 54.1498 33.5759 53.1898 33.5759C52.6698 33.5759 52.3299 33.4029 51.8099 33.0569L43.9199 27.6859L44.6199 37.1279C44.6999 38.1669 43.9199 39.0339 42.8799 39.0339C41.8499 39.0339 41.0699 38.1669 41.1499 37.1279Z" fill="currentColor"></path><path fill-rule="evenodd" clip-rule="evenodd" d="M44.62 37.128C44.7 38.167 43.92 39.034 42.88 39.034C41.85 39.034 41.07 38.167 41.15 37.128L41.85 27.686L33.96 33.057C33.44 33.403 33.1 33.576 32.58 33.576C31.62 33.576 30.84 32.71 30.84 31.757C30.84 30.978 31.4501 30.371 32.0601 30.112L40.98 25.867L32.0601 21.623C31.3601 21.276 30.84 20.756 30.84 19.977C30.84 19.024 31.62 18.244 32.58 18.158C33.18 18.158 33.44 18.331 33.96 18.678L41.85 24.048L41.15 14.606C41.07 13.567 41.85 12.701 42.88 12.701C43.92 12.701 44.7 13.567 44.62 14.606L43.92 24.048L51.8101 18.678C52.3301 18.331 52.6699 18.158 53.1899 18.158C54.1499 18.158 54.9301 19.024 54.9301 19.977C54.9301 20.93 54.32 21.363 53.71 21.623L44.79 25.867L53.71 30.112C54.32 30.371 54.9301 30.805 54.9301 31.757C54.9301 32.71 54.1499 33.576 53.1899 33.576C52.6699 33.576 52.3301 33.403 51.8101 33.057L43.92 27.686L44.62 37.128ZM46.01 31.302L46.4301 36.988C46.5901 39.053 45.01 40.848 42.88 40.848C40.76 40.848 39.18 39.053 39.34 36.988L39.76 31.302L34.97 34.566C34.36 34.973 33.64 35.391 32.58 35.391C30.51 35.391 29.03 33.596 29.03 31.757C29.03 30.016 30.31 28.9 31.3 28.461L36.76 25.867L31.25 23.246C30.28 22.761 29.03 21.754 29.03 19.977C29.03 17.994 30.62 16.514 32.41 16.351L32.49 16.343H32.58C33.08 16.343 33.54 16.419 34.01 16.616C34.38 16.771 34.7 16.99 34.92 17.137C34.94 17.148 34.95 17.158 34.97 17.168L34.98 17.178L39.76 20.432L39.34 14.747C39.18 12.681 40.76 10.886 42.88 10.886C45.01 10.886 46.5901 12.681 46.4301 14.747L46.01 20.432L50.8 17.168C51.41 16.761 52.1299 16.343 53.1899 16.343C55.2599 16.343 56.74 18.138 56.74 19.977C56.74 20.882 56.44 21.656 55.91 22.254C55.44 22.791 54.88 23.095 54.46 23.275L49.01 25.867L54.46 28.459C54.88 28.639 55.44 28.943 55.91 29.48C56.44 30.078 56.74 30.852 56.74 31.757C56.74 33.596 55.2599 35.391 53.1899 35.391C52.1299 35.391 51.41 34.973 50.8 34.566L50.79 34.556L46.01 31.302Z" fill="currentColor"></path><path d="M84.7001 26.2419L85.3901 16.7999L77.5101 22.1699C76.9901 22.5169 76.6401 22.6899 76.1201 22.6899C75.1701 22.6899 74.3901 21.8239 74.3901 20.8709C74.3901 20.0909 75.0001 19.4849 75.6001 19.2249L84.5201 14.981L75.6001 10.736C74.9101 10.39 74.3901 9.86994 74.3901 9.09094C74.3901 8.13794 75.1701 7.35791 76.1201 7.27191C76.7301 7.27191 76.9901 7.44495 77.5101 7.79095L85.3901 13.1619L84.7001 3.71991C84.6101 2.68091 85.3902 1.81396 86.4302 1.81396C87.4702 1.81396 88.2502 2.68091 88.1602 3.71991L87.4701 13.1619L95.3501 7.79095C95.8701 7.44495 96.2201 7.27191 96.7401 7.27191C97.6901 7.27191 98.4701 8.13794 98.4701 9.09094C98.4701 10.0429 97.8601 10.476 97.2601 10.736L88.3401 14.981L97.2601 19.2249C97.8601 19.4849 98.4701 19.9179 98.4701 20.8709C98.4701 21.8239 97.6901 22.6899 96.7401 22.6899C96.2201 22.6899 95.8701 22.5169 95.3501 22.1699L87.4701 16.7999L88.1602 26.2419C88.2502 27.2809 87.4702 28.1469 86.4302 28.1469C85.3902 28.1469 84.6101 27.2809 84.7001 26.2419Z" fill="currentColor"></path><path fill-rule="evenodd" clip-rule="evenodd" d="M88.1602 26.242C88.2502 27.281 87.4702 28.147 86.4302 28.147C85.3902 28.147 84.6101 27.281 84.7001 26.242L85.3901 16.8L77.5101 22.17C76.9901 22.517 76.6401 22.69 76.1201 22.69C75.1701 22.69 74.3901 21.824 74.3901 20.871C74.3901 20.091 75.0001 19.485 75.6001 19.225L84.5201 14.981L75.6001 10.736C74.9101 10.39 74.3901 9.87 74.3901 9.091C74.3901 8.138 75.1701 7.35797 76.1201 7.27197C76.7301 7.27197 76.9901 7.44502 77.5101 7.79102L85.3901 13.162L84.7001 3.71997C84.6101 2.68097 85.3902 1.81403 86.4302 1.81403C87.4702 1.81403 88.2502 2.68097 88.1602 3.71997L87.4701 13.162L95.3501 7.79102C95.8701 7.44502 96.2201 7.27197 96.7401 7.27197C97.6901 7.27197 98.4701 8.138 98.4701 9.091C98.4701 10.043 97.8601 10.476 97.2601 10.736L88.3401 14.981L97.2601 19.225C97.8601 19.485 98.4701 19.918 98.4701 20.871C98.4701 21.824 97.6901 22.69 96.7401 22.69C96.2201 22.69 95.8701 22.517 95.3501 22.17L87.4701 16.8L88.1602 26.242ZM89.5502 20.416L89.9701 26.101C90.1401 28.167 88.5502 29.962 86.4302 29.962C84.3102 29.962 82.7201 28.167 82.8901 26.101L83.3102 20.416L78.5101 23.68C77.9001 24.087 77.1901 24.504 76.1201 24.504C74.0601 24.504 72.5801 22.71 72.5801 20.871C72.5801 19.13 73.8601 18.014 74.8501 17.574L80.3002 14.981L74.7902 12.359C73.8202 11.874 72.5801 10.867 72.5801 9.091C72.5801 7.108 74.1601 5.62803 75.9601 5.46503L76.0402 5.45697H76.1201C76.6201 5.45697 77.0902 5.533 77.5502 5.729C77.9202 5.885 78.2501 6.10398 78.4701 6.25098C78.4801 6.26198 78.5001 6.27198 78.5101 6.28198L78.5302 6.29199L83.3102 9.54602L82.8901 3.85999C82.7201 1.79399 84.3102 0 86.4302 0C88.5502 0 90.1401 1.79399 89.9701 3.85999L89.5502 9.54602L94.3501 6.28198C94.9601 5.87498 95.6701 5.45697 96.7401 5.45697C98.8101 5.45697 100.28 7.252 100.28 9.091C100.28 9.996 99.9801 10.77 99.4601 11.368C98.9901 11.905 98.4201 12.208 98.0101 12.389L92.5602 14.981L98.0101 17.573C98.4201 17.753 98.9901 18.056 99.4601 18.593C99.9801 19.192 100.28 19.965 100.28 20.871C100.28 22.71 98.8101 24.504 96.7401 24.504C95.6701 24.504 94.9601 24.087 94.3501 23.68L94.3301 23.67L89.5502 20.416Z" fill="currentColor"></path></svg>');function kd(r){var e=xd();R(r,e)}var Md=bt('<svg width="121px" height="116px" viewBox="0 0 121 116" version="1.1" xmlns="http://www.w3.org/2000/svg"><g id="Page-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd"><path d="M18.068,99.825 C16.131,99.825 14.499,99.489 13.173,98.816 C11.852,98.143 10.85,97.122 10.177,95.77 C9.504,94.419 9.162,92.768 9.162,90.825 L9.162,70.814 C9.162,68.348 8.676,66.554 7.711,65.433 C6.739,64.318 5.02,63.726 2.554,63.67 C1.787,63.67 1.171,63.378 0.704,62.786 C0.23,62.2 0,61.522 0,60.762 C0,59.94 0.23,59.261 0.704,58.731 C1.171,58.202 1.794,57.909 2.554,57.853 C5.02,57.791 6.739,57.205 7.711,56.091 C8.682,54.976 9.162,53.207 9.162,50.797 L9.162,30.785 C9.162,27.846 9.928,25.61 11.454,24.084 C12.98,22.558 15.184,21.792 18.068,21.792 L41.592,21.792 C42.476,21.792 43.205,22.06 43.797,22.583 C44.382,23.112 44.681,23.791 44.681,24.607 C44.681,25.373 44.432,26.033 43.934,26.594 C43.435,27.154 42.775,27.435 41.947,27.435 L20.105,27.435 C18.865,27.435 17.925,27.758 17.283,28.406 C16.636,29.054 16.312,30.05 16.312,31.408 L16.312,51.513 C16.312,53.275 15.957,54.876 15.253,56.315 C14.549,57.76 13.609,58.887 12.431,59.715 C11.254,60.537 9.878,60.949 8.29,60.949 L8.29,60.594 C9.878,60.594 11.254,61.005 12.431,61.827 C13.609,62.649 14.549,63.782 15.253,65.227 C15.957,66.666 16.312,68.267 16.312,70.029 L16.312,90.221 C16.312,91.579 16.636,92.575 17.283,93.223 C17.931,93.877 18.871,94.195 20.105,94.195 L41.947,94.195 C42.769,94.195 43.429,94.475 43.934,95.035 C44.438,95.596 44.681,96.256 44.681,97.022 C44.681,97.788 44.388,98.442 43.797,99.003 C43.205,99.563 42.476,99.844 41.592,99.844 L18.068,99.844 L18.068,99.825 Z" id="Path" fill="currentColor" fill-rule="nonzero"></path><path d="M78.838,99.825 C77.953,99.825 77.224,99.545 76.633,98.984 C76.041,98.424 75.748,97.764 75.748,97.004 C75.748,96.244 75.997,95.578 76.496,95.017 C76.994,94.457 77.654,94.176 78.483,94.176 L100.325,94.176 C101.564,94.176 102.505,93.859 103.146,93.205 C103.794,92.557 104.118,91.56 104.118,90.203 L104.118,70.011 C104.118,68.248 104.473,66.648 105.177,65.209 C105.88,63.764 106.821,62.637 107.998,61.808 C109.175,60.986 110.551,60.575 112.14,60.575 L112.14,60.93 C110.551,60.93 109.175,60.519 107.998,59.697 C106.821,58.875 105.88,57.741 105.177,56.296 C104.473,54.858 104.118,53.257 104.118,51.494 L104.118,31.39 C104.118,30.038 103.794,29.042 103.146,28.388 C102.498,27.74 101.558,27.416 100.325,27.416 L78.483,27.416 C77.66,27.416 77,27.136 76.496,26.575 C75.997,26.021 75.748,25.355 75.748,24.589 C75.748,23.767 76.041,23.094 76.633,22.564 C77.218,22.035 77.953,21.773 78.838,21.773 L102.361,21.773 C105.245,21.773 107.444,22.54 108.976,24.065 C110.508,25.591 111.268,27.827 111.268,30.767 L111.268,50.778 C111.268,53.189 111.754,54.957 112.719,56.072 C113.69,57.187 115.409,57.779 117.876,57.835 C118.642,57.891 119.258,58.184 119.726,58.713 C120.199,59.242 120.429,59.921 120.429,60.743 C120.429,61.509 120.199,62.182 119.726,62.767 C119.258,63.353 118.636,63.652 117.876,63.652 C115.409,63.708 113.69,64.3 112.719,65.414 C111.747,66.529 111.268,68.323 111.268,70.796 L111.268,90.807 C111.268,92.75 110.931,94.394 110.253,95.752 C109.574,97.11 108.577,98.119 107.257,98.798 C105.93,99.47 104.298,99.807 102.361,99.807 L78.838,99.807 L78.838,99.825 Z" id="Path" fill="currentColor" fill-rule="nonzero"></path><path d="M39.827,61.511 C43.785,61.511 46.995,58.301 46.995,54.343 C46.995,50.384 43.785,47.175 39.827,47.175 C35.868,47.175 32.659,50.384 32.659,54.343 C32.659,58.301 35.868,61.511 39.827,61.511 Z" id="Path" fill="currentColor" fill-rule="nonzero"></path><path d="M61.599,115.942 C65.558,115.942 68.767,112.733 68.767,108.774 C68.767,104.816 65.558,101.607 61.599,101.607 C57.641,101.607 54.432,104.816 54.432,108.774 C54.432,112.733 57.641,115.942 61.599,115.942 Z" id="Path" fill="currentColor" fill-rule="nonzero"></path><path d="M59.872,80.462 C59.724,80.462 59.575,80.462 59.426,80.462 C59.149,80.462 58.872,80.447 58.59,80.431 L58.514,80.431 C57.483,80.375 56.453,80.252 55.453,80.067 L55.089,80.001 C54.945,79.975 54.802,79.944 54.658,79.914 C49.178,78.765 44.097,75.858 40.365,71.736 C39.806,71.121 39.847,70.362 40.072,69.855 C40.318,69.296 40.805,68.952 41.344,68.952 C41.467,68.952 41.595,68.973 41.723,69.004 C47.481,70.598 53.433,72.249 59.313,72.321 L60.431,72.321 C66.312,72.249 72.254,70.603 78.001,69.009 C78.149,68.968 78.278,68.947 78.406,68.947 C78.944,68.947 79.431,69.296 79.677,69.85 C79.903,70.357 79.944,71.116 79.385,71.731 C75.648,75.853 70.572,78.76 65.091,79.908 C64.927,79.944 64.763,79.975 64.599,80.011 L64.297,80.067 C63.292,80.252 62.261,80.37 61.236,80.426 L61.159,80.426 C60.882,80.447 60.605,80.452 60.329,80.457 C60.18,80.457 60.031,80.457 59.882,80.457 L59.872,80.462 Z" id="Path" fill="currentColor" fill-rule="nonzero"></path><path d="M50.388,15.193 C50.342,15.193 50.25,15.171 50.112,15.125 C49.975,15.033 49.654,14.895 49.149,14.712 L37.044,8.934 C36.54,8.659 36.288,8.201 36.288,7.559 C36.288,6.963 36.471,6.298 36.838,5.564 C37.205,4.831 37.617,4.212 38.076,3.707 C38.58,3.157 39.039,2.882 39.451,2.882 C39.818,2.882 40.116,2.974 40.346,3.157 L51.763,10.998 C52.222,11.273 52.451,11.617 52.451,12.03 C52.451,12.58 52.199,13.245 51.694,14.024 C51.19,14.804 50.754,15.193 50.388,15.193 Z" id="Path" fill="currentColor" fill-rule="nonzero"></path><path d="M73.439,12.312 C73.393,12.312 73.302,12.289 73.164,12.243 C73.027,12.151 72.706,12.014 72.201,11.83 L60.096,6.053 C59.592,5.778 59.339,5.319 59.339,4.677 C59.339,4.081 59.523,3.416 59.89,2.682 C60.256,1.949 60.669,1.33 61.128,0.825 C61.632,0.275 62.091,-1.13686838e-13 62.503,-1.13686838e-13 C62.87,-1.13686838e-13 63.168,0.092 63.397,0.275 L74.815,8.116 C75.273,8.391 75.503,8.735 75.503,9.148 C75.503,9.698 75.25,10.363 74.746,11.142 C74.242,11.922 73.806,12.312 73.439,12.312 Z" id="Path" fill="currentColor" fill-rule="nonzero"></path><path d="M52.207,112.646 C52.157,112.676 52.043,112.71 51.864,112.748 C51.657,112.737 51.22,112.794 50.556,112.918 L33.73,114.413 C33.007,114.438 32.439,114.102 32.028,113.406 C31.646,112.76 31.418,111.922 31.346,110.891 C31.273,109.861 31.324,108.925 31.498,108.085 C31.692,107.165 32.013,106.573 32.46,106.308 C32.858,106.073 33.24,105.981 33.606,106.033 L51.009,107.216 C51.683,107.221 52.152,107.447 52.416,107.894 C52.769,108.491 52.921,109.373 52.874,110.541 C52.827,111.71 52.604,112.411 52.207,112.646 Z" id="Path" fill="currentColor" fill-rule="nonzero"></path><path d="M71.184,112.646 C71.234,112.676 71.348,112.71 71.526,112.748 C71.734,112.737 72.17,112.794 72.835,112.918 L89.661,114.413 C90.384,114.438 90.951,114.102 91.363,113.406 C91.745,112.76 91.972,111.922 92.045,110.891 C92.117,109.861 92.067,108.925 91.893,108.085 C91.699,107.165 91.378,106.573 90.93,106.308 C90.533,106.073 90.151,105.981 89.785,106.033 L72.381,107.216 C71.708,107.221 71.239,107.447 70.975,107.894 C70.622,108.491 70.469,109.373 70.516,110.541 C70.564,111.71 70.786,112.411 71.184,112.646 Z" id="Path" fill="currentColor" fill-rule="nonzero"></path><path d="M79.214,52.635 L76.187,56.552 C74.11,59.223 72.983,59.876 70.965,59.876 C69.837,59.876 68.947,59.341 68.947,58.451 C68.947,57.798 69.244,57.086 69.778,56.315 L74.466,49.727 C75.653,48.065 76.9,47.175 78.977,47.175 L79.511,47.175 C81.588,47.175 82.834,48.065 84.021,49.727 L88.71,56.315 C89.244,57.086 89.541,57.798 89.541,58.451 C89.541,59.341 88.651,59.876 87.523,59.876 C85.386,59.876 84.318,59.223 82.241,56.552 L79.214,52.635 Z" id="Path" fill="currentColor" fill-rule="nonzero"></path></g></svg>');function Sd(r){var e=Md();R(r,e)}var Od=bt('<svg width="121" height="111" viewBox="0 0 121 111" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M18.068 110.71C16.131 110.71 14.499 110.38 13.173 109.7C11.852 109.03 10.85 108.01 10.177 106.66C9.504 105.31 9.16199 103.66 9.16199 101.71V81.7001C9.16199 79.2301 8.676 77.4401 7.711 76.3201C6.739 75.2101 5.02002 74.6102 2.55402 74.5602C1.78702 74.5602 1.17098 74.2602 0.703979 73.6702C0.229979 73.0902 0 72.4102 0 71.6502C0 70.8302 0.229979 70.1502 0.703979 69.6202C1.17098 69.0902 1.79402 68.8002 2.55402 68.7402C5.02002 68.6802 6.739 68.0902 7.711 66.9802C8.682 65.8602 9.16199 64.0902 9.16199 61.6802V41.6702C9.16199 38.7302 9.92798 36.5002 11.454 34.9702C12.98 33.4502 15.184 32.6802 18.068 32.6802H41.592C42.476 32.6802 43.205 32.9502 43.797 33.4702C44.382 34.0002 44.681 34.6802 44.681 35.4902C44.681 36.2602 44.432 36.9202 43.934 37.4802C43.435 38.0402 42.775 38.3202 41.947 38.3202H20.105C18.865 38.3202 17.925 38.6502 17.283 39.2902C16.636 39.9402 16.312 40.9402 16.312 42.3002V62.4002C16.312 64.1602 15.957 65.7601 15.253 67.2001C14.549 68.6501 13.609 69.7702 12.431 70.6002C11.254 71.4202 9.87798 71.8401 8.28998 71.8401V71.4802C9.87798 71.4802 11.254 71.8901 12.431 72.7101C13.609 73.5401 14.549 74.6702 15.253 76.1102C15.957 77.5502 16.312 79.1502 16.312 80.9202V101.11C16.312 102.47 16.636 103.46 17.283 104.11C17.931 104.76 18.871 105.08 20.105 105.08H41.947C42.769 105.08 43.429 105.36 43.934 105.92C44.438 106.48 44.681 107.14 44.681 107.91C44.681 108.68 44.388 109.33 43.797 109.89C43.205 110.45 42.476 110.73 41.592 110.73H18.068V110.71Z" fill="currentColor"></path><path d="M78.838 110.71C77.953 110.71 77.225 110.43 76.633 109.87C76.041 109.31 75.749 108.65 75.749 107.89C75.749 107.13 75.998 106.46 76.496 105.9C76.994 105.34 77.654 105.06 78.483 105.06H100.325C101.564 105.06 102.505 104.74 103.146 104.09C103.794 103.44 104.118 102.45 104.118 101.09V80.9002C104.118 79.1302 104.473 77.5301 105.177 76.0901C105.881 74.6501 106.821 73.5201 107.998 72.6901C109.175 71.8701 110.552 71.4601 112.14 71.4601V71.8201C110.552 71.8201 109.175 71.4101 107.998 70.5801C106.821 69.7601 105.881 68.6302 105.177 67.1802C104.473 65.7402 104.118 64.1402 104.118 62.3802V42.2802C104.118 40.9202 103.794 39.9302 103.146 39.2702C102.499 38.6302 101.558 38.3002 100.325 38.3002H78.483C77.661 38.3002 77 38.0202 76.496 37.4602C75.998 36.9102 75.749 36.2402 75.749 35.4702C75.749 34.6502 76.041 33.9802 76.633 33.4502C77.218 32.9202 77.953 32.6602 78.838 32.6602H102.362C105.245 32.6602 107.444 33.4302 108.976 34.9502C110.508 36.4802 111.268 38.7102 111.268 41.6502V61.6602C111.268 64.0702 111.754 65.8401 112.719 66.9601C113.691 68.0701 115.41 68.6602 117.876 68.7202C118.642 68.7802 119.259 69.0702 119.726 69.6002C120.199 70.1302 120.43 70.8102 120.43 71.6302C120.43 72.4002 120.199 73.0702 119.726 73.6502C119.259 74.2402 118.636 74.5402 117.876 74.5402C115.41 74.5902 113.691 75.1902 112.719 76.3002C111.747 77.4202 111.268 79.2102 111.268 81.6802V101.69C111.268 103.64 110.932 105.28 110.253 106.64C109.574 108 108.577 109 107.257 109.68C105.93 110.36 104.299 110.69 102.362 110.69H78.838V110.71Z" fill="currentColor"></path><path d="M39.827 76.03C43.786 76.03 46.995 72.82 46.995 68.86C46.995 64.9 43.786 61.6899 39.827 61.6899C35.868 61.6899 32.659 64.9 32.659 68.86C32.659 72.82 35.868 76.03 39.827 76.03Z" fill="currentColor"></path><path d="M28.903 57.47L28.908 57.46H28.913C29.272 57.09 29.484 56.58 29.484 55.91C29.484 55.44 29.335 54.98 29.096 54.63C28.866 54.29 28.492 53.98 28.016 53.98C27.811 53.98 27.62 54.02 27.465 54.08C27.332 54.13 27.184 54.2 27.074 54.3C23.331 57.37 21.319 61.57 21.319 67.13C21.319 72.7 23.331 76.89 27.074 79.96C27.184 80.06 27.332 80.1399 27.465 80.1899C27.62 80.2399 27.811 80.29 28.016 80.29C28.492 80.29 28.866 79.97 29.096 79.63C29.335 79.28 29.484 78.83 29.484 78.36C29.484 77.68 29.272 77.1701 28.913 76.8101L28.908 76.8H28.903C26.193 74.21 24.895 71.29 24.895 67.13C24.895 62.98 26.193 60.06 28.903 57.47Z" fill="currentColor" stroke="currentColor" stroke-width="0.907194"></path><path d="M50.931 76.8H50.925L50.92 76.8101C50.562 77.1701 50.349 77.68 50.349 78.36C50.349 78.83 50.498 79.28 50.737 79.63C50.967 79.97 51.342 80.29 51.818 80.29C52.022 80.29 52.213 80.2499 52.368 80.1899C52.501 80.1399 52.649 80.06 52.759 79.96C56.502 76.89 58.514 72.7 58.514 67.13C58.514 61.57 56.502 57.37 52.759 54.3C52.649 54.2 52.501 54.13 52.368 54.08C52.213 54.02 52.022 53.98 51.818 53.98C51.342 53.98 50.967 54.29 50.737 54.63C50.498 54.98 50.349 55.44 50.349 55.91C50.349 56.58 50.562 57.09 50.92 57.46H50.925L50.931 57.47C53.64 60.06 54.938 62.98 54.938 67.13C54.938 71.29 53.64 74.21 50.931 76.8Z" fill="currentColor" stroke="currentColor" stroke-width="0.907194"></path><path d="M68.819 57.47L68.825 57.46H68.83C69.188 57.09 69.4 56.58 69.4 55.91C69.4 55.44 69.251 54.98 69.013 54.63C68.782 54.29 68.408 53.98 67.932 53.98C67.728 53.98 67.537 54.02 67.382 54.08C67.248 54.13 67.1 54.2 66.991 54.3C63.248 57.37 61.236 61.57 61.236 67.13C61.236 72.7 63.248 76.89 66.991 79.96C67.1 80.06 67.248 80.1399 67.382 80.1899C67.537 80.2399 67.728 80.29 67.932 80.29C68.408 80.29 68.782 79.97 69.013 79.63C69.251 79.28 69.4 78.83 69.4 78.36C69.4 77.68 69.188 77.1701 68.83 76.8101L68.825 76.8H68.819C66.109 74.21 64.812 71.29 64.812 67.13C64.812 62.98 66.109 60.06 68.819 57.47Z" fill="currentColor" stroke="currentColor" stroke-width="0.907194"></path><path d="M90.847 76.8H90.842L90.836 76.8101C90.478 77.1701 90.266 77.68 90.266 78.36C90.266 78.83 90.415 79.28 90.654 79.63C90.884 79.97 91.258 80.29 91.734 80.29C91.938 80.29 92.129 80.2499 92.284 80.1899C92.418 80.1399 92.566 80.06 92.676 79.96C96.418 76.89 98.431 72.7 98.431 67.13C98.431 61.57 96.418 57.37 92.676 54.3C92.566 54.2 92.418 54.13 92.284 54.08C92.129 54.02 91.938 53.98 91.734 53.98C91.258 53.98 90.884 54.29 90.654 54.63C90.415 54.98 90.266 55.44 90.266 55.91C90.266 56.58 90.478 57.09 90.836 57.46H90.842L90.847 57.47C93.557 60.06 94.854 62.98 94.854 67.13C94.854 71.29 93.557 74.21 90.847 76.8Z" fill="currentColor" stroke="currentColor" stroke-width="0.907194"></path><path d="M44.445 84.1503L44.44 84.1403C43.96 83.5903 43.268 83.2402 42.287 83.2402C41.627 83.2402 40.986 83.4802 40.507 83.8602C40.03 84.2402 39.69 84.7802 39.69 85.3702C39.69 85.6602 39.743 85.9403 39.815 86.1703C39.883 86.3903 39.976 86.5902 40.076 86.7002C44.904 93.5802 51.5 97.3003 60.328 97.3003C69.157 97.3003 75.753 93.5802 80.581 86.7002C80.681 86.5902 80.773 86.3903 80.842 86.1703C80.914 85.9403 80.967 85.6602 80.967 85.3702C80.967 84.7802 80.626 84.2402 80.15 83.8602C79.671 83.4802 79.029 83.2402 78.37 83.2402C77.389 83.2402 76.697 83.5903 76.216 84.1403L76.212 84.1503C74.075 86.7503 71.802 88.6702 69.219 89.9402C66.637 91.2102 63.734 91.8402 60.328 91.8402C56.923 91.8402 54.02 91.2102 51.438 89.9402C48.855 88.6702 46.582 86.7503 44.445 84.1503Z" fill="currentColor" stroke="currentColor" stroke-width="0.453597"></path><path d="M54.456 28.9769L55.988 10.851C56 10.336 56.719 10.095 58.143 10.129C58.748 10.143 59.264 10.2259 59.693 10.3759C60.078 10.5259 60.268 10.718 60.262 10.952C60.338 13.202 60.416 15.4059 60.494 17.5619C60.572 19.7419 60.655 21.6999 60.743 23.4349C60.788 25.1929 60.841 26.587 60.903 27.619C60.922 28.65 60.931 29.166 60.931 29.166C60.923 29.517 60.593 29.7549 59.942 29.8799C59.248 30.0269 58.49 30.091 57.67 30.072C56.763 30.05 56.01 29.9509 55.409 29.7719C54.765 29.6169 54.447 29.3509 54.456 28.9769ZM55.486 4.69092L55.557 0.852965C55.564 0.548965 55.828 0.343951 56.349 0.238951C56.871 0.110951 57.52 0.0559499 58.297 0.0749499C59.117 0.0939499 59.785 0.179945 60.3 0.332945C60.815 0.462945 61.069 0.678946 61.062 0.983947L60.991 4.82092C60.983 5.14892 60.719 5.36491 60.198 5.46991C59.677 5.57491 59.028 5.61791 58.251 5.59991C57.431 5.57991 56.763 5.50591 56.248 5.37591C55.732 5.24691 55.478 5.01792 55.486 4.69092Z" fill="currentColor"></path><path d="M80.121 67.15L77.095 71.0699C75.017 73.7399 73.89 74.39 71.872 74.39C70.744 74.39 69.854 73.86 69.854 72.97C69.854 72.31 70.151 71.6 70.685 70.83L75.373 64.24C76.56 62.58 77.807 61.6899 79.884 61.6899H80.418C82.495 61.6899 83.742 62.58 84.929 64.24L89.617 70.83C90.151 71.6 90.448 72.31 90.448 72.97C90.448 73.86 89.558 74.39 88.43 74.39C86.294 74.39 85.225 73.7399 83.148 71.0699L80.121 67.15Z" fill="currentColor"></path></svg>');function Ed(r){var e=Od();R(r,e)}function Dl(r){switch(r){case Zn.DEFAULT:return Us;case Zn.PROTOTYPER:return Sd;case Zn.BRAINSTORM:return kd;case Zn.REVIEWER:return Ed;default:return Us}}var Td=Y('<span class="c-text-combo__text svelte-1pddlam"><!></span>'),Nd=Y('<div><div class="c-text-combo__gray-text svelte-1pddlam"><!></div></div>'),Ld=Y('<div role="button" tabindex="-1"><!> <!> <!> <!></div>');function Ad(r,e){const t=Wo(e);let n=j(e,"class",8,""),o=j(e,"size",8,1),i=j(e,"align",8,"left"),s=j(e,"greyTextTruncateDirection",8,"right"),a=j(e,"shrink",8,!1);Vt(r,{get size(){return o()},weight:"medium",children:(l,c)=>{var h=Ld();let d;var p=ne(h),f=v=>{var x=De(),O=ie(x);Se(O,e,"leftIcon",{},null),R(v,x)};U(p,v=>{P(()=>t.leftIcon)&&v(f)});var u=le(p,2),m=v=>{var x=Td(),O=ne(x);Se(O,e,"text",{},null),R(v,x)};U(u,v=>{P(()=>t.text)&&v(m)});var g=le(u,2),y=v=>{var x=Nd();let O;var E=ne(x),M=ne(E);Se(M,e,"grayText",{},null),ve(k=>O=Mn(x,1,"c-text-combo__gray svelte-1pddlam",null,O,k),[()=>({"c-text-combo--gray-truncate-left":s()==="left","c-text-combo--gray-truncate-right":s()==="right"})],Je),R(v,x)};U(g,v=>{P(()=>t.grayText)&&v(y)});var C=le(g,2),w=v=>{var x=De(),O=ie(x);Se(O,e,"rightIcon",{},null),R(v,x)};U(C,v=>{P(()=>t.rightIcon)&&v(w)}),ve(v=>d=Mn(h,1,`c-text-combo ${n()}`,"svelte-1pddlam",d,v),[()=>({"c-text-combo--align-right":i()==="right","c-text-combo--shrink":a()})],Je),ke("click",h,function(v){nt.call(this,e,v)}),ke("keydown",h,function(v){nt.call(this,e,v)}),ke("keyup",h,function(v){nt.call(this,e,v)}),ke("blur",h,function(v){nt.call(this,e,v)}),ke("focus",h,function(v){nt.call(this,e,v)}),ke("mouseenter",h,function(v){nt.call(this,e,v)}),ke("mouseleave",h,function(v){nt.call(this,e,v)}),ke("contextmenu",h,function(v){nt.call(this,e,v)}),R(l,h)},$$slots:{default:!0}})}var Dd=bt("<svg><!></svg>");function Il(r,e){const t=Fl(e,["children","$$slots","$$events","$$legacy"]);var n=Dd();Bl(n,()=>({xmlns:"http://www.w3.org/2000/svg","data-ds-icon":"fa",viewBox:"0 0 256 512",...t}));var o=ne(n);jl(o,()=>'<!--! Font Awesome Pro 6.7.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license (Commercial License) Copyright 2024 Fonticons, Inc.--><path d="M192 48c8.8 0 16 7.2 16 16v32h-48c-8.8 0-16 7.2-16 16s7.2 16 16 16h48v64h-48c-8.8 0-16 7.2-16 16s7.2 16 16 16h48v64h-48c-8.8 0-16 7.2-16 16s7.2 16 16 16h48v64h-48c-8.8 0-16 7.2-16 16s7.2 16 16 16h48v32c0 8.8-7.2 16-16 16H64c-8.8 0-16-7.2-16-16V64c0-8.8 7.2-16 16-16zM64 0C28.7 0 0 28.7 0 64v384c0 35.3 28.7 64 64 64h128c35.3 0 64-28.7 64-64V64c0-35.3-28.7-64-64-64z"/>',!0),R(r,n)}var Id=Y('<span slot="leftIcon" class="c-context-menu-item__icon svelte-1a2w9oo"><!></span>'),_d=Y('<span slot="text"> </span>'),$d=Y('<span class="c-mentionable-group-label"><span class="c-mentionable-group-label__text right"> </span></span>');function Rd(r,e){Ie(e,!1);let t=j(e,"item",8),n=j(e,"onSelect",8),o=j(e,"highlight",24,()=>{}),i=Ct();Fe(()=>(T(t()),Ht),()=>{t().type==="breadcrumb-back"?Qe(i,Lt.BreadcrumbBackItem):t().type==="breadcrumb"&&Ht(t())?Qe(i,Lt.BreadcrumbItem):t().type!=="item"||Ht(t())||Qe(i,Lt.Item)}),vt(),ze();var s=De(),a=ie(s);wo(a,()=>Q(i),(l,c)=>{c(l,{get highlight(){return o()},get onSelect(){return n()},children:(h,d)=>{var p=De(),f=ie(p),u=g=>{Ae(g,{get filepath(){return T(t()),P(()=>t().file.pathName)},$$slots:{leftIcon:(y,C)=>{pn(y,{slot:"leftIcon",iconName:"description"})}}})},m=(g,y)=>{var C=v=>{Ae(v,{get filepath(){return T(t()),P(()=>t().folder.pathName)},$$slots:{leftIcon:(x,O)=>{pn(x,{slot:"leftIcon",iconName:"folder_open"})}}})},w=(v,x)=>{var O=M=>{Ae(M,{get filepath(){return T(t()),P(()=>t().externalSource.name)},$$slots:{leftIcon:(k,N)=>{pn(k,{slot:"leftIcon",iconName:"import_contacts"})}}})},E=(M,k)=>{var N=L=>{Ae(L,{get filepath(){return T(t()),P(()=>t().sourceFolder.folderRoot)},$$slots:{leftIcon:(z,A)=>{pn(z,{slot:"leftIcon",iconName:"folder_managed"})}}})},I=(L,z)=>{var A=_=>{Ae(_,{get filepath(){return T(t()),P(()=>t().selection.pathName)},$$slots:{leftIcon:(H,G)=>{pn(H,{slot:"leftIcon",iconName:"text_select_start"})}}})},F=(_,H)=>{var G=ee=>{Ae(ee,{get filepath(){return T(t()),P(()=>t().recentFile.pathName)},$$slots:{leftIcon:(re,ae)=>{pn(re,{slot:"leftIcon",iconName:"description"})}}})},B=(ee,re)=>{var ae=se=>{Ae(se,{get filepath(){return T(t()),P(()=>t().rule.path)},$$slots:{leftIcon:(Z,ue)=>{Il(Z,{slot:"leftIcon",iconName:"rule"})}}})},te=(se,Z)=>{var ue=he=>{var qe=St();ve(()=>je(qe,(T(t()),P(()=>t().label)))),R(he,qe)},X=(he,qe)=>{var dt=xt=>{Ad(xt,{$$slots:{leftIcon:(Nn,zr)=>{var pt=Id(),dn=ne(pt);wo(dn,()=>Dl(t().personality.type),(Fr,Br)=>{Br(Fr,{})}),R(Nn,pt)},text:(Nn,zr)=>{var pt=_d(),dn=ne(pt);ve(()=>je(dn,(T(t()),P(()=>t().label)))),R(Nn,pt)}}})},hn=(xt,Nn)=>{var zr=pt=>{var dn=$d(),Fr=ne(dn),Br=ne(Fr);ve(()=>je(Br,(T(t()),P(()=>t().label)))),R(pt,dn)};U(xt,pt=>{T(vo),T(t()),T(hi),T(fr),P(()=>vo(t())||hi(t())||fr(t()))&&pt(zr)},Nn)};U(he,xt=>{T(ur),T(t()),P(()=>ur(t()))?xt(dt):xt(hn,!1)},qe)};U(se,he=>{T(Ht),T(t()),P(()=>Ht(t()))?he(ue):he(X,!1)},Z)};U(ee,se=>{T(pr),T(t()),P(()=>pr(t()))?se(ae):se(te,!1)},re)};U(_,ee=>{T(dr),T(t()),P(()=>dr(t()))?ee(G):ee(B,!1)},H)};U(L,_=>{T(hr),T(t()),P(()=>hr(t()))?_(A):_(F,!1)},z)};U(M,L=>{T(cr),T(t()),P(()=>cr(t()))?L(N):L(I,!1)},k)};U(v,M=>{T(lr),T(t()),P(()=>lr(t()))?M(O):M(E,!1)},x)};U(g,v=>{T(ar),T(t()),P(()=>ar(t()))?v(C):v(w,!1)},y)};U(f,g=>{T(sr),T(t()),P(()=>sr(t()))?g(u):g(m,!1)}),R(h,p)},$$slots:{default:!0}})}),R(r,s),_e()}var Pd=bt('<svg width="15" height="15" viewBox="0 0 15 15" fill="none" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" clip-rule="evenodd" d="M4.5 1C4.22386 1 4 1.22386 4 1.5C4 1.77614 4.22386 2 4.5 2C5.42215 2 6.0399 2.23054 6.42075 2.56379C6.79286 2.88939 7 3.36626 7 4V7H5.75C5.47386 7 5.25 7.22386 5.25 7.5C5.25 7.77614 5.47386 8 5.75 8H7V11C7 11.6337 6.79286 12.1106 6.42075 12.4362C6.0399 12.7695 5.42215 13 4.5 13C4.22386 13 4 13.2239 4 13.5C4 13.7761 4.22386 14 4.5 14C5.57785 14 6.4601 13.7305 7.07925 13.1888C7.24168 13.0467 7.38169 12.8896 7.5 12.7198C7.61832 12.8896 7.75832 13.0467 7.92075 13.1888C8.5399 13.7305 9.42215 14 10.5 14C10.7761 14 11 13.7761 11 13.5C11 13.2239 10.7761 13 10.5 13C9.57785 13 8.9601 12.7695 8.57925 12.4362C8.20714 12.1106 8 11.6337 8 11V8H9.25C9.52614 8 9.75 7.77614 9.75 7.5C9.75 7.22386 9.52614 7 9.25 7H8V4C8 3.36626 8.20714 2.88939 8.57925 2.56379C8.9601 2.23054 9.57785 2 10.5 2C10.7761 2 11 1.77614 11 1.5C11 1.22386 10.7761 1 10.5 1C9.42215 1 8.5399 1.26946 7.92075 1.81121C7.75832 1.95333 7.61832 2.11043 7.5 2.28023C7.38168 2.11043 7.24168 1.95333 7.07925 1.81121C6.4601 1.26946 5.57785 1 4.5 1Z" fill="currentColor"></path></svg>'),zd=Y('<div><!> <span class="c-guidelines-filespan__text svelte-1jd2qvj" role="button" tabindex="0"> </span></div>'),Fd=bt('<svg width="102" height="9" viewBox="0 0 102 9" fill="none" xmlns="http://www.w3.org/2000/svg"><ellipse cx="51" cy="4.5" rx="51" ry="4.5" fill="currentColor"></ellipse></svg>'),Bd=Y('<div class="c-augment-logo-animated__shadow svelte-hwxi20"><!></div>'),Vd=Y('<div><div class="c-augment-logo-animated__icon svelte-hwxi20"><!></div> <!></div>');function Hd(r,e){Ie(e,!1);const t=Ct(),n=Ct();let o=j(e,"heightPx",8,160),i=j(e,"floatHeight",8,20),s=j(e,"animationDuration",8,3),a=j(e,"showShadow",8,!0),l=j(e,"animated",8,!0);Fe(()=>T(o()),()=>{Qe(t,Math.round(.875*o()))}),Fe(()=>(T(o()),Q(t),T(i()),T(s())),()=>{Qe(n,`
    --augment-logo-height: ${o()}px;
    --augment-logo-icon-size: ${Q(t)}px;
    --augment-logo-float-height: ${i()}px;
    --animation-duration: ${s()}s;
  `)}),vt();var c=Vd();let h;var d=ne(c),p=ne(d);Se(p,e,"default",{},m=>{Ql(m)});var f=le(d,2),u=m=>{var g=Bd();(function(y){var C=Fd();R(y,C)})(ne(g)),R(m,g)};U(f,m=>{a()&&m(u)}),ve(m=>{h=Mn(c,1,"c-augment-logo-animated svelte-hwxi20",null,h,m),Vl(c,Q(n))},[()=>({"c-augment-logo-animated--animated":l()})],Je),R(r,c),_e()}var qd=bt('<svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" clip-rule="evenodd" d="M14.5 2H9L8.65002 2.15002L8 2.79004L7.34998 2.15002L7 2H1.5L1 2.5V12.5L1.5 13H6.78998L7.65002 13.85H8.34998L9.21002 13H14.5L15 12.5V2.5L14.5 2ZM7.5 12.3199L7.32001 12.15L7 12H2V3H6.78998L7.53003 3.73999L7.5 12.3199ZM14 12H9L8.65002 12.15L8.51001 12.28V3.69995L9.21002 3H14V12ZM6 5H3V6H6V5ZM6 9H3V10H6V9ZM3 7H6V8H3V7ZM13 5H10V6H13V5ZM10 7H13V8H10V7ZM10 9H13V10H10V9Z" fill="currentColor"></path></svg>'),jd=bt('<svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" clip-rule="evenodd" d="M7.70996 3H14.5L15.01 3.5V9V13.5L14.51 14H8.74284C8.99647 13.6929 9.21739 13.3578 9.40029 13H13.99V11.49L14 7.48999V5.98999H7.68994L7.67296 6.00697C7.39684 5.81162 7.10191 5.64108 6.79144 5.4986L7.14001 5.15002L7.48999 5H13.99L14 4.01001H7.5L7.14001 3.85999L6.29004 3.01001H2V5.59971C1.6461 5.78062 1.31438 5.99874 1.01001 6.24892V2.5L1.51001 2H6.51001L6.85999 2.15002L7.70996 3Z" fill="currentColor"></path><path d="M6 10.5C6 11.3284 5.32843 12 4.5 12C3.67157 12 3 11.3284 3 10.5C3 9.67157 3.67157 9 4.5 9C5.32843 9 6 9.67157 6 10.5Z" fill="currentColor"></path><path fill-rule="evenodd" clip-rule="evenodd" d="M8 10.5C8 12.433 6.433 14 4.5 14C2.567 14 1 12.433 1 10.5C1 8.567 2.567 7 4.5 7C6.433 7 8 8.567 8 10.5ZM4.5 13C5.88071 13 7 11.8807 7 10.5C7 9.11929 5.88071 8 4.5 8C3.11929 8 2 9.11929 2 10.5C2 11.8807 3.11929 13 4.5 13Z" fill="currentColor"></path></svg>'),Kd=Y("<!> <!>",1),Jd=Y("<!> <!>",1),Wd=Y("<!> <!>",1),Ud=Y("<!> <!>",1),Gd=Y('<div class="l-mention-hover-contents__source-folder svelte-p7en3g"><div class="l-source-folder-name svelte-p7en3g"><!> <!></div> <!></div>'),Zd=Y("<!> <!>",1),Yd=Y('<div class="c-mention-hover-contents__personality-icon svelte-p7en3g"><!></div> <div class="c-mention-hover-contents__personality svelte-p7en3g"><!> <!></div>',1),Xd=Y('<div class="c-mention-hover-contents__task svelte-p7en3g"><!> <!></div>'),Qd=Y("<!> <!>",1),ep=Y('<div class="c-mention-hover-contents svelte-p7en3g"><!></div>');function tp(r,e){Ie(e,!1);let t=j(e,"option",8);ze();var n=ep(),o=ne(n),i=a=>{var l=Kd(),c=ie(l);fi(c);var h=le(c,2);Ae(h,{get filepath(){return T(t()),P(()=>t().file.pathName)}}),R(a,l)},s=(a,l)=>{var c=d=>{var p=Jd(),f=ie(p);fi(f);var u=le(f,2);Ae(u,{get filepath(){return T(t()),P(()=>t().recentFile.pathName)}}),R(d,p)},h=(d,p)=>{var f=m=>{var g=Wd(),y=ie(g);ec(y);var C=le(y,2);Ae(C,{get filepath(){return T(t()),P(()=>t().folder.pathName)}}),R(m,g)},u=(m,g)=>{var y=w=>{var v=Ud(),x=ie(v);(function(E){var M=qd();R(E,M)})(x);var O=le(x,2);Ae(O,{get filepath(){return T(t()),P(()=>t().externalSource.name)}}),R(w,v)},C=(w,v)=>{var x=E=>{var M=Gd(),k=ne(M),N=ne(k);(function(L){var z=jd();R(L,z)})(N);var I=le(N,2);Ae(I,{class:"c-source-folder-item",get filepath(){return T(t()),P(()=>t().sourceFolder.folderRoot)}}),function(L,z){Ie(z,!1);const A=Ct();let F=j(z,"class",8,""),_=j(z,"sourceFolder",8);const H=Ye("chatModel"),G=()=>{H.extensionClient.openGuidelines(_().folderRoot)};Fe(()=>T(_()),()=>{Qe(A,_().guidelinesOverLimit?`Workspace guidelines exceeded ${_().guidelinesLengthLimit} character limit`:"Edit workspace guidelines")}),vt(),ze(),Vt(L,{size:1,children:(B,ee)=>{var re=zd(),ae=ne(re),te=X=>{Xl(X,{})},se=X=>{Xs(X,{slot:"leftIcon",class:"c-guidelines-filespan-warning-icon"})};U(ae,X=>{T(_()),P(()=>!_().guidelinesOverLimit)?X(te):X(se,!1)});var Z=le(ae,2),ue=ne(Z);ve(()=>{Mn(re,1,`c-guidelines-filespan ${F()}`,"svelte-1jd2qvj"),je(ue,Q(A))}),ke("click",Z,G),ke("keydown",Z,()=>{}),R(B,re)},$$slots:{default:!0}}),_e()}(le(k,2),{class:"guidelines-filespan",get sourceFolder(){return T(t()),P(()=>t().sourceFolder)}}),R(E,M)},O=(E,M)=>{var k=I=>{var L=De(),z=ie(L),A=F=>{var _=De(),H=ie(_),G=B=>{Vt(B,{size:1,children:(ee,re)=>{var ae=St();ve(()=>je(ae,(T(t()),P(()=>`Guidelines exceeded length limit of ${t().userGuidelines.lengthLimit} characters`)))),R(ee,ae)},$$slots:{default:!0}})};U(H,B=>{T(t()),P(()=>t().userGuidelines.overLimit)&&B(G)}),R(F,_)};U(z,F=>{T(t()),P(()=>t().userGuidelines.overLimit)&&F(A)}),R(I,L)},N=(I,L)=>{var z=F=>{var _=Zd(),H=ie(_);(function(ee){var re=Pd();R(ee,re)})(H);var G=le(H,2);const B=Je(()=>(T(t()),P(()=>{var ee,re;return`${t().selection.pathName}:L${(ee=t().selection.fullRange)==null?void 0:ee.startLineNumber}-${(re=t().selection.fullRange)==null?void 0:re.endLineNumber}`})));Ae(G,{get filepath(){return Q(B)}}),R(F,_)},A=(F,_)=>{var H=B=>{var ee=Yd(),re=ie(ee);Hd(ne(re),{heightPx:32,floatHeight:4,animationDuration:2.25,children:(Z,ue)=>{var X=De(),he=ie(X);wo(he,()=>Dl(t().personality.type),(qe,dt)=>{dt(qe,{})}),R(Z,X)},$$slots:{default:!0}});var ae=le(re,2),te=ne(ae);Vt(te,{size:2,weight:"medium",children:(Z,ue)=>{var X=St();ve(()=>je(X,(T(t()),P(()=>t().label)))),R(Z,X)},$$slots:{default:!0}});var se=le(te,2);Vt(se,{size:1,children:(Z,ue)=>{var X=St();ve(()=>je(X,(T(t()),P(()=>t().personality.description)))),R(Z,X)},$$slots:{default:!0}}),R(B,ee)},G=(B,ee)=>{var re=te=>{var se=Xd(),Z=ne(se);Vt(Z,{size:2,weight:"bold",children:(he,qe)=>{var dt=St();ve(()=>je(dt,(T(t()),P(()=>t().task.taskTree.name)))),R(he,dt)},$$slots:{default:!0}});var ue=le(Z,2),X=he=>{Vt(he,{size:1,children:(qe,dt)=>{var hn=St();ve(xt=>je(hn,xt),[()=>(T(t()),P(()=>t().task.taskTree.description.trim().replace(/\s+/g," ")))],Je),R(qe,hn)},$$slots:{default:!0}})};U(ue,he=>{T(t()),P(()=>t().task.taskTree.description&&t().task.taskTree.description.trim())&&he(X)}),R(te,se)},ae=(te,se)=>{var Z=X=>{var he=Qd(),qe=ie(he);Il(qe,{});var dt=le(qe,2);const hn=Je(()=>(T(pi),T(ui),T(t()),P(()=>`${pi}/${ui}/${t().rule.path}`)));Ae(dt,{get filepath(){return Q(hn)}}),R(X,he)},ue=X=>{var he=St();ve(()=>je(he,(T(t()),P(()=>t().label)))),R(X,he)};U(te,X=>{T(t()),T(pr),P(()=>t()&&pr(t()))?X(Z):X(ue,!1)},se)};U(B,te=>{T(t()),T(di),P(()=>t()&&di(t()))?te(re):te(ae,!1)},ee)};U(F,B=>{T(t()),T(ur),P(()=>t()&&ur(t()))?B(H):B(G,!1)},_)};U(I,F=>{T(t()),T(hr),P(()=>t()&&hr(t()))?F(z):F(A,!1)},L)};U(E,I=>{T(t()),T(fr),P(()=>t()&&fr(t()))?I(k):I(N,!1)},M)};U(w,E=>{T(t()),T(cr),P(()=>t()&&cr(t()))?E(x):E(O,!1)},v)};U(m,w=>{T(t()),T(lr),P(()=>t()&&lr(t()))?w(y):w(C,!1)},g)};U(d,m=>{T(t()),T(ar),P(()=>t()&&ar(t()))?m(f):m(u,!1)},p)};U(a,d=>{T(t()),T(dr),P(()=>t()&&dr(t()))?d(c):d(h,!1)},l)};U(o,a=>{T(t()),T(sr),P(()=>t()&&sr(t()))?a(i):a(s,!1)}),R(r,n),_e()}var np=Y("<!> <!>",1);function Mp(r,e){Ie(e,!1);const[t,n]=bn(),o=()=>it(h,"$displayItems",t);let i=j(e,"requestEditorFocus",8),s=j(e,"onMentionItemsUpdated",24,()=>{});const a=g=>p(g),l=Ye("chatModel");if(!l)throw new Error("ChatModel not found in context");const c=new Ko(l,p),h=c.displayItems;Wn(()=>{c.dispose()});let d=Ct();function p(g){return!!Q(d)&&(Q(d).insertMention(g),c.closeDropdown(),!0)}function f(g){g===void 0?c.closeDropdown():(c.openDropdown(),c.userQuery.set(g))}function u(g){const y=c.selectMentionable(g);return i()(),y}ze(),Co(yo.Root(r,{triggerCharacter:"@",get onMentionItemsUpdated(){return s()},children:(g,y)=>{var C=np(),w=ie(C);yo.Menu.Root(w,{get mentionables(){return o()},onQueryUpdate:f,onSelectMentionable:u,children:Hl,$$slots:{default:(x,O)=>{const E=Je(()=>O.activeItem);var M=De(),k=ie(M);Zs(k,1,o,Ys,(N,I)=>{const L=Je(()=>Q(I)===Q(E));Rd(N,{get item(){return Q(I)},get highlight(){return Q(L)},onSelect:()=>u(Q(I))})}),R(x,M)}}});var v=le(w,2);yo.ChipTooltip(v,{$$slots:{mentionable:(x,O)=>{const E=Je(()=>O.mentionable);tp(x,{slot:"mentionable",get option(){return Q(E)}})}}}),R(g,C)},$$slots:{default:!0},$$legacy:!0}),g=>Qe(d,g),()=>Q(d)),st(e,"insertMentionNode",a);var m=_e({insertMentionNode:a});return n(),m}const rp=Ue.create({name:"placeholder",addOptions:()=>({emptyEditorClass:"is-editor-empty",emptyNodeClass:"is-empty",placeholder:"Write something …",showOnlyWhenEditable:!0,considerAnyAsEmpty:!1,showOnlyCurrent:!0,includeChildren:!1}),addProseMirrorPlugins(){return[new He({key:new Ve("placeholder"),props:{decorations:({doc:r,selection:e})=>{var t;const n=this.editor.isEditable||!this.options.showOnlyWhenEditable,{anchor:o}=e,i=[];if(!n)return null;const{firstChild:s}=r.content,a=s&&s.type.isLeaf,l=s&&s.isAtom,c=!!this.options.considerAnyAsEmpty||s&&s.type.name===((t=r.type.contentMatch.defaultType)===null||t===void 0?void 0:t.name),h=r.content.childCount<=1&&s&&c&&s.nodeSize<=2&&(!a||!l);return r.descendants((d,p)=>{const f=o>=p&&o<=p+d.nodeSize,u=!d.isLeaf&&!d.childCount;if((f||!this.options.showOnlyCurrent)&&u){const m=[this.options.emptyNodeClass];h&&m.push(this.options.emptyEditorClass);const g=Be.node(p,p+d.nodeSize,{class:m.join(" "),"data-placeholder":typeof this.options.placeholder=="function"?this.options.placeholder({editor:this.editor,node:d,pos:p,hasAnchor:f}):this.options.placeholder});i.push(g)}return this.options.includeChildren}),de.create(r,i)}}})]}});class op{constructor(e){b(this,"_placeholderExtension");b(this,"_editor");b(this,"setPlaceholder",e=>{var t;this._placeholderExtension.options.placeholder=e,(t=this._editor)==null||t.view.updateState(this._editor.view.state)});b(this,"_onUpdate",e=>{this._editor=e});b(this,"_onDestroy",()=>{this._editor=void 0});const t=this._onUpdate.bind(this),n=this._onDestroy.bind(this);this._placeholderExtension=rp.extend({placeholder:e,onUpdate(){var o;(o=this.parent)==null||o.call(this),t(this.editor)},onDestroy(){var o;(o=this.parent)==null||o.call(this),n()}})}get tipTapExtension(){return this._placeholderExtension}}function Sp(r,e){Ie(e,!1);let t=j(e,"placeholder",8,"Type something...");const n=new op(t()),o=Ye(et.CONTEXT_KEY);Wn(o.pluginManager.registerPlugin(n)),Fe(()=>T(t()),()=>{n.setPlaceholder(t())}),vt(),ze(),_e()}const ot=class ot{constructor(e){b(this,"_tipTapExtension");b(this,"_keydownHandler",()=>!1);b(this,"updateOptions",e=>{this._options={...this._options,...e},this._keydownHandler=Ya(this._options.shortcuts)});b(this,"_handleKeyDown",(e,t)=>this._keydownHandler(e,t));this._options=e,this.updateOptions(this._options);const t=this._handleKeyDown,n=ot._getNextPluginId(),o=new Ve(n);this._tipTapExtension=Ue.create({name:n,addProseMirrorPlugins:()=>[new He({key:o,props:{handleKeyDown:t}})]})}get tipTapExtension(){return this._tipTapExtension}};b(ot,"_sequenceId",0),b(ot,"KEYBINDINGS_PLUGIN_KEY_BASE","augment-keybindings-plugin-{}"),b(ot,"_getSequenceId",()=>ot._sequenceId++),b(ot,"_getNextPluginId",()=>{const e=ot._getSequenceId().toString();return ot.KEYBINDINGS_PLUGIN_KEY_BASE.replace("{}",e)});let Jo=ot;function Op(r,e){Ie(e,!1);let t=j(e,"shortcuts",24,()=>({}));const n=Ye(et.CONTEXT_KEY),o=new Jo({shortcuts:t()}),i=n.pluginManager.registerPlugin(o);Wn(i),Fe(()=>T(t()),()=>{o.updateOptions({shortcuts:t()})}),vt(),ze(),_e()}export{Mp as A,tp as C,Ue as E,wp as K,Ft as N,Sp as P,kp as R,K as T,Op as a,et as b,Ve as c,He as d,Il as e,Ko as f,Rd as g,xp as h,Ad as i,Hd as j,Zt as m,bp as n};
