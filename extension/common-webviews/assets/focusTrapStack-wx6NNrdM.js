var g=Object.defineProperty;var p=(e,t,n)=>t in e?g(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n;var i=(e,t,n)=>p(e,typeof t!="symbol"?t+"":t,n);import{I as c}from"./message-broker-DiyMl4p8.js";const l=["jpg","jpeg","png","gif","webp","svg","bmp","tiff","ico"],f=["zip","tar","gz","7z","rar","pdf","doc","docx","ppt","pptx","xls","xlsx","odt","odp","ods","exe","dll","so","dylib","app","msi","deb","rpm","o","a","class","jar","pyc","wasm","mp3","mp4","avi","mov","wav","mkv","DS_Store","db","sqlite","dat"],d=1048576;function x(e){const t=e.lastIndexOf(".");return t===-1||t===e.length-1?"":e.substring(t+1).toUpperCase()}function r(e){const t=e.lastIndexOf(".");return t===-1||t===e.length-1?"":e.substring(t+1).toLowerCase()}function m(e){const t=r(e);return l.includes(t)}function h(e){switch(r(e)){case"jpeg":case"jpg":return c.JPEG;case"png":return c.PNG;case"gif":return c.GIF;case"webp":return c.WEBP;default:return c.IMAGE_FORMAT_UNSPECIFIED}}function v(e){switch(r(e)){case"png":return"image/png";case"jpg":case"jpeg":return"image/jpeg";case"gif":return"image/gif";case"svg":return"image/svg+xml";case"webp":return"image/webp";case"bmp":return"image/bmp";case"ico":return"image/x-icon";default:return"application/octet-stream"}}function E(e){if(m(e))return!1;const t=r(e);return f.includes(t)}function A(e){return e>d}function K(e){return e.replace(/\\/g,"/")}function o(e){return e.split("/").filter(t=>t.length>0)}function j(e){return o(e).at(-1)??""}function k(e){return o(e).slice(0,-1).join("/")}const s=class s{static getStack(){const t=typeof document<"u"?document:null;return t?(t[s.STACK_KEY]||(t[s.STACK_KEY]=[]),t[s.STACK_KEY]):[]}static add(t){const n=s.getStack();n.includes(t)||n.push(t)}static remove(t){const n=s.getStack(),a=n.indexOf(t);return a!==-1&&(n.splice(a,1),!0)}static isActive(t){const n=s.getStack();return n.length>0&&n[n.length-1]===t}static getActive(){const t=s.getStack();return t.length>0?t[t.length-1]:void 0}static size(){return s.getStack().length}static isEmpty(){return s.size()===0}static clear(){const t=typeof document<"u"?document:null;t&&(t[s.STACK_KEY]=[])}static getAll(){return[...s.getStack()]}};i(s,"STACK_KEY","__focusTrapStack");let u=s;export{u as F,d as M,k as a,v as b,E as c,A as d,x as e,h as f,j as g,m as i,K as n};
