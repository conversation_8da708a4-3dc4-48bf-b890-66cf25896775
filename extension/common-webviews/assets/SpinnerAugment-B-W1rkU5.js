var Dc=Object.defineProperty;var Nc=(e,t,n)=>t in e?Dc(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n;var ye=(e,t,n)=>Nc(e,typeof t!="symbol"?t+"":t,n);(function(){const e=document.createElement("link").relList;if(!(e&&e.supports&&e.supports("modulepreload"))){for(const n of document.querySelectorAll('link[rel="modulepreload"]'))t(n);new MutationObserver(n=>{for(const r of n)if(r.type==="childList")for(const s of r.addedNodes)s.tagName==="LINK"&&s.rel==="modulepreload"&&t(s)}).observe(document,{childList:!0,subtree:!0})}function t(n){if(n.ep)return;n.ep=!0;const r=function(s){const o={};return s.integrity&&(o.integrity=s.integrity),s.referrerPolicy&&(o.referrerPolicy=s.referrerPolicy),s.crossOrigin==="use-credentials"?o.credentials="include":s.crossOrigin==="anonymous"?o.credentials="omit":o.credentials="same-origin",o}(n);fetch(n.href,r)}})();const O=typeof __SENTRY_DEBUG__>"u"||__SENTRY_DEBUG__,st="9.20.0",q=globalThis;function gt(){return nr(q),q}function nr(e){const t=e.__SENTRY__=e.__SENTRY__||{};return t.version=t.version||st,t[st]=t[st]||{}}function Un(e,t,n=q){const r=n.__SENTRY__=n.__SENTRY__||{},s=r[st]=r[st]||{};return s[e]||(s[e]=t())}const Pi=Object.prototype.toString;function gs(e){switch(Pi.call(e)){case"[object Error]":case"[object Exception]":case"[object DOMException]":case"[object WebAssembly.Exception]":return!0;default:return Le(e,Error)}}function Dt(e,t){return Pi.call(e)===`[object ${t}]`}function Ai(e){return Dt(e,"ErrorEvent")}function zs(e){return Dt(e,"DOMError")}function De(e){return Dt(e,"String")}function vs(e){return typeof e=="object"&&e!==null&&"__sentry_template_string__"in e&&"__sentry_template_values__"in e}function Xt(e){return e===null||vs(e)||typeof e!="object"&&typeof e!="function"}function Zt(e){return Dt(e,"Object")}function rr(e){return typeof Event<"u"&&Le(e,Event)}function sr(e){return!!(e!=null&&e.then&&typeof e.then=="function")}function Le(e,t){try{return e instanceof t}catch{return!1}}function Oi(e){return!(typeof e!="object"||e===null||!e.__isVue&&!e._isVue)}function Ci(e){return typeof Request<"u"&&Le(e,Request)}const _s=q,Lc=80;function dt(e,t={}){if(!e)return"<unknown>";try{let n=e;const r=5,s=[];let o=0,i=0;const c=" > ",a=c.length;let u;const l=Array.isArray(t)?t:t.keyAttrs,p=!Array.isArray(t)&&t.maxStringLength||Lc;for(;n&&o++<r&&(u=Mc(n,l),!(u==="html"||o>1&&i+s.length*a+u.length>=p));)s.push(u),i+=u.length,n=n.parentNode;return s.reverse().join(c)}catch{return"<unknown>"}}function Mc(e,t){const n=e,r=[];if(!(n!=null&&n.tagName))return"";if(_s.HTMLElement&&n instanceof HTMLElement&&n.dataset){if(n.dataset.sentryComponent)return n.dataset.sentryComponent;if(n.dataset.sentryElement)return n.dataset.sentryElement}r.push(n.tagName.toLowerCase());const s=t!=null&&t.length?t.filter(i=>n.getAttribute(i)).map(i=>[i,n.getAttribute(i)]):null;if(s!=null&&s.length)s.forEach(i=>{r.push(`[${i[0]}="${i[1]}"]`)});else{n.id&&r.push(`#${n.id}`);const i=n.className;if(i&&De(i)){const c=i.split(/\s+/);for(const a of c)r.push(`.${a}`)}}const o=["aria-label","type","name","title","alt"];for(const i of o){const c=n.getAttribute(i);c&&r.push(`[${i}="${c}"]`)}return r.join("")}function hn(){try{return _s.document.location.href}catch{return""}}function Ri(e){if(!_s.HTMLElement)return null;let t=e;for(let n=0;n<5;n++){if(!t)return null;if(t instanceof HTMLElement){if(t.dataset.sentryComponent)return t.dataset.sentryComponent;if(t.dataset.sentryElement)return t.dataset.sentryElement}t=t.parentNode}return null}const Fr=["debug","info","warn","error","log","assert","trace"],Hn={};function vt(e){if(!("console"in q))return e();const t=q.console,n={},r=Object.keys(Hn);r.forEach(s=>{const o=Hn[s];n[s]=t[s],t[s]=o});try{return e()}finally{r.forEach(s=>{t[s]=n[s]})}}const x=Un("logger",function(){let e=!1;const t={enable:()=>{e=!0},disable:()=>{e=!1},isEnabled:()=>e};return O?Fr.forEach(n=>{t[n]=(...r)=>{e&&vt(()=>{q.console[n](`Sentry Logger [${n}]:`,...r)})}}):Fr.forEach(n=>{t[n]=()=>{}}),t});function Bn(e,t=0){return typeof e!="string"||t===0||e.length<=t?e:`${e.slice(0,t)}...`}function Ws(e,t){if(!Array.isArray(e))return"";const n=[];for(let r=0;r<e.length;r++){const s=e[r];try{Oi(s)?n.push("[VueViewModel]"):n.push(String(s))}catch{n.push("[value cannot be serialized]")}}return n.join(t)}function jc(e,t,n=!1){return!!De(e)&&(Dt(t,"RegExp")?t.test(e):!!De(t)&&(n?e===t:e.includes(t)))}function rt(e,t=[],n=!1){return t.some(r=>jc(e,r,n))}function ue(e,t,n){if(!(t in e))return;const r=e[t];if(typeof r!="function")return;const s=n(r);typeof s=="function"&&Di(s,r);try{e[t]=s}catch{O&&x.log(`Failed to replace method "${t}" in object`,e)}}function de(e,t,n){try{Object.defineProperty(e,t,{value:n,writable:!0,configurable:!0})}catch{O&&x.log(`Failed to add non-enumerable property "${t}" to object`,e)}}function Di(e,t){try{const n=t.prototype||{};e.prototype=t.prototype=n,de(e,"__sentry_original__",t)}catch{}}function ys(e){return e.__sentry_original__}function Ni(e){if(gs(e))return{message:e.message,name:e.name,stack:e.stack,...Gs(e)};if(rr(e)){const t={type:e.type,target:Js(e.target),currentTarget:Js(e.currentTarget),...Gs(e)};return typeof CustomEvent<"u"&&Le(e,CustomEvent)&&(t.detail=e.detail),t}return e}function Js(e){try{return t=e,typeof Element<"u"&&Le(t,Element)?dt(e):Object.prototype.toString.call(e)}catch{return"<unknown>"}var t}function Gs(e){if(typeof e=="object"&&e!==null){const t={};for(const n in e)Object.prototype.hasOwnProperty.call(e,n)&&(t[n]=e[n]);return t}return{}}function ge(e=function(){const t=q;return t.crypto||t.msCrypto}()){let t=()=>16*Math.random();try{if(e!=null&&e.randomUUID)return e.randomUUID().replace(/-/g,"");e!=null&&e.getRandomValues&&(t=()=>{const n=new Uint8Array(1);return e.getRandomValues(n),n[0]})}catch{}return("10000000100040008000"+1e11).replace(/[018]/g,n=>(n^(15&t())>>n/4).toString(16))}function Li(e){var t,n;return(n=(t=e.exception)==null?void 0:t.values)==null?void 0:n[0]}function tt(e){const{message:t,event_id:n}=e;if(t)return t;const r=Li(e);return r?r.type&&r.value?`${r.type}: ${r.value}`:r.type||r.value||n||"<unknown>":n||"<unknown>"}function Ur(e,t,n){const r=e.exception=e.exception||{},s=r.values=r.values||[],o=s[0]=s[0]||{};o.value||(o.value=t||""),o.type||(o.type="Error")}function Tt(e,t){const n=Li(e);if(!n)return;const r=n.mechanism;if(n.mechanism={type:"generic",handled:!0,...r,...t},t&&"data"in t){const s={...r==null?void 0:r.data,...t.data};n.mechanism.data=s}}function Ks(e){if(function(t){try{return t.__sentry_captured__}catch{}}(e))return!0;try{de(e,"__sentry_captured__",!0)}catch{}return!1}const Mi=1e3;function mn(){return Date.now()/Mi}const ne=function(){const{performance:e}=q;if(!(e!=null&&e.now))return mn;const t=Date.now()-e.now(),n=e.timeOrigin==null?t:e.timeOrigin;return()=>(n+e.now())/Mi}();let vr;function me(){return vr||(vr=function(){var a;const{performance:e}=q;if(!(e!=null&&e.now))return[void 0,"none"];const t=36e5,n=e.now(),r=Date.now(),s=e.timeOrigin?Math.abs(e.timeOrigin+n-r):t,o=s<t,i=(a=e.timing)==null?void 0:a.navigationStart,c=typeof i=="number"?Math.abs(i+n-r):t;return o||c<t?s<=c?[e.timeOrigin,"timeOrigin"]:[i,"navigationStart"]:[r,"dateNow"]}()),vr[0]}function qc(e){const t=ne(),n={sid:ge(),init:!0,timestamp:t,started:t,duration:0,status:"ok",errors:0,ignoreDuration:!1,toJSON:()=>function(r){return{sid:`${r.sid}`,init:r.init,started:new Date(1e3*r.started).toISOString(),timestamp:new Date(1e3*r.timestamp).toISOString(),status:r.status,errors:r.errors,did:typeof r.did=="number"||typeof r.did=="string"?`${r.did}`:void 0,duration:r.duration,abnormal_mechanism:r.abnormal_mechanism,attrs:{release:r.release,environment:r.environment,ip_address:r.ipAddress,user_agent:r.userAgent}}}(n)};return e&&kt(n,e),n}function kt(e,t={}){if(t.user&&(!e.ipAddress&&t.user.ip_address&&(e.ipAddress=t.user.ip_address),e.did||t.did||(e.did=t.user.id||t.user.email||t.user.username)),e.timestamp=t.timestamp||ne(),t.abnormal_mechanism&&(e.abnormal_mechanism=t.abnormal_mechanism),t.ignoreDuration&&(e.ignoreDuration=t.ignoreDuration),t.sid&&(e.sid=t.sid.length===32?t.sid:ge()),t.init!==void 0&&(e.init=t.init),!e.did&&t.did&&(e.did=`${t.did}`),typeof t.started=="number"&&(e.started=t.started),e.ignoreDuration)e.duration=void 0;else if(typeof t.duration=="number")e.duration=t.duration;else{const n=e.timestamp-e.started;e.duration=n>=0?n:0}t.release&&(e.release=t.release),t.environment&&(e.environment=t.environment),!e.ipAddress&&t.ipAddress&&(e.ipAddress=t.ipAddress),!e.userAgent&&t.userAgent&&(e.userAgent=t.userAgent),typeof t.errors=="number"&&(e.errors=t.errors),t.status&&(e.status=t.status)}function gn(e,t,n=2){if(!t||typeof t!="object"||n<=0)return t;if(e&&Object.keys(t).length===0)return e;const r={...e};for(const s in t)Object.prototype.hasOwnProperty.call(t,s)&&(r[s]=gn(r[s],t[s],n-1));return r}const Hr="_sentrySpan";function Qt(e,t){t?de(e,Hr,t):delete e[Hr]}function zn(e){return e[Hr]}function ze(){return ge()}function vn(){return ge().substring(16)}class Me{constructor(){this._notifyingListeners=!1,this._scopeListeners=[],this._eventProcessors=[],this._breadcrumbs=[],this._attachments=[],this._user={},this._tags={},this._extra={},this._contexts={},this._sdkProcessingMetadata={},this._propagationContext={traceId:ze(),sampleRand:Math.random()}}clone(){const t=new Me;return t._breadcrumbs=[...this._breadcrumbs],t._tags={...this._tags},t._extra={...this._extra},t._contexts={...this._contexts},this._contexts.flags&&(t._contexts.flags={values:[...this._contexts.flags.values]}),t._user=this._user,t._level=this._level,t._session=this._session,t._transactionName=this._transactionName,t._fingerprint=this._fingerprint,t._eventProcessors=[...this._eventProcessors],t._attachments=[...this._attachments],t._sdkProcessingMetadata={...this._sdkProcessingMetadata},t._propagationContext={...this._propagationContext},t._client=this._client,t._lastEventId=this._lastEventId,Qt(t,zn(this)),t}setClient(t){this._client=t}setLastEventId(t){this._lastEventId=t}getClient(){return this._client}lastEventId(){return this._lastEventId}addScopeListener(t){this._scopeListeners.push(t)}addEventProcessor(t){return this._eventProcessors.push(t),this}setUser(t){return this._user=t||{email:void 0,id:void 0,ip_address:void 0,username:void 0},this._session&&kt(this._session,{user:t}),this._notifyScopeListeners(),this}getUser(){return this._user}setTags(t){return this._tags={...this._tags,...t},this._notifyScopeListeners(),this}setTag(t,n){return this._tags={...this._tags,[t]:n},this._notifyScopeListeners(),this}setExtras(t){return this._extra={...this._extra,...t},this._notifyScopeListeners(),this}setExtra(t,n){return this._extra={...this._extra,[t]:n},this._notifyScopeListeners(),this}setFingerprint(t){return this._fingerprint=t,this._notifyScopeListeners(),this}setLevel(t){return this._level=t,this._notifyScopeListeners(),this}setTransactionName(t){return this._transactionName=t,this._notifyScopeListeners(),this}setContext(t,n){return n===null?delete this._contexts[t]:this._contexts[t]=n,this._notifyScopeListeners(),this}setSession(t){return t?this._session=t:delete this._session,this._notifyScopeListeners(),this}getSession(){return this._session}update(t){if(!t)return this;const n=typeof t=="function"?t(this):t,r=n instanceof Me?n.getScopeData():Zt(n)?t:void 0,{tags:s,extra:o,user:i,contexts:c,level:a,fingerprint:u=[],propagationContext:l}=r||{};return this._tags={...this._tags,...s},this._extra={...this._extra,...o},this._contexts={...this._contexts,...c},i&&Object.keys(i).length&&(this._user=i),a&&(this._level=a),u.length&&(this._fingerprint=u),l&&(this._propagationContext=l),this}clear(){return this._breadcrumbs=[],this._tags={},this._extra={},this._user={},this._contexts={},this._level=void 0,this._transactionName=void 0,this._fingerprint=void 0,this._session=void 0,Qt(this,void 0),this._attachments=[],this.setPropagationContext({traceId:ze(),sampleRand:Math.random()}),this._notifyScopeListeners(),this}addBreadcrumb(t,n){var o;const r=typeof n=="number"?n:100;if(r<=0)return this;const s={timestamp:mn(),...t,message:t.message?Bn(t.message,2048):t.message};return this._breadcrumbs.push(s),this._breadcrumbs.length>r&&(this._breadcrumbs=this._breadcrumbs.slice(-r),(o=this._client)==null||o.recordDroppedEvent("buffer_overflow","log_item")),this._notifyScopeListeners(),this}getLastBreadcrumb(){return this._breadcrumbs[this._breadcrumbs.length-1]}clearBreadcrumbs(){return this._breadcrumbs=[],this._notifyScopeListeners(),this}addAttachment(t){return this._attachments.push(t),this}clearAttachments(){return this._attachments=[],this}getScopeData(){return{breadcrumbs:this._breadcrumbs,attachments:this._attachments,contexts:this._contexts,tags:this._tags,extra:this._extra,user:this._user,level:this._level,fingerprint:this._fingerprint||[],eventProcessors:this._eventProcessors,propagationContext:this._propagationContext,sdkProcessingMetadata:this._sdkProcessingMetadata,transactionName:this._transactionName,span:zn(this)}}setSDKProcessingMetadata(t){return this._sdkProcessingMetadata=gn(this._sdkProcessingMetadata,t,2),this}setPropagationContext(t){return this._propagationContext=t,this}getPropagationContext(){return this._propagationContext}captureException(t,n){const r=(n==null?void 0:n.event_id)||ge();if(!this._client)return x.warn("No client configured on scope - will not capture exception!"),r;const s=new Error("Sentry syntheticException");return this._client.captureException(t,{originalException:t,syntheticException:s,...n,event_id:r},this),r}captureMessage(t,n,r){const s=(r==null?void 0:r.event_id)||ge();if(!this._client)return x.warn("No client configured on scope - will not capture message!"),s;const o=new Error(t);return this._client.captureMessage(t,n,{originalException:t,syntheticException:o,...r,event_id:s},this),s}captureEvent(t,n){const r=(n==null?void 0:n.event_id)||ge();return this._client?(this._client.captureEvent(t,{...n,event_id:r},this),r):(x.warn("No client configured on scope - will not capture event!"),r)}_notifyScopeListeners(){this._notifyingListeners||(this._notifyingListeners=!0,this._scopeListeners.forEach(t=>{t(this)}),this._notifyingListeners=!1)}}class Fc{constructor(t,n){let r,s;r=t||new Me,s=n||new Me,this._stack=[{scope:r}],this._isolationScope=s}withScope(t){const n=this._pushScope();let r;try{r=t(n)}catch(s){throw this._popScope(),s}return sr(r)?r.then(s=>(this._popScope(),s),s=>{throw this._popScope(),s}):(this._popScope(),r)}getClient(){return this.getStackTop().client}getScope(){return this.getStackTop().scope}getIsolationScope(){return this._isolationScope}getStackTop(){return this._stack[this._stack.length-1]}_pushScope(){const t=this.getScope().clone();return this._stack.push({client:this.getClient(),scope:t}),t}_popScope(){return!(this._stack.length<=1)&&!!this._stack.pop()}}function It(){const e=nr(gt());return e.stack=e.stack||new Fc(Un("defaultCurrentScope",()=>new Me),Un("defaultIsolationScope",()=>new Me))}function Uc(e){return It().withScope(e)}function Hc(e,t){const n=It();return n.withScope(()=>(n.getStackTop().scope=e,t(e)))}function Vs(e){return It().withScope(()=>e(It().getIsolationScope()))}function Nt(e){const t=nr(e);return t.acs?t.acs:{withIsolationScope:Vs,withScope:Uc,withSetScope:Hc,withSetIsolationScope:(n,r)=>Vs(r),getCurrentScope:()=>It().getScope(),getIsolationScope:()=>It().getIsolationScope()}}function W(){return Nt(gt()).getCurrentScope()}function Xe(){return Nt(gt()).getIsolationScope()}function en(...e){const t=Nt(gt());if(e.length===2){const[n,r]=e;return n?t.withSetScope(n,r):t.withScope(r)}return t.withScope(e[0])}function H(){return W().getClient()}function Bc(e){const t=e.getPropagationContext(),{traceId:n,parentSpanId:r,propagationSpanId:s}=t,o={trace_id:n,span_id:s||vn()};return r&&(o.parent_span_id=r),o}const Ce="sentry.source",bs="sentry.sample_rate",ji="sentry.previous_trace_sample_rate",ft="sentry.op",X="sentry.origin",Wn="sentry.idle_span_finish_reason",or="sentry.measurement_unit",ir="sentry.measurement_value",Ys="sentry.custom_span_name",Br="sentry.profile_id",tn="sentry.exclusive_time",zc="sentry.link.type",Wc=0,qi=1,Y=2;function Fi(e,t){e.setAttribute("http.response.status_code",t);const n=function(r){if(r<400&&r>=100)return{code:qi};if(r>=400&&r<500)switch(r){case 401:return{code:Y,message:"unauthenticated"};case 403:return{code:Y,message:"permission_denied"};case 404:return{code:Y,message:"not_found"};case 409:return{code:Y,message:"already_exists"};case 413:return{code:Y,message:"failed_precondition"};case 429:return{code:Y,message:"resource_exhausted"};case 499:return{code:Y,message:"cancelled"};default:return{code:Y,message:"invalid_argument"}}if(r>=500&&r<600)switch(r){case 501:return{code:Y,message:"unimplemented"};case 503:return{code:Y,message:"unavailable"};case 504:return{code:Y,message:"deadline_exceeded"};default:return{code:Y,message:"internal_error"}}return{code:Y,message:"unknown_error"}}(t);n.message!=="unknown_error"&&e.setStatus(n)}const Ui="_sentryScope",Hi="_sentryIsolationScope";function Jn(e){return{scope:e[Ui],isolationScope:e[Hi]}}function nn(e){if(typeof e=="boolean")return Number(e);const t=typeof e=="string"?parseFloat(e):e;return typeof t!="number"||isNaN(t)||t<0||t>1?void 0:t}const Ss="sentry-",Jc=/^sentry-/,Gc=8192;function Bi(e){const t=function(r){if(!(!r||!De(r)&&!Array.isArray(r)))return Array.isArray(r)?r.reduce((s,o)=>{const i=Xs(o);return Object.entries(i).forEach(([c,a])=>{s[c]=a}),s},{}):Xs(r)}(e);if(!t)return;const n=Object.entries(t).reduce((r,[s,o])=>(s.match(Jc)&&(r[s.slice(Ss.length)]=o),r),{});return Object.keys(n).length>0?n:void 0}function Kc(e){if(e)return function(t){if(Object.keys(t).length!==0)return Object.entries(t).reduce((n,[r,s],o)=>{const i=`${encodeURIComponent(r)}=${encodeURIComponent(s)}`,c=o===0?i:`${n},${i}`;return c.length>Gc?(O&&x.warn(`Not adding key: ${r} with val: ${s} to baggage header due to exceeding baggage size limits.`),n):c},"")}(Object.entries(e).reduce((t,[n,r])=>(r&&(t[`${Ss}${n}`]=r),t),{}))}function Xs(e){return e.split(",").map(t=>t.split("=").map(n=>{try{return decodeURIComponent(n.trim())}catch{return}})).reduce((t,[n,r])=>(n&&r&&(t[n]=r),t),{})}const zi=new RegExp("^[ \\t]*([0-9a-f]{32})?-?([0-9a-f]{16})?-?([01])?[ \\t]*$");function Vc(e,t){const n=function(a){if(!a)return;const u=a.match(zi);if(!u)return;let l;return u[3]==="1"?l=!0:u[3]==="0"&&(l=!1),{traceId:u[1],parentSampled:l,parentSpanId:u[2]}}(e),r=Bi(t);if(!(n!=null&&n.traceId))return{traceId:ze(),sampleRand:Math.random()};const s=function(a,u){const l=nn(u==null?void 0:u.sample_rand);if(l!==void 0)return l;const p=nn(u==null?void 0:u.sample_rate);return p&&(a==null?void 0:a.parentSampled)!==void 0?a.parentSampled?Math.random()*p:p+Math.random()*(1-p):Math.random()}(n,r);r&&(r.sample_rand=s.toString());const{traceId:o,parentSpanId:i,parentSampled:c}=n;return{traceId:o,parentSpanId:i,sampled:c,dsc:r||{},sampleRand:s}}function Zs(e=ze(),t=vn(),n){let r="";return n!==void 0&&(r=n?"-1":"-0"),`${e}-${t}${r}`}const ws=1;let Qs=!1;function Yc(e){const{spanId:t,traceId:n}=e.spanContext(),{data:r,op:s,parent_span_id:o,status:i,origin:c,links:a}=B(e);return{parent_span_id:o,span_id:t,trace_id:n,data:r,op:s,status:i,origin:c,links:a}}function Xc(e){const{spanId:t,traceId:n,isRemote:r}=e.spanContext(),s=r?t:B(e).parent_span_id,o=Jn(e).scope;return{parent_span_id:s,span_id:r?(o==null?void 0:o.getPropagationContext().propagationSpanId)||vn():t,trace_id:n}}function Wi(e){return e&&e.length>0?e.map(({context:{spanId:t,traceId:n,traceFlags:r,...s},attributes:o})=>({span_id:t,trace_id:n,sampled:r===ws,attributes:o,...s})):void 0}function ot(e){return typeof e=="number"?eo(e):Array.isArray(e)?e[0]+e[1]/1e9:e instanceof Date?eo(e.getTime()):ne()}function eo(e){return e>9999999999?e/1e3:e}function B(e){var r;if(function(s){return typeof s.getSpanJSON=="function"}(e))return e.getSpanJSON();const{spanId:t,traceId:n}=e.spanContext();if(function(s){const o=s;return!!(o.attributes&&o.startTime&&o.name&&o.endTime&&o.status)}(e)){const{attributes:s,startTime:o,name:i,endTime:c,status:a,links:u}=e;return{span_id:t,trace_id:n,data:s,description:i,parent_span_id:"parentSpanId"in e?e.parentSpanId:"parentSpanContext"in e?(r=e.parentSpanContext)==null?void 0:r.spanId:void 0,start_timestamp:ot(o),timestamp:ot(c)||void 0,status:Ji(a),op:s[ft],origin:s[X],links:Wi(u)}}return{span_id:t,trace_id:n,start_timestamp:0,data:{}}}function it(e){const{traceFlags:t}=e.spanContext();return t===ws}function Ji(e){if(e&&e.code!==Wc)return e.code===qi?"ok":e.message||"unknown_error"}const at="_sentryChildSpans",zr="_sentryRootSpan";function to(e,t){const n=e[zr]||e;de(t,zr,n),e[at]?e[at].add(t):de(e,at,new Set([t]))}function Nn(e){const t=new Set;return function n(r){if(!t.has(r)&&it(r)){t.add(r);const s=r[at]?Array.from(r[at]):[];for(const o of s)n(o)}}(e),Array.from(t)}function te(e){return e[zr]||e}function ce(){const e=Nt(gt());return e.getActiveSpan?e.getActiveSpan():zn(W())}function Wr(){Qs||(vt(()=>{console.warn("[Sentry] Returning null from `beforeSendSpan` is disallowed. To drop certain spans, configure the respective integrations directly.")}),Qs=!0)}const no=50,ct="?",ro=/\(error: (.*)\)/,so=/captureMessage|captureException/;function Gi(...e){const t=e.sort((n,r)=>n[0]-r[0]).map(n=>n[1]);return(n,r=0,s=0)=>{const o=[],i=n.split(`
`);for(let c=r;c<i.length;c++){const a=i[c];if(a.length>1024)continue;const u=ro.test(a)?a.replace(ro,"$1"):a;if(!u.match(/\S*Error: /)){for(const l of t){const p=l(u);if(p){o.push(p);break}}if(o.length>=no+s)break}}return function(c){if(!c.length)return[];const a=Array.from(c);return/sentryWrapped/.test(Tn(a).function||"")&&a.pop(),a.reverse(),so.test(Tn(a).function||"")&&(a.pop(),so.test(Tn(a).function||"")&&a.pop()),a.slice(0,no).map(u=>({...u,filename:u.filename||Tn(a).filename,function:u.function||ct}))}(o.slice(s))}}function Tn(e){return e[e.length-1]||{}}const oo="<anonymous>";function je(e){try{return e&&typeof e=="function"&&e.name||oo}catch{return oo}}function io(e){const t=e.exception;if(t){const n=[];try{return t.values.forEach(r=>{r.stacktrace.frames&&n.push(...r.stacktrace.frames)}),n}catch{return}}}const Ln={},ao={};function We(e,t){Ln[e]=Ln[e]||[],Ln[e].push(t)}function Je(e,t){if(!ao[e]){ao[e]=!0;try{t()}catch(n){O&&x.error(`Error while instrumenting ${e}`,n)}}}function ve(e,t){const n=e&&Ln[e];if(n)for(const r of n)try{r(t)}catch(s){O&&x.error(`Error while triggering instrumentation handler.
Type: ${e}
Name: ${je(r)}
Error:`,s)}}let _r=null;function Ki(e){const t="error";We(t,e),Je(t,Zc)}function Zc(){_r=q.onerror,q.onerror=function(e,t,n,r,s){return ve("error",{column:r,error:s,line:n,msg:e,url:t}),!!_r&&_r.apply(this,arguments)},q.onerror.__SENTRY_INSTRUMENTED__=!0}let yr=null;function Vi(e){const t="unhandledrejection";We(t,e),Je(t,Qc)}function Qc(){yr=q.onunhandledrejection,q.onunhandledrejection=function(e){return ve("unhandledrejection",e),!yr||yr.apply(this,arguments)},q.onunhandledrejection.__SENTRY_INSTRUMENTED__=!0}let co=!1;function Jr(){const e=ce(),t=e&&te(e);if(t){const n="internal_error";O&&x.log(`[Tracing] Root span: ${n} -> Global error occurred`),t.setStatus({code:Y,message:n})}}function Ge(e){var n;if(typeof __SENTRY_TRACING__=="boolean"&&!__SENTRY_TRACING__)return!1;const t=e||((n=H())==null?void 0:n.getOptions());return!(!t||t.tracesSampleRate==null&&!t.tracesSampler)}Jr.tag="sentry_tracingErrorCallback";const Es="production",eu=/^o(\d+)\./,tu=/^(?:(\w+):)\/\/(?:(\w+)(?::(\w+)?)?@)([\w.-]+)(?::(\d+))?\/(.+)/;function rn(e,t=!1){const{host:n,path:r,pass:s,port:o,projectId:i,protocol:c,publicKey:a}=e;return`${c}://${a}${t&&s?`:${s}`:""}@${n}${o?`:${o}`:""}/${r&&`${r}/`}${i}`}function uo(e){return{protocol:e.protocol,publicKey:e.publicKey||"",pass:e.pass||"",host:e.host,port:e.port||"",path:e.path||"",projectId:e.projectId}}function nu(e){const t=typeof e=="string"?function(n){const r=tu.exec(n);if(!r)return void vt(()=>{console.error(`Invalid Sentry Dsn: ${n}`)});const[s,o,i="",c="",a="",u=""]=r.slice(1);let l="",p=u;const f=p.split("/");if(f.length>1&&(l=f.slice(0,-1).join("/"),p=f.pop()),p){const d=p.match(/^\d+/);d&&(p=d[0])}return uo({host:c,pass:i,path:l,projectId:p,port:a,protocol:s,publicKey:o})}(e):uo(e);if(t&&function(n){if(!O)return!0;const{port:r,projectId:s,protocol:o}=n;return!(["protocol","publicKey","host","projectId"].find(i=>!n[i]&&(x.error(`Invalid Sentry Dsn: ${i} missing`),!0))||(s.match(/^\d+$/)?function(i){return i==="http"||i==="https"}(o)?r&&isNaN(parseInt(r,10))&&(x.error(`Invalid Sentry Dsn: Invalid port ${r}`),1):(x.error(`Invalid Sentry Dsn: Invalid protocol ${o}`),1):(x.error(`Invalid Sentry Dsn: Invalid projectId ${s}`),1)))}(t))return t}const Yi="_frozenDsc";function Mn(e,t){de(e,Yi,t)}function Xi(e,t){const n=t.getOptions(),{publicKey:r,host:s}=t.getDsn()||{};let o;n.orgId?o=String(n.orgId):s&&(o=function(c){const a=c.match(eu);return a==null?void 0:a[1]}(s));const i={environment:n.environment||Es,release:n.release,public_key:r,trace_id:e,org_id:o};return t.emit("createDsc",i),i}function Zi(e,t){const n=t.getPropagationContext();return n.dsc||Xi(n.traceId,e)}function Ke(e){var m;const t=H();if(!t)return{};const n=te(e),r=B(n),s=r.data,o=n.spanContext().traceState,i=(o==null?void 0:o.get("sentry.sample_rate"))??s[bs]??s[ji];function c(h){return typeof i!="number"&&typeof i!="string"||(h.sample_rate=`${i}`),h}const a=n[Yi];if(a)return c(a);const u=o==null?void 0:o.get("sentry.dsc"),l=u&&Bi(u);if(l)return c(l);const p=Xi(e.spanContext().traceId,t),f=s[Ce],d=r.description;return f!=="url"&&d&&(p.transaction=d),Ge()&&(p.sampled=String(it(n)),p.sample_rand=(o==null?void 0:o.get("sentry.sample_rand"))??((m=Jn(n).scope)==null?void 0:m.getPropagationContext().sampleRand.toString())),c(p),t.emit("createDsc",p,n),p}class ut{constructor(t={}){this._traceId=t.traceId||ze(),this._spanId=t.spanId||vn()}spanContext(){return{spanId:this._spanId,traceId:this._traceId,traceFlags:0}}end(t){}setAttribute(t,n){return this}setAttributes(t){return this}setStatus(t){return this}updateName(t){return this}isRecording(){return!1}addEvent(t,n,r){return this}addLink(t){return this}addLinks(t){return this}recordException(t,n){}}function Pe(e,t=100,n=1/0){try{return Gr("",e,t,n)}catch(r){return{ERROR:`**non-serializable** (${r})`}}}function Qi(e,t=3,n=102400){const r=Pe(e,t);return s=r,function(o){return~-encodeURI(o).split(/%..|./).length}(JSON.stringify(s))>n?Qi(e,t-1,n):r;var s}function Gr(e,t,n=1/0,r=1/0,s=function(){const o=new WeakSet;function i(a){return!!o.has(a)||(o.add(a),!1)}function c(a){o.delete(a)}return[i,c]}()){const[o,i]=s;if(t==null||["boolean","string"].includes(typeof t)||typeof t=="number"&&Number.isFinite(t))return t;const c=function(d,m){try{if(d==="domain"&&m&&typeof m=="object"&&m._events)return"[Domain]";if(d==="domainEmitter")return"[DomainEmitter]";if(typeof global<"u"&&m===global)return"[Global]";if(typeof window<"u"&&m===window)return"[Window]";if(typeof document<"u"&&m===document)return"[Document]";if(Oi(m))return"[VueViewModel]";if(Zt(h=m)&&"nativeEvent"in h&&"preventDefault"in h&&"stopPropagation"in h)return"[SyntheticEvent]";if(typeof m=="number"&&!Number.isFinite(m))return`[${m}]`;if(typeof m=="function")return`[Function: ${je(m)}]`;if(typeof m=="symbol")return`[${String(m)}]`;if(typeof m=="bigint")return`[BigInt: ${String(m)}]`;const g=function(v){const _=Object.getPrototypeOf(v);return _!=null&&_.constructor?_.constructor.name:"null prototype"}(m);return/^HTML(\w*)Element$/.test(g)?`[HTMLElement: ${g}]`:`[object ${g}]`}catch(g){return`**non-serializable** (${g})`}var h}(e,t);if(!c.startsWith("[object "))return c;if(t.__sentry_skip_normalization__)return t;const a=typeof t.__sentry_override_normalization_depth__=="number"?t.__sentry_override_normalization_depth__:n;if(a===0)return c.replace("object ","");if(o(t))return"[Circular ~]";const u=t;if(u&&typeof u.toJSON=="function")try{return Gr("",u.toJSON(),a-1,r,s)}catch{}const l=Array.isArray(t)?[]:{};let p=0;const f=Ni(t);for(const d in f){if(!Object.prototype.hasOwnProperty.call(f,d))continue;if(p>=r){l[d]="[MaxProperties ~]";break}const m=f[d];l[d]=Gr(d,m,a-1,r,s),p++}return i(t),l}function $t(e,t=[]){return[e,t]}function ru(e,t){const[n,r]=e;return[n,[...r,t]]}function lo(e,t){const n=e[1];for(const r of n)if(t(r,r[0].type))return!0;return!1}function Kr(e){const t=nr(q);return t.encodePolyfill?t.encodePolyfill(e):new TextEncoder().encode(e)}function su(e){const[t,n]=e;let r=JSON.stringify(t);function s(o){typeof r=="string"?r=typeof o=="string"?r+o:[Kr(r),o]:r.push(typeof o=="string"?Kr(o):o)}for(const o of n){const[i,c]=o;if(s(`
${JSON.stringify(i)}
`),typeof c=="string"||c instanceof Uint8Array)s(c);else{let a;try{a=JSON.stringify(c)}catch{a=JSON.stringify(Pe(c))}s(a)}}return typeof r=="string"?r:function(o){const i=o.reduce((u,l)=>u+l.length,0),c=new Uint8Array(i);let a=0;for(const u of o)c.set(u,a),a+=u.length;return c}(r)}function ou(e){return[{type:"span"},e]}function iu(e){const t=typeof e.data=="string"?Kr(e.data):e.data;return[{type:"attachment",length:t.length,filename:e.filename,content_type:e.contentType,attachment_type:e.attachmentType},t]}const au={session:"session",sessions:"session",attachment:"attachment",transaction:"transaction",event:"error",client_report:"internal",user_report:"default",profile:"profile",profile_chunk:"profile",replay_event:"replay",replay_recording:"replay",check_in:"monitor",feedback:"feedback",span:"span",raw_security:"security",log:"log_item"};function fo(e){return au[e]}function ea(e){if(!(e!=null&&e.sdk))return;const{name:t,version:n}=e.sdk;return{name:t,version:n}}function cu(e,t,n,r){const s=ea(n),o=e.type&&e.type!=="replay_event"?e.type:"event";(function(c,a){a&&(c.sdk=c.sdk||{},c.sdk.name=c.sdk.name||a.name,c.sdk.version=c.sdk.version||a.version,c.sdk.integrations=[...c.sdk.integrations||[],...a.integrations||[]],c.sdk.packages=[...c.sdk.packages||[],...a.packages||[]])})(e,n==null?void 0:n.sdk);const i=function(c,a,u,l){var f;const p=(f=c.sdkProcessingMetadata)==null?void 0:f.dynamicSamplingContext;return{event_id:c.event_id,sent_at:new Date().toISOString(),...a&&{sdk:a},...!!u&&l&&{dsn:rn(l)},...p&&{trace:p}}}(e,s,r,t);return delete e.sdkProcessingMetadata,$t(i,[[{type:o},e]])}function po(e){if(!e||e.length===0)return;const t={};return e.forEach(n=>{const r=n.attributes||{},s=r[or],o=r[ir];typeof s=="string"&&typeof o=="number"&&(t[n.name]={value:o,unit:s})}),t}class ar{constructor(t={}){this._traceId=t.traceId||ze(),this._spanId=t.spanId||vn(),this._startTime=t.startTimestamp||ne(),this._links=t.links,this._attributes={},this.setAttributes({[X]:"manual",[ft]:t.op,...t.attributes}),this._name=t.name,t.parentSpanId&&(this._parentSpanId=t.parentSpanId),"sampled"in t&&(this._sampled=t.sampled),t.endTimestamp&&(this._endTime=t.endTimestamp),this._events=[],this._isStandaloneSpan=t.isStandalone,this._endTime&&this._onSpanEnded()}addLink(t){return this._links?this._links.push(t):this._links=[t],this}addLinks(t){return this._links?this._links.push(...t):this._links=t,this}recordException(t,n){}spanContext(){const{_spanId:t,_traceId:n,_sampled:r}=this;return{spanId:t,traceId:n,traceFlags:r?ws:0}}setAttribute(t,n){return n===void 0?delete this._attributes[t]:this._attributes[t]=n,this}setAttributes(t){return Object.keys(t).forEach(n=>this.setAttribute(n,t[n])),this}updateStartTime(t){this._startTime=ot(t)}setStatus(t){return this._status=t,this}updateName(t){return this._name=t,this.setAttribute(Ce,"custom"),this}end(t){this._endTime||(this._endTime=ot(t),function(n){if(!O)return;const{description:r="< unknown name >",op:s="< unknown op >"}=B(n),{spanId:o}=n.spanContext(),i=`[Tracing] Finishing "${s}" ${te(n)===n?"root ":""}span "${r}" with ID ${o}`;x.log(i)}(this),this._onSpanEnded())}getSpanJSON(){return{data:this._attributes,description:this._name,op:this._attributes[ft],parent_span_id:this._parentSpanId,span_id:this._spanId,start_timestamp:this._startTime,status:Ji(this._status),timestamp:this._endTime,trace_id:this._traceId,origin:this._attributes[X],profile_id:this._attributes[Br],exclusive_time:this._attributes[tn],measurements:po(this._events),is_segment:this._isStandaloneSpan&&te(this)===this||void 0,segment_id:this._isStandaloneSpan?te(this).spanContext().spanId:void 0,links:Wi(this._links)}}isRecording(){return!this._endTime&&!!this._sampled}addEvent(t,n,r){O&&x.log("[Tracing] Adding an event to span:",t);const s=ho(n)?n:r||ne(),o=ho(n)?{}:n||{},i={name:t,time:ot(s),attributes:o};return this._events.push(i),this}isStandaloneSpan(){return!!this._isStandaloneSpan}_onSpanEnded(){const t=H();if(t&&t.emit("spanEnd",this),!(this._isStandaloneSpan||this===te(this)))return;if(this._isStandaloneSpan)return void(this._sampled?function(r){const s=H();if(!s)return;const o=r[1];if(!o||o.length===0)return void s.recordDroppedEvent("before_send","span");s.sendEnvelope(r)}(function(r,s){const o=Ke(r[0]),i=s==null?void 0:s.getDsn(),c=s==null?void 0:s.getOptions().tunnel,a={sent_at:new Date().toISOString(),...function(f){return!!f.trace_id&&!!f.public_key}(o)&&{trace:o},...!!c&&i&&{dsn:rn(i)}},u=s==null?void 0:s.getOptions().beforeSendSpan,l=u?f=>{const d=B(f);return u(d)||(Wr(),d)}:B,p=[];for(const f of r){const d=l(f);d&&p.push(ou(d))}return $t(a,p)}([this],t)):(O&&x.log("[Tracing] Discarding standalone span because its trace was not chosen to be sampled."),t&&t.recordDroppedEvent("sample_rate","span")));const n=this._convertSpanToTransaction();n&&(Jn(this).scope||W()).captureEvent(n)}_convertSpanToTransaction(){var a;if(!mo(B(this)))return;this._name||(O&&x.warn("Transaction has no name, falling back to `<unlabeled transaction>`."),this._name="<unlabeled transaction>");const{scope:t,isolationScope:n}=Jn(this),r=(a=t==null?void 0:t.getScopeData().sdkProcessingMetadata)==null?void 0:a.normalizedRequest;if(this._sampled!==!0)return;const s=Nn(this).filter(u=>u!==this&&!function(l){return l instanceof ar&&l.isStandaloneSpan()}(u)).map(u=>B(u)).filter(mo),o=this._attributes[Ce];delete this._attributes[Ys],s.forEach(u=>{delete u.data[Ys]});const i={contexts:{trace:Yc(this)},spans:s.length>1e3?s.sort((u,l)=>u.start_timestamp-l.start_timestamp).slice(0,1e3):s,start_timestamp:this._startTime,timestamp:this._endTime,transaction:this._name,type:"transaction",sdkProcessingMetadata:{capturedSpanScope:t,capturedSpanIsolationScope:n,dynamicSamplingContext:Ke(this)},request:r,...o&&{transaction_info:{source:o}}},c=po(this._events);return c&&Object.keys(c).length&&(O&&x.log("[Measurements] Adding measurements to transaction event",JSON.stringify(c,void 0,2)),i.measurements=c),i}}function ho(e){return e&&typeof e=="number"||e instanceof Date||Array.isArray(e)}function mo(e){return!!(e.start_timestamp&&e.timestamp&&e.span_id&&e.trace_id)}const ta="__SENTRY_SUPPRESS_TRACING__";function _n(e){const t=ra();if(t.startInactiveSpan)return t.startInactiveSpan(e);const n=function(o){const i=o.experimental||{},c={isStandalone:i.standalone,...o};if(o.startTime){const a={...c};return a.startTimestamp=ot(o.startTime),delete a.startTime,a}return c}(e),{forceTransaction:r,parentSpan:s}=e;return(e.scope?o=>en(e.scope,o):s!==void 0?o=>na(s,o):o=>o())(()=>{const o=W(),i=function(c){const a=zn(c);if(!a)return;const u=H();return(u?u.getOptions():{}).parentSpanIsAlwaysRootSpan?te(a):a}(o);return e.onlyIfParent&&!i?new ut:function({parentSpan:c,spanArguments:a,forceTransaction:u,scope:l}){if(!Ge()){const d=new ut;return(u||!c)&&Mn(d,{sampled:"false",sample_rate:"0",transaction:a.name,...Ke(d)}),d}const p=Xe();let f;if(c&&!u)f=function(d,m,h){const{spanId:g,traceId:v}=d.spanContext(),_=!m.getScopeData().sdkProcessingMetadata[ta]&&it(d),S=_?new ar({...h,parentSpanId:g,traceId:v,sampled:_}):new ut({traceId:v});to(d,S);const y=H();return y&&(y.emit("spanStart",S),h.endTimestamp&&y.emit("spanEnd",S)),S}(c,l,a),to(c,f);else if(c){const d=Ke(c),{traceId:m,spanId:h}=c.spanContext(),g=it(c);f=go({traceId:m,parentSpanId:h,...a},l,g),Mn(f,d)}else{const{traceId:d,dsc:m,parentSpanId:h,sampled:g}={...p.getPropagationContext(),...l.getPropagationContext()};f=go({traceId:d,parentSpanId:h,...a},l,g),m&&Mn(f,m)}return function(d){if(!O)return;const{description:m="< unknown name >",op:h="< unknown op >",parent_span_id:g}=B(d),{spanId:v}=d.spanContext(),_=it(d),S=te(d),y=S===d,b=`[Tracing] Starting ${_?"sampled":"unsampled"} ${y?"root ":""}span`,T=[`op: ${h}`,`name: ${m}`,`ID: ${v}`];if(g&&T.push(`parent ID: ${g}`),!y){const{op:w,description:L}=B(S);T.push(`root ID: ${S.spanContext().spanId}`),w&&T.push(`root op: ${w}`),L&&T.push(`root description: ${L}`)}x.log(`${b}
  ${T.join(`
  `)}`)}(f),function(d,m,h){d&&(de(d,Hi,h),de(d,Ui,m))}(f,l,p),f}({parentSpan:i,spanArguments:n,forceTransaction:r,scope:o})})}function na(e,t){const n=ra();return n.withActiveSpan?n.withActiveSpan(e,t):en(r=>(Qt(r,e||void 0),t(r)))}function ra(){return Nt(gt())}function go(e,t,n){var m;const r=H(),s=(r==null?void 0:r.getOptions())||{},{name:o=""}=e,i={spanAttributes:{...e.attributes},spanName:o,parentSampled:n};r==null||r.emit("beforeSampling",i,{decision:!1});const c=i.parentSampled??n,a=i.spanAttributes,u=t.getPropagationContext(),[l,p,f]=t.getScopeData().sdkProcessingMetadata[ta]?[!1]:function(h,g,v){if(!Ge(h))return[!1];let _,S;typeof h.tracesSampler=="function"?(S=h.tracesSampler({...g,inheritOrSampleWith:T=>typeof g.parentSampleRate=="number"?g.parentSampleRate:typeof g.parentSampled=="boolean"?Number(g.parentSampled):T}),_=!0):g.parentSampled!==void 0?S=g.parentSampled:h.tracesSampleRate!==void 0&&(S=h.tracesSampleRate,_=!0);const y=nn(S);if(y===void 0)return O&&x.warn(`[Tracing] Discarding root span because of invalid sample rate. Sample rate must be a boolean or a number between 0 and 1. Got ${JSON.stringify(S)} of type ${JSON.stringify(typeof S)}.`),[!1];if(!y)return O&&x.log("[Tracing] Discarding transaction because "+(typeof h.tracesSampler=="function"?"tracesSampler returned 0 or false":"a negative sampling decision was inherited or tracesSampleRate is set to 0")),[!1,y,_];const b=v<y;return b||O&&x.log(`[Tracing] Discarding transaction because it's not included in the random sample (sampling rate = ${Number(S)})`),[b,y,_]}(s,{name:o,parentSampled:c,attributes:a,parentSampleRate:nn((m=u.dsc)==null?void 0:m.sample_rate)},u.sampleRand),d=new ar({...e,attributes:{[Ce]:"custom",[bs]:p!==void 0&&f?p:void 0,...a},sampled:l});return!l&&r&&(O&&x.log("[Tracing] Discarding root span because its trace was not chosen to be sampled."),r.recordDroppedEvent("sample_rate","transaction")),r&&r.emit("spanStart",d),d}const jn={idleTimeout:1e3,finalTimeout:3e4,childSpanTimeout:15e3},uu="heartbeatFailed",lu="idleTimeout",du="finalTimeout",fu="externalFinish";function vo(e,t={}){const n=new Map;let r,s=!1,o=fu,i=!t.disableAutoFinish;const c=[],{idleTimeout:a=jn.idleTimeout,finalTimeout:u=jn.finalTimeout,childSpanTimeout:l=jn.childSpanTimeout,beforeSpanEnd:p}=t,f=H();if(!f||!Ge()){const y=new ut;return Mn(y,{sample_rate:"0",sampled:"false",...Ke(y)}),y}const d=W(),m=ce(),h=function(y){const b=_n(y);return Qt(W(),b),O&&x.log("[Tracing] Started span is an idle span"),b}(e);function g(){r&&(clearTimeout(r),r=void 0)}function v(y){g(),r=setTimeout(()=>{!s&&n.size===0&&i&&(o=lu,h.end(y))},a)}function _(y){r=setTimeout(()=>{!s&&i&&(o=uu,h.end(y))},l)}function S(y){s=!0,n.clear(),c.forEach(E=>E()),Qt(d,m);const b=B(h),{start_timestamp:T}=b;if(!T)return;b.data[Wn]||h.setAttribute(Wn,o),x.log(`[Tracing] Idle span "${b.op}" finished`);const w=Nn(h).filter(E=>E!==h);let L=0;w.forEach(E=>{E.isRecording()&&(E.setStatus({code:Y,message:"cancelled"}),E.end(y),O&&x.log("[Tracing] Cancelling span since span ended early",JSON.stringify(E,void 0,2)));const R=B(E),{timestamp:D=0,start_timestamp:k=0}=R,A=k<=y,$=D-k<=(u+a)/1e3;if(O){const P=JSON.stringify(E,void 0,2);A?$||x.log("[Tracing] Discarding span since it finished after idle span final timeout",P):x.log("[Tracing] Discarding span since it happened after idle span was finished",P)}$&&A||(function(P,I){P[at]&&P[at].delete(I)}(h,E),L++)}),L>0&&h.setAttribute("sentry.idle_span_discarded_spans",L)}return h.end=new Proxy(h.end,{apply(y,b,T){if(p&&p(h),b instanceof ut)return;const[w,...L]=T,E=ot(w||ne()),R=Nn(h).filter(P=>P!==h);if(!R.length)return S(E),Reflect.apply(y,b,[E,...L]);const D=R.map(P=>B(P).timestamp).filter(P=>!!P),k=D.length?Math.max(...D):void 0,A=B(h).start_timestamp,$=Math.min(A?A+u/1e3:1/0,Math.max(A||-1/0,Math.min(E,k||1/0)));return S($),Reflect.apply(y,b,[$,...L])}}),c.push(f.on("spanStart",y=>{if(!(s||y===h||B(y).timestamp)){var b;Nn(h).includes(y)&&(b=y.spanContext().spanId,g(),n.set(b,!0),_(ne()+l/1e3))}})),c.push(f.on("spanEnd",y=>{var b;s||(b=y.spanContext().spanId,n.has(b)&&n.delete(b),n.size===0&&v(ne()+a/1e3))})),c.push(f.on("idleSpanEnableAutoFinish",y=>{y===h&&(i=!0,v(),n.size&&_())})),t.disableAutoFinish||v(),setTimeout(()=>{s||(h.setStatus({code:Y,message:"deadline_exceeded"}),o=du,h.end())},u),h}var Ae;function pt(e){return new Ve(t=>{t(e)})}function Gn(e){return new Ve((t,n)=>{n(e)})}(function(e){e[e.PENDING=0]="PENDING",e[e.RESOLVED=1]="RESOLVED",e[e.REJECTED=2]="REJECTED"})(Ae||(Ae={}));class Ve{constructor(t){this._state=Ae.PENDING,this._handlers=[],this._runExecutor(t)}then(t,n){return new Ve((r,s)=>{this._handlers.push([!1,o=>{if(t)try{r(t(o))}catch(i){s(i)}else r(o)},o=>{if(n)try{r(n(o))}catch(i){s(i)}else s(o)}]),this._executeHandlers()})}catch(t){return this.then(n=>n,t)}finally(t){return new Ve((n,r)=>{let s,o;return this.then(i=>{o=!1,s=i,t&&t()},i=>{o=!0,s=i,t&&t()}).then(()=>{o?r(s):n(s)})})}_executeHandlers(){if(this._state===Ae.PENDING)return;const t=this._handlers.slice();this._handlers=[],t.forEach(n=>{n[0]||(this._state===Ae.RESOLVED&&n[1](this._value),this._state===Ae.REJECTED&&n[2](this._value),n[0]=!0)})}_runExecutor(t){const n=(o,i)=>{this._state===Ae.PENDING&&(sr(i)?i.then(r,s):(this._state=o,this._value=i,this._executeHandlers()))},r=o=>{n(Ae.RESOLVED,o)},s=o=>{n(Ae.REJECTED,o)};try{t(r,s)}catch(o){s(o)}}}function Vr(e,t,n,r=0){return new Ve((s,o)=>{const i=e[r];if(t===null||typeof i!="function")s(t);else{const c=i({...t},n);O&&i.id&&c===null&&x.log(`Event processor "${i.id}" dropped event`),sr(c)?c.then(a=>Vr(e,a,n,r+1).then(s)).then(null,o):Vr(e,c,n,r+1).then(s).then(null,o)}})}let kn,_o,br;function pu(e,t){const{fingerprint:n,span:r,breadcrumbs:s,sdkProcessingMetadata:o}=t;(function(i,c){const{extra:a,tags:u,user:l,contexts:p,level:f,transactionName:d}=c;Object.keys(a).length&&(i.extra={...a,...i.extra}),Object.keys(u).length&&(i.tags={...u,...i.tags}),Object.keys(l).length&&(i.user={...l,...i.user}),Object.keys(p).length&&(i.contexts={...p,...i.contexts}),f&&(i.level=f),d&&i.type!=="transaction"&&(i.transaction=d)})(e,t),r&&function(i,c){i.contexts={trace:Xc(c),...i.contexts},i.sdkProcessingMetadata={dynamicSamplingContext:Ke(c),...i.sdkProcessingMetadata};const a=te(c),u=B(a).description;u&&!i.transaction&&i.type==="transaction"&&(i.transaction=u)}(e,r),function(i,c){i.fingerprint=i.fingerprint?Array.isArray(i.fingerprint)?i.fingerprint:[i.fingerprint]:[],c&&(i.fingerprint=i.fingerprint.concat(c)),i.fingerprint.length||delete i.fingerprint}(e,n),function(i,c){const a=[...i.breadcrumbs||[],...c];i.breadcrumbs=a.length?a:void 0}(e,s),function(i,c){i.sdkProcessingMetadata={...i.sdkProcessingMetadata,...c}}(e,o)}function yo(e,t){const{extra:n,tags:r,user:s,contexts:o,level:i,sdkProcessingMetadata:c,breadcrumbs:a,fingerprint:u,eventProcessors:l,attachments:p,propagationContext:f,transactionName:d,span:m}=t;In(e,"extra",n),In(e,"tags",r),In(e,"user",s),In(e,"contexts",o),e.sdkProcessingMetadata=gn(e.sdkProcessingMetadata,c,2),i&&(e.level=i),d&&(e.transactionName=d),m&&(e.span=m),a.length&&(e.breadcrumbs=[...e.breadcrumbs,...a]),u.length&&(e.fingerprint=[...e.fingerprint,...u]),l.length&&(e.eventProcessors=[...e.eventProcessors,...l]),p.length&&(e.attachments=[...e.attachments,...p]),e.propagationContext={...e.propagationContext,...f}}function In(e,t,n){e[t]=gn(e[t],n,1)}function hu(e,t,n,r,s,o){const{normalizeDepth:i=3,normalizeMaxBreadth:c=1e3}=e,a={...t,event_id:t.event_id||n.event_id||ge(),timestamp:t.timestamp||mn()},u=n.integrations||e.integrations.map(m=>m.name);(function(m,h){const{environment:g,release:v,dist:_,maxValueLength:S=250}=h;m.environment=m.environment||g||Es,!m.release&&v&&(m.release=v),!m.dist&&_&&(m.dist=_);const y=m.request;y!=null&&y.url&&(y.url=Bn(y.url,S))})(a,e),function(m,h){h.length>0&&(m.sdk=m.sdk||{},m.sdk.integrations=[...m.sdk.integrations||[],...h])}(a,u),s&&s.emit("applyFrameMetadata",t),t.type===void 0&&function(m,h){var v,_;const g=function(S){const y=q._sentryDebugIds;if(!y)return{};const b=Object.keys(y);return br&&b.length===_o||(_o=b.length,br=b.reduce((T,w)=>{kn||(kn={});const L=kn[w];if(L)T[L[0]]=L[1];else{const E=S(w);for(let R=E.length-1;R>=0;R--){const D=E[R],k=D==null?void 0:D.filename,A=y[w];if(k&&A){T[k]=A,kn[w]=[k,A];break}}}return T},{})),br}(h);(_=(v=m.exception)==null?void 0:v.values)==null||_.forEach(S=>{var y,b;(b=(y=S.stacktrace)==null?void 0:y.frames)==null||b.forEach(T=>{T.filename&&(T.debug_id=g[T.filename])})})}(a,e.stackParser);const l=function(m,h){if(!h)return m;const g=m?m.clone():new Me;return g.update(h),g}(r,n.captureContext);n.mechanism&&Tt(a,n.mechanism);const p=s?s.getEventProcessors():[],f=Un("globalScope",()=>new Me).getScopeData();o&&yo(f,o.getScopeData()),l&&yo(f,l.getScopeData());const d=[...n.attachments||[],...f.attachments];return d.length&&(n.attachments=d),pu(a,f),Vr([...p,...f.eventProcessors],a,n).then(m=>(m&&function(h){var _,S;const g={};if((S=(_=h.exception)==null?void 0:_.values)==null||S.forEach(y=>{var b,T;(T=(b=y.stacktrace)==null?void 0:b.frames)==null||T.forEach(w=>{w.debug_id&&(w.abs_path?g[w.abs_path]=w.debug_id:w.filename&&(g[w.filename]=w.debug_id),delete w.debug_id)})}),Object.keys(g).length===0)return;h.debug_meta=h.debug_meta||{},h.debug_meta.images=h.debug_meta.images||[];const v=h.debug_meta.images;Object.entries(g).forEach(([y,b])=>{v.push({type:"sourcemap",code_file:y,debug_id:b})})}(m),typeof i=="number"&&i>0?function(h,g,v){var S,y;if(!h)return null;const _={...h,...h.breadcrumbs&&{breadcrumbs:h.breadcrumbs.map(b=>({...b,...b.data&&{data:Pe(b.data,g,v)}}))},...h.user&&{user:Pe(h.user,g,v)},...h.contexts&&{contexts:Pe(h.contexts,g,v)},...h.extra&&{extra:Pe(h.extra,g,v)}};return(S=h.contexts)!=null&&S.trace&&_.contexts&&(_.contexts.trace=h.contexts.trace,h.contexts.trace.data&&(_.contexts.trace.data=Pe(h.contexts.trace.data,g,v))),h.spans&&(_.spans=h.spans.map(b=>({...b,...b.data&&{data:Pe(b.data,g,v)}}))),(y=h.contexts)!=null&&y.flags&&_.contexts&&(_.contexts.flags=Pe(h.contexts.flags,3,v)),_}(m,i,c):m))}function Yr(e,t){return W().captureException(e,void 0)}function bo(e,t){return W().captureEvent(e,t)}function So(e){const t=Xe(),n=W(),{userAgent:r}=q.navigator||{},s=qc({user:n.getUser()||t.getUser(),...r&&{userAgent:r},...e}),o=t.getSession();return(o==null?void 0:o.status)==="ok"&&kt(o,{status:"exited"}),sa(),t.setSession(s),s}function sa(){const e=Xe(),t=W().getSession()||e.getSession();t&&function(n,r){let s={};n.status==="ok"&&(s={status:"exited"}),kt(n,s)}(t),oa(),e.setSession()}function oa(){const e=Xe(),t=H(),n=e.getSession();n&&t&&t.captureSession(n)}function wo(e=!1){e?sa():oa()}const mu="7";function gu(e,t,n){return t||`${function(r){return`${function(s){const o=s.protocol?`${s.protocol}:`:"",i=s.port?`:${s.port}`:"";return`${o}//${s.host}${i}${s.path?`/${s.path}`:""}/api/`}(r)}${r.projectId}/envelope/`}(e)}?${function(r,s){const o={sentry_version:mu};return r.publicKey&&(o.sentry_key=r.publicKey),s&&(o.sentry_client=`${s.name}/${s.version}`),new URLSearchParams(o).toString()}(e,n)}`}const Eo=[];function vu(e){const t=e.defaultIntegrations||[],n=e.integrations;let r;if(t.forEach(s=>{s.isDefaultInstance=!0}),Array.isArray(n))r=[...t,...n];else if(typeof n=="function"){const s=n(t);r=Array.isArray(s)?s:[s]}else r=t;return function(s){const o={};return s.forEach(i=>{const{name:c}=i,a=o[c];a&&!a.isDefaultInstance&&i.isDefaultInstance||(o[c]=i)}),Object.values(o)}(r)}function xo(e,t){for(const n of t)n!=null&&n.afterAllSetup&&n.afterAllSetup(e)}function To(e,t,n){if(n[t.name])O&&x.log(`Integration skipped because it was already installed: ${t.name}`);else{if(n[t.name]=t,Eo.indexOf(t.name)===-1&&typeof t.setupOnce=="function"&&(t.setupOnce(),Eo.push(t.name)),t.setup&&typeof t.setup=="function"&&t.setup(e),typeof t.preprocessEvent=="function"){const r=t.preprocessEvent.bind(t);e.on("preprocessEvent",(s,o)=>r(s,o,e))}if(typeof t.processEvent=="function"){const r=t.processEvent.bind(t),s=Object.assign((o,i)=>r(o,i,e),{id:t.name});e.addEventProcessor(s)}O&&x.log(`Integration installed: ${t.name}`)}}function ia(e){const t=[];e.message&&t.push(e.message);try{const n=e.exception.values[e.exception.values.length-1];n!=null&&n.value&&(t.push(n.value),n.type&&t.push(`${n.type}: ${n.value}`))}catch{}return t}const ko="Not capturing exception because it's already been captured.",Io="Discarded session because of missing or non-string release",aa=Symbol.for("SentryInternalError"),ca=Symbol.for("SentryDoNotSendEventError");function $n(e){return{message:e,[aa]:!0}}function Sr(e){return{message:e,[ca]:!0}}function $o(e){return!!e&&typeof e=="object"&&aa in e}function Po(e){return!!e&&typeof e=="object"&&ca in e}class _u{constructor(t){if(this._options=t,this._integrations={},this._numProcessing=0,this._outcomes={},this._hooks={},this._eventProcessors=[],t.dsn?this._dsn=nu(t.dsn):O&&x.warn("No DSN provided, client will not send events."),this._dsn){const n=gu(this._dsn,t.tunnel,t._metadata?t._metadata.sdk:void 0);this._transport=t.transport({tunnel:this._options.tunnel,recordDroppedEvent:this.recordDroppedEvent.bind(this),...t.transportOptions,url:n})}}captureException(t,n,r){const s=ge();if(Ks(t))return O&&x.log(ko),s;const o={event_id:s,...n};return this._process(this.eventFromException(t,o).then(i=>this._captureEvent(i,o,r))),o.event_id}captureMessage(t,n,r,s){const o={event_id:ge(),...r},i=vs(t)?t:String(t),c=Xt(t)?this.eventFromMessage(i,n,o):this.eventFromException(t,o);return this._process(c.then(a=>this._captureEvent(a,o,s))),o.event_id}captureEvent(t,n,r){const s=ge();if(n!=null&&n.originalException&&Ks(n.originalException))return O&&x.log(ko),s;const o={event_id:s,...n},i=t.sdkProcessingMetadata||{},c=i.capturedSpanScope,a=i.capturedSpanIsolationScope;return this._process(this._captureEvent(t,o,c||r,a)),o.event_id}captureSession(t){this.sendSession(t),kt(t,{init:!1})}getDsn(){return this._dsn}getOptions(){return this._options}getSdkMetadata(){return this._options._metadata}getTransport(){return this._transport}flush(t){const n=this._transport;return n?(this.emit("flush"),this._isClientDoneProcessing(t).then(r=>n.flush(t).then(s=>r&&s))):pt(!0)}close(t){return this.flush(t).then(n=>(this.getOptions().enabled=!1,this.emit("close"),n))}getEventProcessors(){return this._eventProcessors}addEventProcessor(t){this._eventProcessors.push(t)}init(){(this._isEnabled()||this._options.integrations.some(({name:t})=>t.startsWith("Spotlight")))&&this._setupIntegrations()}getIntegrationByName(t){return this._integrations[t]}addIntegration(t){const n=this._integrations[t.name];To(this,t,this._integrations),n||xo(this,[t])}sendEvent(t,n={}){this.emit("beforeSendEvent",t,n);let r=cu(t,this._dsn,this._options._metadata,this._options.tunnel);for(const o of n.attachments||[])r=ru(r,iu(o));const s=this.sendEnvelope(r);s&&s.then(o=>this.emit("afterSendEvent",t,o),null)}sendSession(t){const{release:n,environment:r=Es}=this._options;if("aggregates"in t){const o=t.attrs||{};if(!o.release&&!n)return void(O&&x.warn(Io));o.release=o.release||n,o.environment=o.environment||r,t.attrs=o}else{if(!t.release&&!n)return void(O&&x.warn(Io));t.release=t.release||n,t.environment=t.environment||r}this.emit("beforeSendSession",t);const s=function(o,i,c,a){const u=ea(c);return $t({sent_at:new Date().toISOString(),...u&&{sdk:u},...!!a&&i&&{dsn:rn(i)}},["aggregates"in o?[{type:"sessions"},o]:[{type:"session"},o.toJSON()]])}(t,this._dsn,this._options._metadata,this._options.tunnel);this.sendEnvelope(s)}recordDroppedEvent(t,n,r=1){if(this._options.sendClientReports){const s=`${t}:${n}`;O&&x.log(`Recording outcome: "${s}"${r>1?` (${r} times)`:""}`),this._outcomes[s]=(this._outcomes[s]||0)+r}}on(t,n){const r=this._hooks[t]=this._hooks[t]||[];return r.push(n),()=>{const s=r.indexOf(n);s>-1&&r.splice(s,1)}}emit(t,...n){const r=this._hooks[t];r&&r.forEach(s=>s(...n))}sendEnvelope(t){return this.emit("beforeEnvelope",t),this._isEnabled()&&this._transport?this._transport.send(t).then(null,n=>(O&&x.error("Error while sending envelope:",n),n)):(O&&x.error("Transport disabled"),pt({}))}_setupIntegrations(){const{integrations:t}=this._options;this._integrations=function(n,r){const s={};return r.forEach(o=>{o&&To(n,o,s)}),s}(this,t),xo(this,t)}_updateSessionFromEvent(t,n){var c;let r=n.level==="fatal",s=!1;const o=(c=n.exception)==null?void 0:c.values;if(o){s=!0;for(const a of o){const u=a.mechanism;if((u==null?void 0:u.handled)===!1){r=!0;break}}}const i=t.status==="ok";(i&&t.errors===0||i&&r)&&(kt(t,{...r&&{status:"crashed"},errors:t.errors||Number(s||r)}),this.captureSession(t))}_isClientDoneProcessing(t){return new Ve(n=>{let r=0;const s=setInterval(()=>{this._numProcessing==0?(clearInterval(s),n(!0)):(r+=1,t&&r>=t&&(clearInterval(s),n(!1)))},1)})}_isEnabled(){return this.getOptions().enabled!==!1&&this._transport!==void 0}_prepareEvent(t,n,r,s){const o=this.getOptions(),i=Object.keys(this._integrations);return!n.integrations&&(i!=null&&i.length)&&(n.integrations=i),this.emit("preprocessEvent",t,n),t.type||s.setLastEventId(t.event_id||n.event_id),hu(o,t,n,r,this,s).then(c=>{if(c===null)return c;this.emit("postprocessEvent",c,n),c.contexts={trace:Bc(r),...c.contexts};const a=Zi(this,r);return c.sdkProcessingMetadata={dynamicSamplingContext:a,...c.sdkProcessingMetadata},c})}_captureEvent(t,n={},r=W(),s=Xe()){return O&&wr(t)&&x.log(`Captured error event \`${ia(t)[0]||"<unknown>"}\``),this._processEvent(t,n,r,s).then(o=>o.event_id,o=>{O&&(Po(o)?x.log(o.message):$o(o)?x.warn(o.message):x.warn(o))})}_processEvent(t,n,r,s){const o=this.getOptions(),{sampleRate:i}=o,c=Ao(t),a=wr(t),u=t.type||"error",l=`before send for type \`${u}\``,p=i===void 0?void 0:nn(i);if(a&&typeof p=="number"&&Math.random()>p)return this.recordDroppedEvent("sample_rate","error"),Gn(Sr(`Discarding event because it's not included in the random sample (sampling rate = ${i})`));const f=u==="replay_event"?"replay":u;return this._prepareEvent(t,n,r,s).then(d=>{if(d===null)throw this.recordDroppedEvent("event_processor",f),Sr("An event processor returned `null`, will not send event.");if(n.data&&n.data.__sentry__===!0)return d;const m=function(h,g,v,_){const{beforeSend:S,beforeSendTransaction:y,beforeSendSpan:b}=g;let T=v;if(wr(T)&&S)return S(T,_);if(Ao(T)){if(b){const L=b(function(E){var C;const{trace_id:R,parent_span_id:D,span_id:k,status:A,origin:$,data:P,op:I}=((C=E.contexts)==null?void 0:C.trace)??{};return{data:P??{},description:E.transaction,op:I,parent_span_id:D,span_id:k??"",start_timestamp:E.start_timestamp??0,status:A,timestamp:E.timestamp,trace_id:R??"",origin:$,profile_id:P==null?void 0:P[Br],exclusive_time:P==null?void 0:P[tn],measurements:E.measurements,is_segment:!0}}(T));if(L?T=gn(v,{type:"transaction",timestamp:(w=L).timestamp,start_timestamp:w.start_timestamp,transaction:w.description,contexts:{trace:{trace_id:w.trace_id,span_id:w.span_id,parent_span_id:w.parent_span_id,op:w.op,status:w.status,origin:w.origin,data:{...w.data,...w.profile_id&&{[Br]:w.profile_id},...w.exclusive_time&&{[tn]:w.exclusive_time}}}},measurements:w.measurements}):Wr(),T.spans){const E=[];for(const R of T.spans){const D=b(R);D?E.push(D):(Wr(),E.push(R))}T.spans=E}}if(y){if(T.spans){const L=T.spans.length;T.sdkProcessingMetadata={...v.sdkProcessingMetadata,spanCountBeforeProcessing:L}}return y(T,_)}}var w;return T}(0,o,d,n);return function(h,g){const v=`${g} must return \`null\` or a valid event.`;if(sr(h))return h.then(_=>{if(!Zt(_)&&_!==null)throw $n(v);return _},_=>{throw $n(`${g} rejected with ${_}`)});if(!Zt(h)&&h!==null)throw $n(v);return h}(m,l)}).then(d=>{var g;if(d===null){if(this.recordDroppedEvent("before_send",f),c){const v=1+(t.spans||[]).length;this.recordDroppedEvent("before_send","span",v)}throw Sr(`${l} returned \`null\`, will not send event.`)}const m=r.getSession()||s.getSession();if(a&&m&&this._updateSessionFromEvent(m,d),c){const v=(((g=d.sdkProcessingMetadata)==null?void 0:g.spanCountBeforeProcessing)||0)-(d.spans?d.spans.length:0);v>0&&this.recordDroppedEvent("before_send","span",v)}const h=d.transaction_info;if(c&&h&&d.transaction!==t.transaction){const v="custom";d.transaction_info={...h,source:v}}return this.sendEvent(d,n),d}).then(null,d=>{throw Po(d)||$o(d)?d:(this.captureException(d,{data:{__sentry__:!0},originalException:d}),$n(`Event processing pipeline threw an error, original event will not be sent. Details have been sent as a new event.
Reason: ${d}`))})}_process(t){this._numProcessing++,t.then(n=>(this._numProcessing--,n),n=>(this._numProcessing--,n))}_clearOutcomes(){const t=this._outcomes;return this._outcomes={},Object.entries(t).map(([n,r])=>{const[s,o]=n.split(":");return{reason:s,category:o,quantity:r}})}_flushOutcomes(){O&&x.log("Flushing outcomes...");const t=this._clearOutcomes();if(t.length===0)return void(O&&x.log("No outcomes to send"));if(!this._dsn)return void(O&&x.log("No dsn provided, will not send outcomes"));O&&x.log("Sending outcomes:",t);const n=(r=t,$t((s=this._options.tunnel&&rn(this._dsn))?{dsn:s}:{},[[{type:"client_report"},{timestamp:mn(),discarded_events:r}]]));var r,s;this.sendEnvelope(n)}}function wr(e){return e.type===void 0}function Ao(e){return e.type==="transaction"}function Er(e,t){var o;const n=function(i){var c;return(c=q._sentryClientToLogBufferMap)==null?void 0:c.get(i)}(e)??[];if(n.length===0)return;const r=e.getOptions(),s=function(i,c,a,u){const l={};return c!=null&&c.sdk&&(l.sdk={name:c.sdk.name,version:c.sdk.version}),a&&u&&(l.dsn=rn(u)),$t(l,[(p=i,[{type:"log",item_count:p.length,content_type:"application/vnd.sentry.items.log+json"},{items:p}])]);var p}(n,r._metadata,r.tunnel,e.getDsn());(o=q._sentryClientToLogBufferMap)==null||o.set(e,[]),e.emit("flushLogs"),e.sendEnvelope(s)}function yu(e,t){t.debug===!0&&(O?x.enable():vt(()=>{console.warn("[Sentry] Cannot initialize SDK with `debug` option using a non-debug bundle.")})),W().update(t.initialScope);const n=new e(t);return function(r){W().setClient(r)}(n),n.init(),n}q._sentryClientToLogBufferMap=new WeakMap;const ua=Symbol.for("SentryBufferFullError");function bu(e){const t=[];function n(r){return t.splice(t.indexOf(r),1)[0]||Promise.resolve(void 0)}return{$:t,add:function(r){if(!(e===void 0||t.length<e))return Gn(ua);const s=r();return t.indexOf(s)===-1&&t.push(s),s.then(()=>n(s)).then(null,()=>n(s).then(null,()=>{})),s},drain:function(r){return new Ve((s,o)=>{let i=t.length;if(!i)return s(!0);const c=setTimeout(()=>{r&&r>0&&s(!1)},r);t.forEach(a=>{pt(a).then(()=>{--i||(clearTimeout(c),s(!0))},o)})})}}}const Su=6e4;function wu(e,{statusCode:t,headers:n},r=Date.now()){const s={...e},o=n==null?void 0:n["x-sentry-rate-limits"],i=n==null?void 0:n["retry-after"];if(o)for(const c of o.trim().split(",")){const[a,u,,,l]=c.split(":",5),p=parseInt(a,10),f=1e3*(isNaN(p)?60:p);if(u)for(const d of u.split(";"))d==="metric_bucket"&&l&&!l.split(";").includes("custom")||(s[d]=r+f);else s.all=r+f}else i?s.all=r+function(c,a=Date.now()){const u=parseInt(`${c}`,10);if(!isNaN(u))return 1e3*u;const l=Date.parse(`${c}`);return isNaN(l)?Su:l-a}(i,r):t===429&&(s.all=r+6e4);return s}const Eu=64;function xu(e,t,n=bu(e.bufferSize||Eu)){let r={};return{send:function(s){const o=[];if(lo(s,(a,u)=>{const l=fo(u);(function(p,f,d=Date.now()){return function(m,h){return m[h]||m.all||0}(p,f)>d})(r,l)?e.recordDroppedEvent("ratelimit_backoff",l):o.push(a)}),o.length===0)return pt({});const i=$t(s[0],o),c=a=>{lo(i,(u,l)=>{e.recordDroppedEvent(a,fo(l))})};return n.add(()=>t({body:su(i)}).then(a=>(a.statusCode!==void 0&&(a.statusCode<200||a.statusCode>=300)&&O&&x.warn(`Sentry responded with status code ${a.statusCode} to sent event.`),r=wu(r,a),a),a=>{throw c("network_error"),O&&x.error("Encountered error running transport request:",a),a})).then(a=>a,a=>{if(a===ua)return O&&x.error("Skipped sending event because buffer is full."),c("queue_overflow"),pt({});throw a})},flush:s=>n.drain(s)}}function Tu(e){var t;((t=e.user)==null?void 0:t.ip_address)===void 0&&(e.user={...e.user,ip_address:"{{auto}}"})}function ku(e){var t;"aggregates"in e?((t=e.attrs)==null?void 0:t.ip_address)===void 0&&(e.attrs={...e.attrs,ip_address:"{{auto}}"}):e.ipAddress===void 0&&(e.ipAddress="{{auto}}")}function la(e,t,n=[t],r="npm"){const s=e._metadata||{};s.sdk||(s.sdk={name:`sentry.javascript.${t}`,packages:n.map(o=>({name:`${r}:@sentry/${o}`,version:st})),version:st}),e._metadata=s}function da(e={}){const t=H();if(!function(){const c=H();return(c==null?void 0:c.getOptions().enabled)!==!1&&!!(c!=null&&c.getTransport())}()||!t)return{};const n=Nt(gt());if(n.getTraceData)return n.getTraceData(e);const r=W(),s=e.span||ce(),o=s?function(c){const{traceId:a,spanId:u}=c.spanContext();return Zs(a,u,it(c))}(s):function(c){const{traceId:a,sampled:u,propagationSpanId:l}=c.getPropagationContext();return Zs(a,l,u)}(r),i=Kc(s?Ke(s):Zi(t,r));return zi.test(o)?{"sentry-trace":o,baggage:i}:(x.warn("Invalid sentry-trace data. Cannot generate trace data"),{})}const Iu=100;function et(e,t){const n=H(),r=Xe();if(!n)return;const{beforeBreadcrumb:s=null,maxBreadcrumbs:o=Iu}=n.getOptions();if(o<=0)return;const i={timestamp:mn(),...e},c=s?vt(()=>s(i,t)):i;c!==null&&(n.emit&&n.emit("beforeAddBreadcrumb",c,t),r.addBreadcrumb(c,o))}let Oo;const Co=new WeakMap,$u=()=>({name:"FunctionToString",setupOnce(){Oo=Function.prototype.toString;try{Function.prototype.toString=function(...e){const t=ys(this),n=Co.has(H())&&t!==void 0?t:this;return Oo.apply(n,e)}}catch{}},setup(e){Co.set(e,!0)}}),Pu=[/^Script error\.?$/,/^Javascript error: Script error\.? on line 0$/,/^ResizeObserver loop completed with undelivered notifications.$/,/^Cannot redefine property: googletag$/,/^Can't find variable: gmo$/,/^undefined is not an object \(evaluating 'a\.[A-Z]'\)$/,`can't redefine non-configurable property "solana"`,"vv().getRestrictions is not a function. (In 'vv().getRestrictions(1,a)', 'vv().getRestrictions' is undefined)","Can't find variable: _AutofillCallbackHandler",/^Non-Error promise rejection captured with value: Object Not Found Matching Id:\d+, MethodName:simulateEvent, ParamCount:\d+$/,/^Java exception was raised during method invocation$/],Au=(e={})=>{let t;return{name:"EventFilters",setup(n){const r=n.getOptions();t=Ro(e,r)},processEvent(n,r,s){if(!t){const o=s.getOptions();t=Ro(e,o)}return function(o,i){if(o.type){if(o.type==="transaction"&&function(c,a){if(!(a!=null&&a.length))return!1;const u=c.transaction;return!!u&&rt(u,a)}(o,i.ignoreTransactions))return O&&x.warn(`Event dropped due to being matched by \`ignoreTransactions\` option.
Event: ${tt(o)}`),!0}else{if(function(c,a){return a!=null&&a.length?ia(c).some(u=>rt(u,a)):!1}(o,i.ignoreErrors))return O&&x.warn(`Event dropped due to being matched by \`ignoreErrors\` option.
Event: ${tt(o)}`),!0;if(function(c){var a,u;return(u=(a=c.exception)==null?void 0:a.values)!=null&&u.length?!c.message&&!c.exception.values.some(l=>l.stacktrace||l.type&&l.type!=="Error"||l.value):!1}(o))return O&&x.warn(`Event dropped due to not having an error message, error type or stacktrace.
Event: ${tt(o)}`),!0;if(function(c,a){if(!(a!=null&&a.length))return!1;const u=Pn(c);return!!u&&rt(u,a)}(o,i.denyUrls))return O&&x.warn(`Event dropped due to being matched by \`denyUrls\` option.
Event: ${tt(o)}.
Url: ${Pn(o)}`),!0;if(!function(c,a){if(!(a!=null&&a.length))return!0;const u=Pn(c);return!u||rt(u,a)}(o,i.allowUrls))return O&&x.warn(`Event dropped due to not being matched by \`allowUrls\` option.
Event: ${tt(o)}.
Url: ${Pn(o)}`),!0}return!1}(n,t)?null:n}}},Ou=(e={})=>({...Au(e),name:"InboundFilters"});function Ro(e={},t={}){return{allowUrls:[...e.allowUrls||[],...t.allowUrls||[]],denyUrls:[...e.denyUrls||[],...t.denyUrls||[]],ignoreErrors:[...e.ignoreErrors||[],...t.ignoreErrors||[],...e.disableErrorDefaults?[]:Pu],ignoreTransactions:[...e.ignoreTransactions||[],...t.ignoreTransactions||[]]}}function Pn(e){var t,n;try{const r=[...((t=e.exception)==null?void 0:t.values)??[]].reverse().find(o=>{var i,c,a;return((i=o.mechanism)==null?void 0:i.parent_id)===void 0&&((a=(c=o.stacktrace)==null?void 0:c.frames)==null?void 0:a.length)}),s=(n=r==null?void 0:r.stacktrace)==null?void 0:n.frames;return s?function(o=[]){for(let i=o.length-1;i>=0;i--){const c=o[i];if(c&&c.filename!=="<anonymous>"&&c.filename!=="[native code]")return c.filename||null}return null}(s):null}catch{return O&&x.error(`Cannot extract url for event ${tt(e)}`),null}}function Cu(e,t,n,r,s,o){var c;if(!((c=s.exception)!=null&&c.values)||!o||!Le(o.originalException,Error))return;const i=s.exception.values.length>0?s.exception.values[s.exception.values.length-1]:void 0;i&&(s.exception.values=Xr(e,t,r,o.originalException,n,s.exception.values,i,0))}function Xr(e,t,n,r,s,o,i,c){if(o.length>=n+1)return o;let a=[...o];if(Le(r[s],Error)){Do(i,c);const u=e(t,r[s]),l=a.length;No(u,s,l,c),a=Xr(e,t,n,r[s],s,[u,...a],u,l)}return Array.isArray(r.errors)&&r.errors.forEach((u,l)=>{if(Le(u,Error)){Do(i,c);const p=e(t,u),f=a.length;No(p,`errors[${l}]`,f,c),a=Xr(e,t,n,u,s,[p,...a],p,f)}}),a}function Do(e,t){e.mechanism=e.mechanism||{type:"generic",handled:!0},e.mechanism={...e.mechanism,...e.type==="AggregateError"&&{is_exception_group:!0},exception_id:t}}function No(e,t,n,r){e.mechanism=e.mechanism||{type:"generic",handled:!0},e.mechanism={...e.mechanism,type:"chained",source:t,exception_id:n,parent_id:r}}function Ru(){"console"in q&&Fr.forEach(function(e){e in q.console&&ue(q.console,e,function(t){return Hn[e]=t,function(...n){ve("console",{args:n,level:e});const r=Hn[e];r==null||r.apply(q.console,n)}})})}function Du(e){return e==="warn"?"warning":["fatal","error","warning","log","info","debug"].includes(e)?e:"log"}const Nu=()=>{let e;return{name:"Dedupe",processEvent(t){if(t.type)return t;try{if(function(n,r){return r?!!(function(s,o){const i=s.message,c=o.message;return!(!i&&!c||i&&!c||!i&&c||i!==c||!Mo(s,o)||!Lo(s,o))}(n,r)||function(s,o){const i=jo(o),c=jo(s);return!(!i||!c||i.type!==c.type||i.value!==c.value||!Mo(s,o)||!Lo(s,o))}(n,r)):!1}(t,e))return O&&x.warn("Event dropped due to being a duplicate of previously captured event."),null}catch{}return e=t}}};function Lo(e,t){let n=io(e),r=io(t);if(!n&&!r)return!0;if(n&&!r||!n&&r||r.length!==n.length)return!1;for(let s=0;s<r.length;s++){const o=r[s],i=n[s];if(o.filename!==i.filename||o.lineno!==i.lineno||o.colno!==i.colno||o.function!==i.function)return!1}return!0}function Mo(e,t){let n=e.fingerprint,r=t.fingerprint;if(!n&&!r)return!0;if(n&&!r||!n&&r)return!1;try{return n.join("")===r.join("")}catch{return!1}}function jo(e){var t;return((t=e.exception)==null?void 0:t.values)&&e.exception.values[0]}const Lu="thismessage:/";function fa(e){return"isRelative"in e}function pa(e,t){const n=e.indexOf("://")<=0&&e.indexOf("//")!==0,r=n?Lu:void 0;try{if("canParse"in URL&&!URL.canParse(e,r))return;const s=new URL(e,r);return n?{isRelative:n,pathname:s.pathname,search:s.search,hash:s.hash}:s}catch{}}function Mu(e){if(fa(e))return e.pathname;const t=new URL(e);return t.search="",t.hash="",["80","443"].includes(t.port)&&(t.port=""),t.password&&(t.password="%filtered%"),t.username&&(t.username="%filtered%"),t.toString()}function xt(e){if(!e)return{};const t=e.match(/^(([^:/?#]+):)?(\/\/([^/?#]*))?([^?#]*)(\?([^#]*))?(#(.*))?$/);if(!t)return{};const n=t[6]||"",r=t[8]||"";return{host:t[4],path:t[5],protocol:t[2],search:n,hash:r,relative:t[5]+n+r}}function ju(e,t,n,r,s="auto.http.browser"){if(!e.fetchData)return;const{method:o,url:i}=e.fetchData,c=Ge()&&t(i);if(e.endTimestamp&&c){const p=e.fetchData.__span;if(!p)return;const f=r[p];return void(f&&(function(d,m){var h;if(m.response){Fi(d,m.response.status);const g=((h=m.response)==null?void 0:h.headers)&&m.response.headers.get("content-length");if(g){const v=parseInt(g);v>0&&d.setAttribute("http.response_content_length",v)}}else m.error&&d.setStatus({code:Y,message:"internal_error"});d.end()}(f,e),delete r[p]))}const a=!!ce(),u=c&&a?_n(function(p,f,d){const m=pa(p);return{name:m?`${f} ${Mu(m)}`:f,attributes:qu(p,m,f,d)}}(i,o,s)):new ut;if(e.fetchData.__span=u.spanContext().spanId,r[u.spanContext().spanId]=u,n(e.fetchData.url)){const p=e.args[0],f=e.args[1]||{},d=function(m,h,g){const v=da({span:g}),_=v["sentry-trace"],S=v.baggage;if(!_)return;const y=h.headers||(Ci(m)?m.headers:void 0);if(y){if(function(b){return typeof Headers<"u"&&Le(b,Headers)}(y)){const b=new Headers(y);if(b.get("sentry-trace")||b.set("sentry-trace",_),S){const T=b.get("baggage");T?An(T)||b.set("baggage",`${T},${S}`):b.set("baggage",S)}return b}if(Array.isArray(y)){const b=[...y];y.find(w=>w[0]==="sentry-trace")||b.push(["sentry-trace",_]);const T=y.find(w=>w[0]==="baggage"&&An(w[1]));return S&&!T&&b.push(["baggage",S]),b}{const b="sentry-trace"in y?y["sentry-trace"]:void 0,T="baggage"in y?y.baggage:void 0,w=T?Array.isArray(T)?[...T]:[T]:[],L=T&&(Array.isArray(T)?T.find(E=>An(E)):An(T));return S&&!L&&w.push(S),{...y,"sentry-trace":b??_,baggage:w.length>0?w.join(","):void 0}}}return{...v}}(p,f,Ge()&&a?u:void 0);d&&(e.args[1]=f,f.headers=d)}const l=H();if(l){const p={input:e.args,response:e.response,startTimestamp:e.startTimestamp,endTimestamp:e.endTimestamp};l.emit("beforeOutgoingRequestSpan",u,p)}return u}function An(e){return e.split(",").some(t=>t.trim().startsWith(Ss))}function qu(e,t,n,r){const s={url:e,type:"fetch","http.method":n,[X]:r,[ft]:"http.client"};return t&&(fa(t)||(s["http.url"]=t.href,s["server.address"]=t.host),t.search&&(s["http.query"]=t.search),t.hash&&(s["http.fragment"]=t.hash)),s}function qo(e){return e===void 0?void 0:e>=400&&e<500?"warning":e>=500?"error":void 0}const sn=q;function ha(){if(!("fetch"in sn))return!1;try{return new Headers,new Request("http://www.example.com"),new Response,!0}catch{return!1}}function Zr(e){return e&&/^function\s+\w+\(\)\s+\{\s+\[native code\]\s+\}$/.test(e.toString())}function ma(e,t){const n="fetch";We(n,e),Je(n,()=>ga(void 0,t))}function ga(e,t=!1){t&&!function(){var s;if(typeof EdgeRuntime=="string")return!0;if(!ha())return!1;if(Zr(sn.fetch))return!0;let n=!1;const r=sn.document;if(r&&typeof r.createElement=="function")try{const o=r.createElement("iframe");o.hidden=!0,r.head.appendChild(o),(s=o.contentWindow)!=null&&s.fetch&&(n=Zr(o.contentWindow.fetch)),r.head.removeChild(o)}catch(o){O&&x.warn("Could not create sandbox iframe for pure fetch check, bailing to window.fetch: ",o)}return n}()||ue(q,"fetch",function(n){return function(...r){const s=new Error,{method:o,url:i}=function(a){if(a.length===0)return{method:"GET",url:""};if(a.length===2){const[l,p]=a;return{url:Fo(l),method:Qr(p,"method")?String(p.method).toUpperCase():"GET"}}const u=a[0];return{url:Fo(u),method:Qr(u,"method")?String(u.method).toUpperCase():"GET"}}(r),c={args:r,fetchData:{method:o,url:i},startTimestamp:1e3*ne(),virtualError:s,headers:Uu(r)};return e||ve("fetch",{...c}),n.apply(q,r).then(async a=>(e?e(a):ve("fetch",{...c,endTimestamp:1e3*ne(),response:a}),a),a=>{if(ve("fetch",{...c,endTimestamp:1e3*ne(),error:a}),gs(a)&&a.stack===void 0&&(a.stack=s.stack,de(a,"framesToPop",1)),a instanceof TypeError&&(a.message==="Failed to fetch"||a.message==="Load failed"||a.message==="NetworkError when attempting to fetch resource."))try{const u=new URL(c.fetchData.url);a.message=`${a.message} (${u.host})`}catch{}throw a})}})}function Fu(e){let t;try{t=e.clone()}catch{return}(async function(n,r){if(n!=null&&n.body){const s=n.body,o=s.getReader(),i=setTimeout(()=>{s.cancel().then(null,()=>{})},9e4);let c=!0;for(;c;){let a;try{a=setTimeout(()=>{s.cancel().then(null,()=>{})},5e3);const{done:u}=await o.read();clearTimeout(a),u&&(r(),c=!1)}catch{c=!1}finally{clearTimeout(a)}}clearTimeout(i),o.releaseLock(),s.cancel().then(null,()=>{})}})(t,()=>{ve("fetch-body-resolved",{endTimestamp:1e3*ne(),response:e})})}function Qr(e,t){return!!e&&typeof e=="object"&&!!e[t]}function Fo(e){return typeof e=="string"?e:e?Qr(e,"url")?e.url:e.toString?e.toString():"":""}function Uu(e){const[t,n]=e;try{if(typeof n=="object"&&n!==null&&"headers"in n&&n.headers)return new Headers(n.headers);if(Ci(t))return new Headers(t.headers)}catch{}}const j=q;let es=0;function Uo(){return es>0}function Pt(e,t={}){if(!function(r){return typeof r=="function"}(e))return e;try{const r=e.__sentry_wrapped__;if(r)return typeof r=="function"?r:e;if(ys(e))return e}catch{return e}const n=function(...r){try{const s=r.map(o=>Pt(o,t));return e.apply(this,s)}catch(s){throw es++,setTimeout(()=>{es--}),en(o=>{o.addEventProcessor(i=>(t.mechanism&&(Ur(i,void 0),Tt(i,t.mechanism)),i.extra={...i.extra,arguments:r},i)),Yr(s)}),s}};try{for(const r in e)Object.prototype.hasOwnProperty.call(e,r)&&(n[r]=e[r])}catch{}Di(n,e),de(e,"__sentry_wrapped__",n);try{Object.getOwnPropertyDescriptor(n,"name").configurable&&Object.defineProperty(n,"name",{get:()=>e.name})}catch{}return n}function ts(){const e=hn(),{referrer:t}=j.document||{},{userAgent:n}=j.navigator||{};return{url:e,headers:{...t&&{Referer:t},...n&&{"User-Agent":n}}}}function xs(e,t){const n=Ts(e,t),r={type:zu(t),value:Wu(t)};return n.length&&(r.stacktrace={frames:n}),r.type===void 0&&r.value===""&&(r.value="Unrecoverable error caught"),r}function Hu(e,t,n,r){const s=H(),o=s==null?void 0:s.getOptions().normalizeDepth,i=function(u){for(const l in u)if(Object.prototype.hasOwnProperty.call(u,l)){const p=u[l];if(p instanceof Error)return p}}(t),c={__serialized__:Qi(t,o)};if(i)return{exception:{values:[xs(e,i)]},extra:c};const a={exception:{values:[{type:rr(t)?t.constructor.name:r?"UnhandledRejection":"Error",value:Ju(t,{isUnhandledRejection:r})}]},extra:c};if(n){const u=Ts(e,n);u.length&&(a.exception.values[0].stacktrace={frames:u})}return a}function xr(e,t){return{exception:{values:[xs(e,t)]}}}function Ts(e,t){const n=t.stacktrace||t.stack||"",r=function(o){return o&&Bu.test(o.message)?1:0}(t),s=function(o){return typeof o.framesToPop=="number"?o.framesToPop:0}(t);try{return e(n,r,s)}catch{}return[]}const Bu=/Minified React error #\d+;/i;function va(e){return typeof WebAssembly<"u"&&WebAssembly.Exception!==void 0&&e instanceof WebAssembly.Exception}function zu(e){const t=e==null?void 0:e.name;return!t&&va(e)?e.message&&Array.isArray(e.message)&&e.message.length==2?e.message[0]:"WebAssembly.Exception":t}function Wu(e){const t=e==null?void 0:e.message;return va(e)?Array.isArray(e.message)&&e.message.length==2?e.message[1]:"wasm exception":t?t.error&&typeof t.error.message=="string"?t.error.message:t:"No error message"}function ns(e,t,n,r,s){let o;if(Ai(t)&&t.error)return xr(e,t.error);if(zs(t)||Dt(t,"DOMException")){const i=t;if("stack"in t)o=xr(e,t);else{const c=i.name||(zs(i)?"DOMError":"DOMException"),a=i.message?`${c}: ${i.message}`:c;o=rs(e,a,n,r),Ur(o,a)}return"code"in i&&(o.tags={...o.tags,"DOMException.code":`${i.code}`}),o}return gs(t)?xr(e,t):Zt(t)||rr(t)?(o=Hu(e,t,n,s),Tt(o,{synthetic:!0}),o):(o=rs(e,t,n,r),Ur(o,`${t}`),Tt(o,{synthetic:!0}),o)}function rs(e,t,n,r){const s={};if(r&&n){const o=Ts(e,n);o.length&&(s.exception={values:[{value:t,stacktrace:{frames:o}}]}),Tt(s,{synthetic:!0})}if(vs(t)){const{__sentry_template_string__:o,__sentry_template_values__:i}=t;return s.logentry={message:o,params:i},s}return s.message=t,s}function Ju(e,{isUnhandledRejection:t}){const n=function(s,o=40){const i=Object.keys(Ni(s));i.sort();const c=i[0];if(!c)return"[object has no keys]";if(c.length>=o)return Bn(c,o);for(let a=i.length;a>0;a--){const u=i.slice(0,a).join(", ");if(!(u.length>o))return a===i.length?u:Bn(u,o)}return""}(e),r=t?"promise rejection":"exception";return Ai(e)?`Event \`ErrorEvent\` captured as ${r} with message \`${e.message}\``:rr(e)?`Event \`${function(s){try{const o=Object.getPrototypeOf(s);return o?o.constructor.name:void 0}catch{}}(e)}\` (type=${e.type}) captured as ${r}`:`Object captured as ${r} with keys: ${n}`}class Gu extends _u{constructor(t){const n={parentSpanIsAlwaysRootSpan:!0,...t};la(n,"browser",["browser"],j.SENTRY_SDK_SOURCE||"npm"),super(n);const r=this,{sendDefaultPii:s,_experiments:o}=r._options,i=o==null?void 0:o.enableLogs;n.sendClientReports&&j.document&&j.document.addEventListener("visibilitychange",()=>{j.document.visibilityState==="hidden"&&(this._flushOutcomes(),i&&Er(r))}),i&&(r.on("flush",()=>{Er(r)}),r.on("afterCaptureLog",()=>{r._logFlushIdleTimeout&&clearTimeout(r._logFlushIdleTimeout),r._logFlushIdleTimeout=setTimeout(()=>{Er(r)},5e3)})),s&&(r.on("postprocessEvent",Tu),r.on("beforeSendSession",ku))}eventFromException(t,n){return function(r,s,o,i){const c=ns(r,s,(o==null?void 0:o.syntheticException)||void 0,i);return Tt(c),c.level="error",o!=null&&o.event_id&&(c.event_id=o.event_id),pt(c)}(this._options.stackParser,t,n,this._options.attachStacktrace)}eventFromMessage(t,n="info",r){return function(s,o,i="info",c,a){const u=rs(s,o,(c==null?void 0:c.syntheticException)||void 0,a);return u.level=i,c!=null&&c.event_id&&(u.event_id=c.event_id),pt(u)}(this._options.stackParser,t,n,r,this._options.attachStacktrace)}_prepareEvent(t,n,r,s){return t.platform=t.platform||"javascript",super._prepareEvent(t,n,r,s)}}const ks=typeof __SENTRY_DEBUG__>"u"||__SENTRY_DEBUG__,At=(e,t,n,r)=>{let s,o;return i=>{t.value>=0&&(i||r)&&(o=t.value-(s||0),(o||s===void 0)&&(s=t.value,t.delta=o,t.rating=((c,a)=>c>a[1]?"poor":c>a[0]?"needs-improvement":"good")(t.value,n),e(t)))}},N=q,on=(e=!0)=>{var n,r;const t=(r=(n=N.performance)==null?void 0:n.getEntriesByType)==null?void 0:r.call(n,"navigation")[0];if(!e||t&&t.responseStart>0&&t.responseStart<performance.now())return t},yn=()=>{const e=on();return(e==null?void 0:e.activationStart)||0},Ot=(e,t)=>{var s,o;const n=on();let r="navigate";return n&&((s=N.document)!=null&&s.prerendering||yn()>0?r="prerender":(o=N.document)!=null&&o.wasDiscarded?r="restore":n.type&&(r=n.type.replace(/_/g,"-"))),{name:e,value:t===void 0?-1:t,rating:"good",delta:0,entries:[],id:`v4-${Date.now()}-${Math.floor(8999999999999*Math.random())+1e12}`,navigationType:r}},ht=(e,t,n)=>{try{if(PerformanceObserver.supportedEntryTypes.includes(e)){const r=new PerformanceObserver(s=>{Promise.resolve().then(()=>{t(s.getEntries())})});return r.observe(Object.assign({type:e,buffered:!0},n||{})),r}}catch{}},Lt=e=>{const t=n=>{var r;n.type!=="pagehide"&&((r=N.document)==null?void 0:r.visibilityState)!=="hidden"||e(n)};N.document&&(addEventListener("visibilitychange",t,!0),addEventListener("pagehide",t,!0))},cr=e=>{let t=!1;return()=>{t||(e(),t=!0)}};let Gt=-1;const Kn=e=>{N.document.visibilityState==="hidden"&&Gt>-1&&(Gt=e.type==="visibilitychange"?e.timeStamp:0,Ku())},Ku=()=>{removeEventListener("visibilitychange",Kn,!0),removeEventListener("prerenderingchange",Kn,!0)},ur=()=>(N.document&&Gt<0&&(Gt=N.document.visibilityState!=="hidden"||N.document.prerendering?1/0:0,addEventListener("visibilitychange",Kn,!0),addEventListener("prerenderingchange",Kn,!0)),{get firstHiddenTime(){return Gt}}),bn=e=>{var t;(t=N.document)!=null&&t.prerendering?addEventListener("prerenderingchange",()=>e(),!0):e()},Vu=[1800,3e3],Yu=[.1,.25],Xu=(e,t={})=>{((n,r={})=>{bn(()=>{const s=ur(),o=Ot("FCP");let i;const c=ht("paint",a=>{a.forEach(u=>{u.name==="first-contentful-paint"&&(c.disconnect(),u.startTime<s.firstHiddenTime&&(o.value=Math.max(u.startTime-yn(),0),o.entries.push(u),i(!0)))})});c&&(i=At(n,o,Vu,r.reportAllChanges))})})(cr(()=>{const n=Ot("CLS",0);let r,s=0,o=[];const i=a=>{a.forEach(u=>{if(!u.hadRecentInput){const l=o[0],p=o[o.length-1];s&&l&&p&&u.startTime-p.startTime<1e3&&u.startTime-l.startTime<5e3?(s+=u.value,o.push(u)):(s=u.value,o=[u])}}),s>n.value&&(n.value=s,n.entries=o,r())},c=ht("layout-shift",i);c&&(r=At(e,n,Yu,t.reportAllChanges),Lt(()=>{i(c.takeRecords()),r(!0)}),setTimeout(r,0))}))},Zu=[100,300],Qu=(e,t={})=>{bn(()=>{const n=ur(),r=Ot("FID");let s;const o=a=>{a.startTime<n.firstHiddenTime&&(r.value=a.processingStart-a.startTime,r.entries.push(a),s(!0))},i=a=>{a.forEach(o)},c=ht("first-input",i);s=At(e,r,Zu,t.reportAllChanges),c&&Lt(cr(()=>{i(c.takeRecords()),c.disconnect()}))})};let _a=0,Tr=1/0,On=0;const el=e=>{e.forEach(t=>{t.interactionId&&(Tr=Math.min(Tr,t.interactionId),On=Math.max(On,t.interactionId),_a=On?(On-Tr)/7+1:0)})};let ss;const tl=()=>{"interactionCount"in performance||ss||(ss=ht("event",el,{type:"event",buffered:!0,durationThreshold:0}))},Oe=[],kr=new Map,nl=()=>(ss?_a:performance.interactionCount||0)-0,rl=[],sl=e=>{var r;if(rl.forEach(s=>s(e)),!e.interactionId&&e.entryType!=="first-input")return;const t=Oe[Oe.length-1],n=kr.get(e.interactionId);if(n||Oe.length<10||t&&e.duration>t.latency){if(n)e.duration>n.latency?(n.entries=[e],n.latency=e.duration):e.duration===n.latency&&e.startTime===((r=n.entries[0])==null?void 0:r.startTime)&&n.entries.push(e);else{const s={id:e.interactionId,latency:e.duration,entries:[e]};kr.set(s.id,s),Oe.push(s)}Oe.sort((s,o)=>o.latency-s.latency),Oe.length>10&&Oe.splice(10).forEach(s=>kr.delete(s.id))}},ya=e=>{var r;const t=N.requestIdleCallback||N.setTimeout;let n=-1;return e=cr(e),((r=N.document)==null?void 0:r.visibilityState)==="hidden"?e():(n=t(e),Lt(e)),n},ol=[200,500],il=(e,t={})=>{"PerformanceEventTiming"in N&&"interactionId"in PerformanceEventTiming.prototype&&bn(()=>{tl();const n=Ot("INP");let r;const s=i=>{ya(()=>{i.forEach(sl);const c=(()=>{const a=Math.min(Oe.length-1,Math.floor(nl()/50));return Oe[a]})();c&&c.latency!==n.value&&(n.value=c.latency,n.entries=c.entries,r())})},o=ht("event",s,{durationThreshold:t.durationThreshold!=null?t.durationThreshold:40});r=At(e,n,ol,t.reportAllChanges),o&&(o.observe({type:"first-input",buffered:!0}),Lt(()=>{s(o.takeRecords()),r(!0)}))})},al=[2500,4e3],Ho={},cl=(e,t={})=>{bn(()=>{const n=ur(),r=Ot("LCP");let s;const o=c=>{t.reportAllChanges||(c=c.slice(-1)),c.forEach(a=>{a.startTime<n.firstHiddenTime&&(r.value=Math.max(a.startTime-yn(),0),r.entries=[a],s())})},i=ht("largest-contentful-paint",o);if(i){s=At(e,r,al,t.reportAllChanges);const c=cr(()=>{Ho[r.id]||(o(i.takeRecords()),i.disconnect(),Ho[r.id]=!0,s(!0))});["keydown","click"].forEach(a=>{N.document&&addEventListener(a,()=>ya(c),{once:!0,capture:!0})}),Lt(c)}})},ul=[800,1800],os=e=>{var t,n;(t=N.document)!=null&&t.prerendering?bn(()=>os(e)):((n=N.document)==null?void 0:n.readyState)!=="complete"?addEventListener("load",()=>os(e),!0):setTimeout(e,0)},ll=(e,t={})=>{const n=Ot("TTFB"),r=At(e,n,ul,t.reportAllChanges);os(()=>{const s=on();s&&(n.value=Math.max(s.responseStart-yn(),0),n.entries=[s],r(!0))})},Kt={},Vn={};let ba,Sa,wa,Ea,xa;function Ta(e,t=!1){return Vt("cls",e,dl,ba,t)}function zt(e,t){return ka(e,t),Vn[e]||(function(n){const r={};n==="event"&&(r.durationThreshold=0),ht(n,s=>{Mt(n,{entries:s})},r)}(e),Vn[e]=!0),Ia(e,t)}function Mt(e,t){const n=Kt[e];if(n!=null&&n.length)for(const r of n)try{r(t)}catch(s){ks&&x.error(`Error while triggering instrumentation handler.
Type: ${e}
Name: ${je(r)}
Error:`,s)}}function dl(){return Xu(e=>{Mt("cls",{metric:e}),ba=e},{reportAllChanges:!0})}function fl(){return Qu(e=>{Mt("fid",{metric:e}),Sa=e})}function pl(){return cl(e=>{Mt("lcp",{metric:e}),wa=e},{reportAllChanges:!0})}function hl(){return ll(e=>{Mt("ttfb",{metric:e}),Ea=e})}function ml(){return il(e=>{Mt("inp",{metric:e}),xa=e})}function Vt(e,t,n,r,s=!1){let o;return ka(e,t),Vn[e]||(o=n(),Vn[e]=!0),r&&t({metric:r}),Ia(e,t,s?o:void 0)}function ka(e,t){Kt[e]=Kt[e]||[],Kt[e].push(t)}function Ia(e,t,n){return()=>{n&&n();const r=Kt[e];if(!r)return;const s=r.indexOf(t);s!==-1&&r.splice(s,1)}}function Ir(e){return typeof e=="number"&&isFinite(e)}function Re(e,t,n,{...r}){const s=B(e).start_timestamp;return s&&s>t&&typeof e.updateStartTime=="function"&&e.updateStartTime(t),na(e,()=>{const o=_n({startTime:t,...r});return o&&o.end(n),o})}function $a(e){var h;const t=H();if(!t)return;const{name:n,transaction:r,attributes:s,startTime:o}=e,{release:i,environment:c,sendDefaultPii:a}=t.getOptions(),u=t.getIntegrationByName("Replay"),l=u==null?void 0:u.getReplayId(),p=W(),f=p.getUser(),d=f!==void 0?f.email||f.id||f.ip_address:void 0;let m;try{m=p.getScopeData().contexts.profile.profile_id}catch{}return _n({name:n,attributes:{release:i,environment:c,user:d||void 0,profile_id:m||void 0,replay_id:l||void 0,transaction:r,"user_agent.original":(h=N.navigator)==null?void 0:h.userAgent,"client.address":a?"{{auto}}":void 0,...s},startTime:o,experimental:{standalone:!0}})}function Is(){return N.addEventListener&&N.performance}function G(e){return e/1e3}function Pa(e){let t="unknown",n="unknown",r="";for(const s of e){if(s==="/"){[t,n]=e.split("/");break}if(!isNaN(Number(s))){t=r==="h"?"http":r,n=e.split(r)[1];break}r+=s}return r===e&&(t=r),{name:t,version:n}}function gl(){let e,t,n=0;if(!function(){try{return PerformanceObserver.supportedEntryTypes.includes("layout-shift")}catch{return!1}}())return;let r=!1;function s(){r||(r=!0,t&&function(i,c,a){var m;ks&&x.log(`Sending CLS span (${i})`);const u=G((me()||0)+((c==null?void 0:c.startTime)||0)),l=W().getScopeData().transactionName,p=c?dt((m=c.sources[0])==null?void 0:m.node):"Layout shift",f={[X]:"auto.http.browser.cls",[ft]:"ui.webvital.cls",[tn]:(c==null?void 0:c.duration)||0,"sentry.pageload.span_id":a},d=$a({name:p,transaction:l,attributes:f,startTime:u});d&&(d.addEvent("cls",{[or]:"",[ir]:i}),d.end(u))}(n,e,t),o())}const o=Ta(({metric:i})=>{const c=i.entries[i.entries.length-1];c&&(n=i.value,e=c)},!0);Lt(()=>{s()}),setTimeout(()=>{const i=H();if(!i)return;const c=i.on("startNavigationSpan",()=>{s(),c==null||c()}),a=ce();if(a){const u=te(a);B(u).op==="pageload"&&(t=u.spanContext().spanId)}},0)}const vl=2147483647;let se,bt,Bo=0,Z={};function _l({recordClsStandaloneSpans:e}){const t=Is();if(t&&me()){t.mark&&N.performance.mark("sentry-tracing-init");const n=Vt("fid",({metric:i})=>{const c=i.entries[i.entries.length-1];if(!c)return;const a=G(me()),u=G(c.startTime);Z.fid={value:i.value,unit:"millisecond"},Z["mark.fid"]={value:a+u,unit:"second"}},fl,Sa),r=function(i,c=!1){return Vt("lcp",i,pl,wa,c)}(({metric:i})=>{const c=i.entries[i.entries.length-1];c&&(Z.lcp={value:i.value,unit:"millisecond"},se=c)},!0),s=function(i){return Vt("ttfb",i,hl,Ea)}(({metric:i})=>{i.entries[i.entries.length-1]&&(Z.ttfb={value:i.value,unit:"millisecond"})}),o=e?gl():Ta(({metric:i})=>{const c=i.entries[i.entries.length-1];c&&(Z.cls={value:i.value,unit:""},bt=c)},!0);return()=>{n(),r(),s(),o==null||o()}}return()=>{}}function yl(e,t){const n=Is(),r=me();if(!(n!=null&&n.getEntries)||!r)return;const s=G(r),o=n.getEntries(),{op:i,start_timestamp:c}=B(e);if(o.slice(Bo).forEach(a=>{const u=G(a.startTime),l=G(Math.max(0,a.duration));if(!(i==="navigation"&&c&&s+u<c))switch(a.entryType){case"navigation":(function(p,f,d){["unloadEvent","redirect","domContentLoadedEvent","loadEvent","connect"].forEach(m=>{Cn(p,f,m,d)}),Cn(p,f,"secureConnection",d,"TLS/SSL"),Cn(p,f,"fetch",d,"cache"),Cn(p,f,"domainLookup",d,"DNS"),function(m,h,g){const v=g+G(h.requestStart),_=g+G(h.responseEnd),S=g+G(h.responseStart);h.responseEnd&&(Re(m,v,_,{op:"browser.request",name:h.name,attributes:{[X]:"auto.ui.browser.metrics"}}),Re(m,S,_,{op:"browser.response",name:h.name,attributes:{[X]:"auto.ui.browser.metrics"}}))}(p,f,d)})(e,a,s);break;case"mark":case"paint":case"measure":{(function(d,m,h,g,v){const _=on(!1),S=G(_?_.requestStart:0),y=v+Math.max(h,S),b=v+h,T=b+g,w={[X]:"auto.resource.browser.metrics"};if(y!==b&&(w["sentry.browser.measure_happened_before_request"]=!0,w["sentry.browser.measure_start_time"]=y),m.detail)if(typeof m.detail=="object")for(const[L,E]of Object.entries(m.detail))if(E&&Xt(E))w[`sentry.browser.measure.detail.${L}`]=E;else try{w[`sentry.browser.measure.detail.${L}`]=JSON.stringify(E)}catch{}else if(Xt(m.detail))w["sentry.browser.measure.detail"]=m.detail;else try{w["sentry.browser.measure.detail"]=JSON.stringify(m.detail)}catch{}y<=T&&Re(d,y,T,{name:m.name,op:m.entryType,attributes:w})})(e,a,u,l,s);const p=ur(),f=a.startTime<p.firstHiddenTime;a.name==="first-paint"&&f&&(Z.fp={value:a.startTime,unit:"millisecond"}),a.name==="first-contentful-paint"&&f&&(Z.fcp={value:a.startTime,unit:"millisecond"});break}case"resource":(function(p,f,d,m,h,g){if(f.initiatorType==="xmlhttprequest"||f.initiatorType==="fetch")return;const v=xt(d),_={[X]:"auto.resource.browser.metrics"};$r(_,f,"transferSize","http.response_transfer_size"),$r(_,f,"encodedBodySize","http.response_content_length"),$r(_,f,"decodedBodySize","http.decoded_response_content_length");const S=f.deliveryType;S!=null&&(_["http.response_delivery_type"]=S);const y=f.renderBlockingStatus;y&&(_["resource.render_blocking_status"]=y),v.protocol&&(_["url.scheme"]=v.protocol.split(":").pop()),v.host&&(_["server.address"]=v.host),_["url.same_origin"]=d.includes(N.location.origin);const{name:b,version:T}=Pa(f.nextHopProtocol);_["network.protocol.name"]=b,_["network.protocol.version"]=T;const w=g+m,L=w+h;Re(p,w,L,{name:d.replace(N.location.origin,""),op:f.initiatorType?`resource.${f.initiatorType}`:"resource.other",attributes:_})})(e,a,a.name,u,l,s)}}),Bo=Math.max(o.length-1,0),function(a){const u=N.navigator;if(!u)return;const l=u.connection;l&&(l.effectiveType&&a.setAttribute("effectiveConnectionType",l.effectiveType),l.type&&a.setAttribute("connectionType",l.type),Ir(l.rtt)&&(Z["connection.rtt"]={value:l.rtt,unit:"millisecond"})),Ir(u.deviceMemory)&&a.setAttribute("deviceMemory",`${u.deviceMemory} GB`),Ir(u.hardwareConcurrency)&&a.setAttribute("hardwareConcurrency",String(u.hardwareConcurrency))}(e),i==="pageload"){(function(u){const l=on(!1);if(!l)return;const{responseStart:p,requestStart:f}=l;f<=p&&(u["ttfb.requestTime"]={value:p-f,unit:"millisecond"})})(Z);const a=Z["mark.fid"];a&&Z.fid&&(Re(e,a.value,a.value+G(Z.fid.value),{name:"first input delay",op:"ui.action",attributes:{[X]:"auto.ui.browser.metrics"}}),delete Z["mark.fid"]),"fcp"in Z&&t.recordClsOnPageloadSpan||delete Z.cls,Object.entries(Z).forEach(([u,l])=>{(function(p,f,d,m=ce()){const h=m&&te(m);h&&(O&&x.log(`[Measurement] Setting measurement on root span: ${p} = ${f} ${d}`),h.addEvent(p,{[ir]:f,[or]:d}))})(u,l.value,l.unit)}),e.setAttribute("performance.timeOrigin",s),e.setAttribute("performance.activationStart",yn()),function(u){se&&(se.element&&u.setAttribute("lcp.element",dt(se.element)),se.id&&u.setAttribute("lcp.id",se.id),se.url&&u.setAttribute("lcp.url",se.url.trim().slice(0,200)),se.loadTime!=null&&u.setAttribute("lcp.loadTime",se.loadTime),se.renderTime!=null&&u.setAttribute("lcp.renderTime",se.renderTime),u.setAttribute("lcp.size",se.size)),bt!=null&&bt.sources&&bt.sources.forEach((l,p)=>u.setAttribute(`cls.source.${p+1}`,dt(l.node)))}(e)}se=void 0,bt=void 0,Z={}}function Cn(e,t,n,r,s=n){const o=function(a){return a==="secureConnection"?"connectEnd":a==="fetch"?"domainLookupStart":`${a}End`}(n),i=t[o],c=t[`${n}Start`];c&&i&&Re(e,r+G(c),r+G(i),{op:`browser.${s}`,name:t.name,attributes:{[X]:"auto.ui.browser.metrics",...n==="redirect"&&t.redirectCount!=null?{"http.redirect_count":t.redirectCount}:{}}})}function $r(e,t,n,r){const s=t[n];s!=null&&s<vl&&(e[r]=s)}const bl=1e3;let zo,Pr,Ar,Rn;function Sl(){if(!N.document)return;const e=ve.bind(null,"dom"),t=Wo(e,!0);N.document.addEventListener("click",t,!1),N.document.addEventListener("keypress",t,!1),["EventTarget","Node"].forEach(n=>{var o,i;const r=N,s=(o=r[n])==null?void 0:o.prototype;(i=s==null?void 0:s.hasOwnProperty)!=null&&i.call(s,"addEventListener")&&(ue(s,"addEventListener",function(c){return function(a,u,l){if(a==="click"||a=="keypress")try{const p=this.__sentry_instrumentation_handlers__=this.__sentry_instrumentation_handlers__||{},f=p[a]=p[a]||{refCount:0};if(!f.handler){const d=Wo(e);f.handler=d,c.call(this,a,d,l)}f.refCount++}catch{}return c.call(this,a,u,l)}}),ue(s,"removeEventListener",function(c){return function(a,u,l){if(a==="click"||a=="keypress")try{const p=this.__sentry_instrumentation_handlers__||{},f=p[a];f&&(f.refCount--,f.refCount<=0&&(c.call(this,a,f.handler,l),f.handler=void 0,delete p[a]),Object.keys(p).length===0&&delete this.__sentry_instrumentation_handlers__)}catch{}return c.call(this,a,u,l)}}))})}function Wo(e,t=!1){return n=>{if(!n||n._sentryCaptured)return;const r=function(o){try{return o.target}catch{return null}}(n);if(function(o,i){return o==="keypress"&&(!(i!=null&&i.tagName)||i.tagName!=="INPUT"&&i.tagName!=="TEXTAREA"&&!i.isContentEditable)}(n.type,r))return;de(n,"_sentryCaptured",!0),r&&!r._sentryId&&de(r,"_sentryId",ge());const s=n.type==="keypress"?"input":n.type;(function(o){if(o.type!==Pr)return!1;try{if(!o.target||o.target._sentryId!==Ar)return!1}catch{}return!0})(n)||(e({event:n,name:s,global:t}),Pr=n.type,Ar=r?r._sentryId:void 0),clearTimeout(zo),zo=N.setTimeout(()=>{Ar=void 0,Pr=void 0},bl)}}function $s(e){const t="history";We(t,e),Je(t,wl)}function wl(){function e(t){return function(...n){const r=n.length>2?n[2]:void 0;if(r){const s=Rn,o=function(i){try{return new URL(i,N.location.origin).toString()}catch{return i}}(String(r));if(Rn=o,s===o)return t.apply(this,n);ve("history",{from:s,to:o})}return t.apply(this,n)}}N.addEventListener("popstate",()=>{const t=N.location.href,n=Rn;Rn=t,n!==t&&ve("history",{from:n,to:t})}),"history"in sn&&sn.history&&(ue(N.history,"pushState",e),ue(N.history,"replaceState",e))}const qn={};function Jo(e){qn[e]=void 0}const wt="__sentry_xhr_v3__";function Aa(e){We("xhr",e),Je("xhr",El)}function El(){if(!N.XMLHttpRequest)return;const e=XMLHttpRequest.prototype;e.open=new Proxy(e.open,{apply(t,n,r){const s=new Error,o=1e3*ne(),i=De(r[0])?r[0].toUpperCase():void 0,c=function(u){if(De(u))return u;try{return u.toString()}catch{}}(r[1]);if(!i||!c)return t.apply(n,r);n[wt]={method:i,url:c,request_headers:{}},i==="POST"&&c.match(/sentry_key/)&&(n.__sentry_own_request__=!0);const a=()=>{const u=n[wt];if(u&&n.readyState===4){try{u.status_code=n.status}catch{}ve("xhr",{endTimestamp:1e3*ne(),startTimestamp:o,xhr:n,virtualError:s})}};return"onreadystatechange"in n&&typeof n.onreadystatechange=="function"?n.onreadystatechange=new Proxy(n.onreadystatechange,{apply:(u,l,p)=>(a(),u.apply(l,p))}):n.addEventListener("readystatechange",a),n.setRequestHeader=new Proxy(n.setRequestHeader,{apply(u,l,p){const[f,d]=p,m=l[wt];return m&&De(f)&&De(d)&&(m.request_headers[f.toLowerCase()]=d),u.apply(l,p)}}),t.apply(n,r)}}),e.send=new Proxy(e.send,{apply(t,n,r){const s=n[wt];return s?(r[0]!==void 0&&(s.body=r[0]),ve("xhr",{startTimestamp:1e3*ne(),xhr:n}),t.apply(n,r)):t.apply(n,r)}})}const Or=[],Fn=new Map;function xl(){if(Is()&&me()){const e=Vt("inp",({metric:t})=>{if(t.value==null)return;const n=t.entries.find(f=>f.duration===t.value&&Go[f.name]);if(!n)return;const{interactionId:r}=n,s=Go[n.name],o=G(me()+n.startTime),i=G(t.value),c=ce(),a=c?te(c):void 0,u=(r!=null?Fn.get(r):void 0)||a,l=u?B(u).description:W().getScopeData().transactionName,p=$a({name:dt(n.target),transaction:l,attributes:{[X]:"auto.http.browser.inp",[ft]:`ui.interaction.${s}`,[tn]:n.duration},startTime:o});p&&(p.addEvent("inp",{[or]:"millisecond",[ir]:t.value}),p.end(o+i))},ml,xa);return()=>{e()}}return()=>{}}const Go={click:"click",pointerdown:"click",pointerup:"click",mousedown:"click",mouseup:"click",touchstart:"click",touchend:"click",mouseover:"hover",mouseout:"hover",mouseenter:"hover",mouseleave:"hover",pointerover:"hover",pointerout:"hover",pointerenter:"hover",pointerleave:"hover",dragstart:"drag",dragend:"drag",drag:"drag",dragenter:"drag",dragleave:"drag",dragover:"drag",drop:"drag",keydown:"press",keyup:"press",keypress:"press",input:"press"};function Tl(e,t=function(n){const r=qn[n];if(r)return r;let s=N[n];if(Zr(s))return qn[n]=s.bind(N);const o=N.document;if(o&&typeof o.createElement=="function")try{const i=o.createElement("iframe");i.hidden=!0,o.head.appendChild(i);const c=i.contentWindow;c!=null&&c[n]&&(s=c[n]),o.head.removeChild(i)}catch(i){ks&&x.warn(`Could not create sandbox iframe for ${n} check, bailing to window.${n}: `,i)}return s&&(qn[n]=s.bind(N))}("fetch")){let n=0,r=0;return xu(e,function(s){const o=s.body.length;n+=o,r++;const i={body:s.body,method:"POST",referrerPolicy:"strict-origin",headers:e.headers,keepalive:n<=6e4&&r<15,...e.fetchOptions};if(!t)return Jo("fetch"),Gn("No fetch implementation available");try{return t(e.url,i).then(c=>(n-=o,r--,{statusCode:c.status,headers:{"x-sentry-rate-limits":c.headers.get("X-Sentry-Rate-Limits"),"retry-after":c.headers.get("Retry-After")}}))}catch(c){return Jo("fetch"),n-=o,r--,Gn(c)}})}function Cr(e,t,n,r){const s={filename:e,function:t==="<anonymous>"?ct:t,in_app:!0};return n!==void 0&&(s.lineno=n),r!==void 0&&(s.colno=r),s}const kl=/^\s*at (\S+?)(?::(\d+))(?::(\d+))\s*$/i,Il=/^\s*at (?:(.+?\)(?: \[.+\])?|.*?) ?\((?:address at )?)?(?:async )?((?:<anonymous>|[-a-z]+:|.*bundle|\/)?.*?)(?::(\d+))?(?::(\d+))?\)?\s*$/i,$l=/\((\S*)(?::(\d+))(?::(\d+))\)/,Pl=/^\s*(.*?)(?:\((.*?)\))?(?:^|@)?((?:[-a-z]+)?:\/.*?|\[native code\]|[^@]*(?:bundle|\d+\.js)|\/[\w\-. /=]+)(?::(\d+))?(?::(\d+))?\s*$/i,Al=/(\S+) line (\d+)(?: > eval line \d+)* > eval/i,Ol=Gi([30,e=>{const t=kl.exec(e);if(t){const[,r,s,o]=t;return Cr(r,ct,+s,+o)}const n=Il.exec(e);if(n){if(n[2]&&n[2].indexOf("eval")===0){const o=$l.exec(n[2]);o&&(n[2]=o[1],n[3]=o[2],n[4]=o[3])}const[r,s]=Ko(n[1]||ct,n[2]);return Cr(s,r,n[3]?+n[3]:void 0,n[4]?+n[4]:void 0)}}],[50,e=>{const t=Pl.exec(e);if(t){if(t[3]&&t[3].indexOf(" > eval")>-1){const s=Al.exec(t[3]);s&&(t[1]=t[1]||"eval",t[3]=s[1],t[4]=s[2],t[5]="")}let n=t[3],r=t[1]||ct;return[r,n]=Ko(r,n),Cr(n,r,t[4]?+t[4]:void 0,t[5]?+t[5]:void 0)}}]),Ko=(e,t)=>{const n=e.indexOf("safari-extension")!==-1,r=e.indexOf("safari-web-extension")!==-1;return n||r?[e.indexOf("@")!==-1?e.split("@")[0]:ct,n?`safari-extension:${t}`:`safari-web-extension:${t}`]:[e,t]},he=typeof __SENTRY_DEBUG__>"u"||__SENTRY_DEBUG__,Vo=1024,Cl=(e={})=>{const t={console:!0,dom:!0,fetch:!0,history:!0,sentry:!0,xhr:!0,...e};return{name:"Breadcrumbs",setup(n){var r;t.console&&function(s){const o="console";We(o,s),Je(o,Ru)}(function(s){return function(o){if(H()!==s)return;const i={category:"console",data:{arguments:o.args,logger:"console"},level:Du(o.level),message:Ws(o.args," ")};if(o.level==="assert"){if(o.args[0]!==!1)return;i.message=`Assertion failed: ${Ws(o.args.slice(1)," ")||"console.assert"}`,i.data.arguments=o.args.slice(1)}et(i,{input:o.args,level:o.level})}}(n)),t.dom&&(r=function(s,o){return function(i){if(H()!==s)return;let c,a,u=typeof o=="object"?o.serializeAttribute:void 0,l=typeof o=="object"&&typeof o.maxStringLength=="number"?o.maxStringLength:void 0;l&&l>Vo&&(he&&x.warn(`\`dom.maxStringLength\` cannot exceed 1024, but a value of ${l} was configured. Sentry will use 1024 instead.`),l=Vo),typeof u=="string"&&(u=[u]);try{const f=i.event,d=function(m){return!!m&&!!m.target}(f)?f.target:f;c=dt(d,{keyAttrs:u,maxStringLength:l}),a=Ri(d)}catch{c="<unknown>"}if(c.length===0)return;const p={category:`ui.${i.name}`,message:c};a&&(p.data={"ui.component_name":a}),et(p,{event:i.event,name:i.name,global:i.global})}}(n,t.dom),We("dom",r),Je("dom",Sl)),t.xhr&&Aa(function(s){return function(o){if(H()!==s)return;const{startTimestamp:i,endTimestamp:c}=o,a=o.xhr[wt];if(!i||!c||!a)return;const{method:u,url:l,status_code:p,body:f}=a,d={method:u,url:l,status_code:p},m={xhr:o.xhr,input:f,startTimestamp:i,endTimestamp:c},h={category:"xhr",data:d,type:"http",level:qo(p)};s.emit("beforeOutgoingRequestBreadcrumb",h,m),et(h,m)}}(n)),t.fetch&&ma(function(s){return function(o){if(H()!==s)return;const{startTimestamp:i,endTimestamp:c}=o;if(c&&(!o.fetchData.url.match(/sentry_key/)||o.fetchData.method!=="POST"))if(o.fetchData.method,o.fetchData.url,o.error){const a=o.fetchData,u={data:o.error,input:o.args,startTimestamp:i,endTimestamp:c},l={category:"fetch",data:a,level:"error",type:"http"};s.emit("beforeOutgoingRequestBreadcrumb",l,u),et(l,u)}else{const a=o.response,u={...o.fetchData,status_code:a==null?void 0:a.status};o.fetchData.request_body_size,o.fetchData.response_body_size;const l={input:o.args,response:a,startTimestamp:i,endTimestamp:c},p={category:"fetch",data:u,type:"http",level:qo(u.status_code)};s.emit("beforeOutgoingRequestBreadcrumb",p,l),et(p,l)}}}(n)),t.history&&$s(function(s){return function(o){if(H()!==s)return;let i=o.from,c=o.to;const a=xt(j.location.href);let u=i?xt(i):void 0;const l=xt(c);u!=null&&u.path||(u=a),a.protocol===l.protocol&&a.host===l.host&&(c=l.relative),a.protocol===u.protocol&&a.host===u.host&&(i=u.relative),et({category:"navigation",data:{from:i,to:c}})}}(n)),t.sentry&&n.on("beforeSendEvent",function(s){return function(o){H()===s&&et({category:"sentry."+(o.type==="transaction"?"transaction":"event"),event_id:o.event_id,level:o.level,message:tt(o)},{event:o})}}(n))}}},Rl=["EventTarget","Window","Node","ApplicationCache","AudioTrackList","BroadcastChannel","ChannelMergerNode","CryptoOperation","EventSource","FileReader","HTMLUnknownElement","IDBDatabase","IDBRequest","IDBTransaction","KeyOperation","MediaController","MessagePort","ModalWindow","Notification","SVGElementInstance","Screen","SharedWorker","TextTrack","TextTrackCue","TextTrackList","WebSocket","WebSocketWorker","Worker","XMLHttpRequest","XMLHttpRequestEventTarget","XMLHttpRequestUpload"],Dl=(e={})=>{const t={XMLHttpRequest:!0,eventTarget:!0,requestAnimationFrame:!0,setInterval:!0,setTimeout:!0,...e};return{name:"BrowserApiErrors",setupOnce(){t.setTimeout&&ue(j,"setTimeout",Yo),t.setInterval&&ue(j,"setInterval",Yo),t.requestAnimationFrame&&ue(j,"requestAnimationFrame",Nl),t.XMLHttpRequest&&"XMLHttpRequest"in j&&ue(XMLHttpRequest.prototype,"send",Ll);const n=t.eventTarget;n&&(Array.isArray(n)?n:Rl).forEach(Ml)}}};function Yo(e){return function(...t){const n=t[0];return t[0]=Pt(n,{mechanism:{data:{function:je(e)},handled:!1,type:"instrument"}}),e.apply(this,t)}}function Nl(e){return function(t){return e.apply(this,[Pt(t,{mechanism:{data:{function:"requestAnimationFrame",handler:je(e)},handled:!1,type:"instrument"}})])}}function Ll(e){return function(...t){const n=this;return["onload","onerror","onprogress","onreadystatechange"].forEach(r=>{r in n&&typeof n[r]=="function"&&ue(n,r,function(s){const o={mechanism:{data:{function:r,handler:je(s)},handled:!1,type:"instrument"}},i=ys(s);return i&&(o.mechanism.data.handler=je(i)),Pt(s,o)})}),e.apply(this,t)}}function Ml(e){var r,s;const t=j,n=(r=t[e])==null?void 0:r.prototype;(s=n==null?void 0:n.hasOwnProperty)!=null&&s.call(n,"addEventListener")&&(ue(n,"addEventListener",function(o){return function(i,c,a){try{typeof c.handleEvent=="function"&&(c.handleEvent=Pt(c.handleEvent,{mechanism:{data:{function:"handleEvent",handler:je(c),target:e},handled:!1,type:"instrument"}}))}catch{}return o.apply(this,[i,Pt(c,{mechanism:{data:{function:"addEventListener",handler:je(c),target:e},handled:!1,type:"instrument"}}),a])}}),ue(n,"removeEventListener",function(o){return function(i,c,a){try{const u=c.__sentry_wrapped__;u&&o.call(this,i,u,a)}catch{}return o.call(this,i,c,a)}}))}const jl=()=>({name:"BrowserSession",setupOnce(){j.document!==void 0?(So({ignoreDuration:!0}),wo(),$s(({from:e,to:t})=>{e!==void 0&&e!==t&&(So({ignoreDuration:!0}),wo())})):he&&x.warn("Using the `browserSessionIntegration` in non-browser environments is not supported.")}}),ql=(e={})=>{const t={onerror:!0,onunhandledrejection:!0,...e};return{name:"GlobalHandlers",setupOnce(){Error.stackTraceLimit=50},setup(n){t.onerror&&(function(r){Ki(s=>{const{stackParser:o,attachStacktrace:i}=Zo();if(H()!==r||Uo())return;const{msg:c,url:a,line:u,column:l,error:p}=s,f=function(d,m,h,g){const v=d.exception=d.exception||{},_=v.values=v.values||[],S=_[0]=_[0]||{},y=S.stacktrace=S.stacktrace||{},b=y.frames=y.frames||[],T=g,w=h,L=De(m)&&m.length>0?m:hn();return b.length===0&&b.push({colno:T,filename:L,function:ct,in_app:!0,lineno:w}),d}(ns(o,p||c,void 0,i,!1),a,u,l);f.level="error",bo(f,{originalException:p,mechanism:{handled:!1,type:"onerror"}})})}(n),Xo("onerror")),t.onunhandledrejection&&(function(r){Vi(s=>{const{stackParser:o,attachStacktrace:i}=Zo();if(H()!==r||Uo())return;const c=function(u){if(Xt(u))return u;try{if("reason"in u)return u.reason;if("detail"in u&&"reason"in u.detail)return u.detail.reason}catch{}return u}(s),a=Xt(c)?{exception:{values:[{type:"UnhandledRejection",value:`Non-Error promise rejection captured with value: ${String(c)}`}]}}:ns(o,c,void 0,i,!0);a.level="error",bo(a,{originalException:c,mechanism:{handled:!1,type:"onunhandledrejection"}})})}(n),Xo("onunhandledrejection"))}}};function Xo(e){he&&x.log(`Global Handler attached: ${e}`)}function Zo(){const e=H();return(e==null?void 0:e.getOptions())||{stackParser:()=>[],attachStacktrace:!1}}const Fl=()=>({name:"HttpContext",preprocessEvent(e){var r;if(!j.navigator&&!j.location&&!j.document)return;const t=ts(),n={...t.headers,...(r=e.request)==null?void 0:r.headers};e.request={...t,...e.request,headers:n}}}),Ul=(e={})=>{const t=e.limit||5,n=e.key||"cause";return{name:"LinkedErrors",preprocessEvent(r,s,o){Cu(xs,o.getOptions().stackParser,n,t,r,s)}}};function Hl(e){const t={};for(const n of Object.getOwnPropertyNames(e)){const r=n;e[r]!==void 0&&(t[r]=e[r])}return t}function Bl(e={}){const t=function(s={}){var o;return{defaultIntegrations:[Ou(),$u(),Dl(),Cl(),ql(),Ul(),Nu(),Fl(),jl()],release:typeof __SENTRY_RELEASE__=="string"?__SENTRY_RELEASE__:(o=j.SENTRY_RELEASE)==null?void 0:o.id,sendClientReports:!0,...Hl(s)}}(e);if(!t.skipBrowserExtensionCheck&&function(){var l;const s=j.window!==void 0&&j;if(!s)return!1;const o=s[s.chrome?"chrome":"browser"],i=(l=o==null?void 0:o.runtime)==null?void 0:l.id,c=hn()||"",a=!!i&&j===j.top&&["chrome-extension:","moz-extension:","ms-browser-extension:","safari-web-extension:"].some(p=>c.startsWith(`${p}//`)),u=s.nw!==void 0;return!!i&&!a&&!u}())return void(he&&vt(()=>{console.error("[Sentry] You cannot run Sentry this way in a browser extension, check: https://docs.sentry.io/platforms/javascript/best-practices/browser-extensions/")}));he&&!ha()&&x.warn("No Fetch API detected. The Sentry SDK requires a Fetch API compatible environment to send events. Please add a Fetch API polyfill.");const n={...t,stackParser:(r=t.stackParser||Ol,Array.isArray(r)?Gi(...r):r),integrations:vu(t),transport:t.transport||Tl};var r;return yu(Gu,n)}const Qo=new WeakMap,Rr=new Map,Oa={traceFetch:!0,traceXHR:!0,enableHTTPTimings:!0,trackFetchStreamPerformance:!1};function zl(e,t){const{traceFetch:n,traceXHR:r,trackFetchStreamPerformance:s,shouldCreateSpanForRequest:o,enableHTTPTimings:i,tracePropagationTargets:c,onRequestSpanStart:a}={...Oa,...t},u=typeof o=="function"?o:f=>!0,l=f=>function(d,m){const h=hn();if(h){let g,v;try{g=new URL(d,h),v=new URL(h).origin}catch{return!1}const _=g.origin===v;return m?rt(g.toString(),m)||_&&rt(g.pathname,m):_}{const g=!!d.match(/^\/(?!\/)/);return m?rt(d,m):g}}(f,c),p={};n&&(e.addEventProcessor(f=>(f.type==="transaction"&&f.spans&&f.spans.forEach(d=>{if(d.op==="http.client"){const m=Rr.get(d.span_id);m&&(d.timestamp=m/1e3,Rr.delete(d.span_id))}}),f)),s&&function(f){const d="fetch-body-resolved";We(d,f),Je(d,()=>ga(Fu))}(f=>{if(f.response){const d=Qo.get(f.response);d&&f.endTimestamp&&Rr.set(d,f.endTimestamp)}}),ma(f=>{const d=ju(f,u,l,p);if(f.response&&f.fetchData.__span&&Qo.set(f.response,f.fetchData.__span),d){const m=ti(f.fetchData.url),h=m?xt(m).host:void 0;d.setAttributes({"http.url":m,"server.address":h}),i&&ei(d),a==null||a(d,{headers:f.headers})}})),r&&Aa(f=>{var m;const d=function(h,g,v,_){const S=h.xhr,y=S==null?void 0:S[wt];if(!S||S.__sentry_own_request__||!y)return;const{url:b,method:T}=y,w=Ge()&&g(b);if(h.endTimestamp&&w){const P=S.__sentry_xhr_span_id__;if(!P)return;const I=_[P];return void(I&&y.status_code!==void 0&&(Fi(I,y.status_code),I.end(),delete _[P]))}const L=ti(b),E=xt(L||b),R=(A=b,A.split(/[?#]/,1)[0]),D=!!ce(),k=w&&D?_n({name:`${T} ${R}`,attributes:{url:b,type:"xhr","http.method":T,"http.url":L,"server.address":E==null?void 0:E.host,[X]:"auto.http.browser",[ft]:"http.client",...(E==null?void 0:E.search)&&{"http.query":E==null?void 0:E.search},...(E==null?void 0:E.hash)&&{"http.fragment":E==null?void 0:E.hash}}}):new ut;var A;S.__sentry_xhr_span_id__=k.spanContext().spanId,_[S.__sentry_xhr_span_id__]=k,v(b)&&function(P,I){const{"sentry-trace":C,baggage:U}=da({span:I});C&&function(re,J,Se){var Bs;const ie=(Bs=re.__sentry_xhr_v3__)==null?void 0:Bs.request_headers;if(!(ie!=null&&ie["sentry-trace"]))try{if(re.setRequestHeader("sentry-trace",J),Se){const gr=ie==null?void 0:ie.baggage;gr&&gr.split(",").some(Rc=>Rc.trim().startsWith("sentry-"))||re.setRequestHeader("baggage",Se)}}catch{}}(P,C,U)}(S,Ge()&&D?k:void 0);const $=H();return $&&$.emit("beforeOutgoingRequestSpan",k,h),k}(f,u,l,p);if(d){let h;i&&ei(d);try{h=new Headers((m=f.xhr.__sentry_xhr_v3__)==null?void 0:m.request_headers)}catch{}a==null||a(d,{headers:h})}})}function ei(e){const{url:t}=B(e).data;if(!t||typeof t!="string")return;const n=zt("resource",({entries:r})=>{r.forEach(s=>{(function(o){return o.entryType==="resource"&&"initiatorType"in o&&typeof o.nextHopProtocol=="string"&&(o.initiatorType==="fetch"||o.initiatorType==="xmlhttprequest")})(s)&&s.name.endsWith(t)&&(function(o){const{name:i,version:c}=Pa(o.nextHopProtocol),a=[];return a.push(["network.protocol.version",c],["network.protocol.name",i]),me()?[...a,["http.request.redirect_start",we(o.redirectStart)],["http.request.fetch_start",we(o.fetchStart)],["http.request.domain_lookup_start",we(o.domainLookupStart)],["http.request.domain_lookup_end",we(o.domainLookupEnd)],["http.request.connect_start",we(o.connectStart)],["http.request.secure_connection_start",we(o.secureConnectionStart)],["http.request.connection_end",we(o.connectEnd)],["http.request.request_start",we(o.requestStart)],["http.request.response_start",we(o.responseStart)],["http.request.response_end",we(o.responseEnd)]]:a}(s).forEach(o=>e.setAttribute(...o)),setTimeout(n))})})}function we(e=0){return((me()||performance.timeOrigin)+e)/1e3}function ti(e){try{return new URL(e,j.location.origin).href}catch{return}}const Wl=3600,ni="sentry_previous_trace",Jl="sentry.previous_trace";function Gl(e,{linkPreviousTrace:t,consistentTraceSampling:n}){const r=t==="session-storage";let s=r?function(){var i;try{const c=(i=j.sessionStorage)==null?void 0:i.getItem(ni);return JSON.parse(c)}catch{return}}():void 0;e.on("spanStart",i=>{if(te(i)!==i)return;const c=W().getPropagationContext();s=function(a,u,l){const p=B(u);function f(){var h,g;try{return Number((h=l.dsc)==null?void 0:h.sample_rate)??Number((g=p.data)==null?void 0:g[bs])}catch{return 0}}const d={spanContext:u.spanContext(),startTimestamp:p.start_timestamp,sampleRate:f(),sampleRand:l.sampleRand};if(!a)return d;const m=a.spanContext;return m.traceId===p.trace_id?a:(Date.now()/1e3-a.startTimestamp<=Wl&&(he&&x.info(`Adding previous_trace ${m} link to span ${{op:p.op,...u.spanContext()}}`),u.addLink({context:m,attributes:{[zc]:"previous_trace"}}),u.setAttribute(Jl,`${m.traceId}-${m.spanId}-${Dr(m)?1:0}`)),d)}(s,i,c),r&&function(a){try{j.sessionStorage.setItem(ni,JSON.stringify(a))}catch(u){he&&x.warn("Could not store previous trace in sessionStorage",u)}}(s)});let o=!0;n&&e.on("beforeSampling",i=>{if(!s)return;const c=W(),a=c.getPropagationContext();o&&a.parentSpanId?o=!1:(c.setPropagationContext({...a,dsc:{...a.dsc,sample_rate:String(s.sampleRate),sampled:String(Dr(s.spanContext))},sampleRand:s.sampleRand}),i.parentSampled=Dr(s.spanContext),i.parentSampleRate=s.sampleRate,i.spanAttributes={...i.spanAttributes,[ji]:s.sampleRate})})}function Dr(e){return e.traceFlags===1}const Kl={...jn,instrumentNavigation:!0,instrumentPageLoad:!0,markBackgroundSpan:!0,enableLongTask:!0,enableLongAnimationFrame:!0,enableInp:!0,linkPreviousTrace:"in-memory",consistentTraceSampling:!1,_experiments:{},...Oa};let ri=!1;const Vl=(e={})=>{ri&&vt(()=>{console.warn("Multiple browserTracingIntegration instances are not supported.")}),ri=!0;const t=j.document;co||(co=!0,Ki(Jr),Vi(Jr));const{enableInp:n,enableLongTask:r,enableLongAnimationFrame:s,_experiments:{enableInteractions:o,enableStandaloneClsSpans:i},beforeStartSpan:c,idleTimeout:a,finalTimeout:u,childSpanTimeout:l,markBackgroundSpan:p,traceFetch:f,traceXHR:d,trackFetchStreamPerformance:m,shouldCreateSpanForRequest:h,enableHTTPTimings:g,instrumentPageLoad:v,instrumentNavigation:_,linkPreviousTrace:S,consistentTraceSampling:y,onRequestSpanStart:b}={...Kl,...e},T=_l({recordClsStandaloneSpans:i||!1});n&&xl(),s&&q.PerformanceObserver&&PerformanceObserver.supportedEntryTypes&&PerformanceObserver.supportedEntryTypes.includes("long-animation-frame")?new PerformanceObserver(E=>{const R=ce();if(R)for(const D of E.getEntries()){if(!D.scripts[0])continue;const k=G(me()+D.startTime),{start_timestamp:A,op:$}=B(R);if($==="navigation"&&A&&k<A)continue;const P=G(D.duration),I={[X]:"auto.ui.browser.metrics"},C=D.scripts[0],{invoker:U,invokerType:re,sourceURL:J,sourceFunctionName:Se,sourceCharPosition:ie}=C;I["browser.script.invoker"]=U,I["browser.script.invoker_type"]=re,J&&(I["code.filepath"]=J),Se&&(I["code.function"]=Se),ie!==-1&&(I["browser.script.source_char_position"]=ie),Re(R,k,k+P,{name:"Main UI thread blocked",op:"ui.long-animation-frame",attributes:I})}}).observe({type:"long-animation-frame",buffered:!0}):r&&zt("longtask",({entries:E})=>{const R=ce();if(!R)return;const{op:D,start_timestamp:k}=B(R);for(const A of E){const $=G(me()+A.startTime),P=G(A.duration);D==="navigation"&&k&&$<k||Re(R,$,$+P,{name:"Main UI thread blocked",op:"ui.long-task",attributes:{[X]:"auto.ui.browser.metrics"}})}}),o&&zt("event",({entries:E})=>{const R=ce();if(R){for(const D of E)if(D.name==="click"){const k=G(me()+D.startTime),A=G(D.duration),$={name:dt(D.target),op:`ui.interaction.${D.name}`,startTime:k,attributes:{[X]:"auto.ui.browser.metrics"}},P=Ri(D.target);P&&($.attributes["ui.component_name"]=P),Re(R,k,k+A,$)}}});const w={name:void 0,source:void 0};function L(E,R){const D=R.op==="pageload",k=c?c(R):R,A=k.attributes||{};R.name!==k.name&&(A[Ce]="custom",k.attributes=A),w.name=k.name,w.source=A[Ce];const $=vo(k,{idleTimeout:a,finalTimeout:u,childSpanTimeout:l,disableAutoFinish:D,beforeSpanEnd:I=>{T(),yl(I,{recordClsOnPageloadSpan:!i}),oi(E,void 0);const C=W(),U=C.getPropagationContext();C.setPropagationContext({...U,traceId:$.spanContext().traceId,sampled:it($),dsc:Ke(I)})}});function P(){t&&["interactive","complete"].includes(t.readyState)&&E.emit("idleSpanEnableAutoFinish",$)}oi(E,$),D&&t&&(t.addEventListener("readystatechange",()=>{P()}),P())}return{name:"BrowserTracing",afterAllSetup(E){let R=hn();function D(){const k=Dn(E);k&&!B(k).timestamp&&(he&&x.log(`[Tracing] Finishing current active span with op: ${B(k).op}`),k.setAttribute(Wn,"cancelled"),k.end())}if(E.on("startNavigationSpan",k=>{if(H()!==E)return;D(),Xe().setPropagationContext({traceId:ze(),sampleRand:Math.random()});const A=W();A.setPropagationContext({traceId:ze(),sampleRand:Math.random()}),A.setSDKProcessingMetadata({normalizedRequest:void 0}),L(E,{op:"navigation",...k})}),E.on("startPageLoadSpan",(k,A={})=>{if(H()!==E)return;D();const $=Vc(A.sentryTrace||si("sentry-trace"),A.baggage||si("baggage")),P=W();P.setPropagationContext($),P.setSDKProcessingMetadata({normalizedRequest:ts()}),L(E,{op:"pageload",...k})}),S!=="off"&&Gl(E,{linkPreviousTrace:S,consistentTraceSampling:y}),j.location){if(v){const k=me();(function(A,$,P){A.emit("startPageLoadSpan",$,P),W().setTransactionName($.name),Dn(A)})(E,{name:j.location.pathname,startTime:k?k/1e3:void 0,attributes:{[Ce]:"url",[X]:"auto.pageload.browser"}})}_&&$s(({to:k,from:A})=>{if(A===void 0&&(R==null?void 0:R.indexOf(k))!==-1)return void(R=void 0);R=void 0;const $=pa(k);(function(P,I){P.emit("startNavigationSpan",I),W().setTransactionName(I.name),Dn(P)})(E,{name:($==null?void 0:$.pathname)||j.location.pathname,attributes:{[Ce]:"url",[X]:"auto.navigation.browser"}}),W().setSDKProcessingMetadata({normalizedRequest:{...ts(),url:k}})})}p&&(j.document?j.document.addEventListener("visibilitychange",()=>{const k=ce();if(!k)return;const A=te(k);if(j.document.hidden&&A){const $="cancelled",{op:P,status:I}=B(A);he&&x.log(`[Tracing] Transaction: ${$} -> since tab moved to the background, op: ${P}`),I||A.setStatus({code:Y,message:$}),A.setAttribute("sentry.cancellation_reason","document.hidden"),A.end()}}):he&&x.warn("[Tracing] Could not set up background tab detection due to lack of global document")),o&&function(k,A,$,P,I){const C=j.document;let U;const re=()=>{const J="ui.action.click",Se=Dn(k);if(Se){const ie=B(Se).op;if(["navigation","pageload"].includes(ie))return void(he&&x.warn(`[Tracing] Did not create ${J} span because a pageload or navigation span is in progress.`))}U&&(U.setAttribute(Wn,"interactionInterrupted"),U.end(),U=void 0),I.name?U=vo({name:I.name,op:J,attributes:{[Ce]:I.source||"url"}},{idleTimeout:A,finalTimeout:$,childSpanTimeout:P}):he&&x.warn(`[Tracing] Did not create ${J} transaction because _latestRouteName is missing.`)};C&&addEventListener("click",re,{once:!1,capture:!0})}(E,a,u,l,w),n&&function(){const k=({entries:A})=>{const $=ce(),P=$&&te($);A.forEach(I=>{if(!function(U){return"duration"in U}(I)||!P)return;const C=I.interactionId;if(C!=null&&!Fn.has(C)){if(Or.length>10){const U=Or.shift();Fn.delete(U)}Or.push(C),Fn.set(C,P)}})};zt("event",k),zt("first-input",k)}(),zl(E,{traceFetch:f,traceXHR:d,trackFetchStreamPerformance:m,tracePropagationTargets:E.getOptions().tracePropagationTargets,shouldCreateSpanForRequest:h,enableHTTPTimings:g,onRequestSpanStart:b})}}};function si(e){const t=j.document,n=t==null?void 0:t.querySelector(`meta[name=${e}]`);return(n==null?void 0:n.getAttribute("content"))||void 0}const Ca="_sentry_idleSpan";function Dn(e){return e[Ca]}function oi(e,t){de(e,Ca,t)}const Yl=!1;var lr=Array.isArray,Xl=Array.prototype.indexOf,Zl=Array.from,Ra=Object.defineProperty,Be=Object.getOwnPropertyDescriptor,Da=Object.getOwnPropertyDescriptors,Ql=Object.prototype,ed=Array.prototype,Ps=Object.getPrototypeOf,ii=Object.isExtensible;function Ht(e){return typeof e=="function"}const Ne=()=>{};function of(e){return typeof(e==null?void 0:e.then)=="function"}function td(e){return e()}function an(e){for(var t=0;t<e.length;t++)e[t]()}function af(e,t,n=!1){return e===void 0?n?t():t:e}function cf(e,t){if(Array.isArray(e))return e;if(!(Symbol.iterator in e))return Array.from(e);const n=[];for(const r of e)if(n.push(r),n.length===t)break;return n}const _e=2,As=4,Sn=8,Os=16,Fe=32,jt=64,Cs=128,le=256,Yn=512,fe=1024,qe=2048,Ze=4096,Ct=8192,Rs=16384,Na=32768,Ds=65536,ai=1<<17,nd=1<<18,La=1<<19,is=1<<20,Ns=1<<21,Te=Symbol("$state"),Ma=Symbol("legacy props"),rd=Symbol(""),ja=new class extends Error{constructor(){super(...arguments);ye(this,"name","StaleReactionError");ye(this,"message","The reaction that called `getAbortSignal()` was re-run or destroyed")}};function qa(e){return e===this.v}function Fa(e,t){return e!=e?t==t:e!==t||e!==null&&typeof e=="object"||typeof e=="function"}function uf(e,t){return e!==t}function Ua(e){return!Fa(e,this.v)}let qt=!1,sd=!1;const lf=1,df=2,ff=4,pf=8,hf=16,od=1,id=2,Ha=4,ad=8,cd=16,mf=1,gf=2,vf=4,oe=Symbol(),ud="http://www.w3.org/1999/xhtml",_f="http://www.w3.org/2000/svg",ld="@attach";function yf(){throw new Error("https://svelte.dev/e/invalid_default_snippet")}function wn(e){throw new Error("https://svelte.dev/e/lifecycle_outside_component")}let M=null;function ci(e){M=e}function bf(e){return Wa().get(e)}function Sf(e,t){return Wa().set(e,t),t}function Ba(e,t=!1,n){var r=M={p:M,c:null,d:!1,e:null,m:!1,s:e,x:null,l:null};qt&&!t&&(M.l={s:null,u:null,r1:[],r2:fr(!1)}),En(()=>{r.d=!0})}function za(e){const t=M;if(t!==null){e!==void 0&&(t.x=e);const i=t.e;if(i!==null){var n=z,r=F;t.e=null;try{for(var s=0;s<i.length;s++){var o=i[s];Ye(o.effect),Ie(o.reaction),nc(o.fn)}}finally{Ye(n),Ie(r)}}M=t.p,t.m=!0}return e||{}}function dr(){return!qt||M!==null&&M.l===null}function Wa(e){return M===null&&wn(),M.c??(M.c=new Map(function(t){let n=t.p;for(;n!==null;){const r=n.c;if(r!==null)return r;n=n.p}return null}(M)||void 0))}function Et(e){if(typeof e!="object"||e===null||Te in e)return e;const t=Ps(e);if(t!==Ql&&t!==ed)return e;var n=new Map,r=lr(e),s=Ue(0),o=F,i=c=>{var a=F;Ie(o);var u=c();return Ie(a),u};return r&&n.set("length",Ue(e.length)),new Proxy(e,{defineProperty(c,a,u){"value"in u&&u.configurable!==!1&&u.enumerable!==!1&&u.writable!==!1||function(){throw new Error("https://svelte.dev/e/state_descriptors_fixed")}();var l=n.get(a);return l===void 0?l=i(()=>{var p=Ue(u.value);return n.set(a,p),p}):ee(l,u.value,!0),!0},deleteProperty(c,a){var u=n.get(a);if(u===void 0){if(a in c){const f=i(()=>Ue(oe));n.set(a,f),Nr(s)}}else{if(r&&typeof a=="string"){var l=n.get("length"),p=Number(a);Number.isInteger(p)&&p<l.v&&ee(l,p)}ee(u,oe),Nr(s)}return!0},get(c,a,u){var d;if(a===Te)return e;var l=n.get(a),p=a in c;if(l!==void 0||p&&!((d=Be(c,a))!=null&&d.writable)||(l=i(()=>Ue(Et(p?c[a]:oe))),n.set(a,l)),l!==void 0){var f=V(l);return f===oe?void 0:f}return Reflect.get(c,a,u)},getOwnPropertyDescriptor(c,a){var u=Reflect.getOwnPropertyDescriptor(c,a);if(u&&"value"in u){var l=n.get(a);l&&(u.value=V(l))}else if(u===void 0){var p=n.get(a),f=p==null?void 0:p.v;if(p!==void 0&&f!==oe)return{enumerable:!0,configurable:!0,value:f,writable:!0}}return u},has(c,a){var p;if(a===Te)return!0;var u=n.get(a),l=u!==void 0&&u.v!==oe||Reflect.has(c,a);return(u!==void 0||z!==null&&(!l||(p=Be(c,a))!=null&&p.writable))&&(u===void 0&&(u=i(()=>Ue(l?Et(c[a]):oe)),n.set(a,u)),V(u)===oe)?!1:l},set(c,a,u,l){var _;var p=n.get(a),f=a in c;if(r&&a==="length")for(var d=u;d<p.v;d+=1){var m=n.get(d+"");m!==void 0?ee(m,oe):d in c&&(m=i(()=>Ue(oe)),n.set(d+"",m))}p===void 0?f&&!((_=Be(c,a))!=null&&_.writable)||(ee(p=i(()=>Ue(void 0)),Et(u)),n.set(a,p)):(f=p.v!==oe,ee(p,i(()=>Et(u))));var h=Reflect.getOwnPropertyDescriptor(c,a);if(h!=null&&h.set&&h.set.call(l,u),!f){if(r&&typeof a=="string"){var g=n.get("length"),v=Number(a);Number.isInteger(v)&&v>=g.v&&ee(g,v+1)}Nr(s)}return!0},ownKeys(c){V(s);var a=Reflect.ownKeys(c).filter(p=>{var f=n.get(p);return f===void 0||f.v!==oe});for(var[u,l]of n)l.v===oe||u in c||a.push(u);return a},setPrototypeOf(){(function(){throw new Error("https://svelte.dev/e/state_prototype_fixed")})()}})}function Nr(e,t=1){ee(e,e.v+t)}function ui(e){try{if(e!==null&&typeof e=="object"&&Te in e)return e[Te]}catch{}return e}function Ft(e){var t=_e|qe,n=F!==null&&F.f&_e?F:null;return z===null||n!==null&&n.f&le?t|=le:z.f|=La,{ctx:M,deps:null,effects:null,equals:qa,f:t,fn:e,reactions:null,rv:0,v:null,wv:0,parent:n??z,ac:null}}function wf(e){const t=Ft(e);return lc(t),t}function Ja(e){const t=Ft(e);return t.equals=Ua,t}function Ga(e){var t=e.effects;if(t!==null){e.effects=null;for(var n=0;n<t.length;n+=1)ke(t[n])}}function Ka(e){var t,n=z;Ye(function(r){for(var s=r.parent;s!==null;){if(!(s.f&_e))return s;s=s.parent}return null}(e));try{Ga(e),t=pc(e)}finally{Ye(n)}return t}function Va(e){var t=Ka(e);e.equals(t)||(e.v=t,e.wv=dc()),Ut||be(e,(He||e.f&le)&&e.deps!==null?Ze:fe)}const cn=new Map;function fr(e,t){return{f:0,v:e,reactions:null,equals:qa,rv:0,wv:0}}function Ue(e,t){const n=fr(e);return lc(n),n}function as(e,t=!1,n=!0){var s;const r=fr(e);return t||(r.equals=Ua),qt&&n&&M!==null&&M.l!==null&&((s=M.l).s??(s.s=[])).push(r),r}function Ef(e,t){return ee(e,$e(()=>V(e))),t}function ee(e,t,n=!1){return F!==null&&(!Ee||F.f&ai)&&dr()&&F.f&(_e|Os|ai)&&((K==null?void 0:K.reaction)!==F||!K.sources.includes(e))&&function(){throw new Error("https://svelte.dev/e/state_unsafe_mutation")}(),Ya(e,n?Et(t):t)}function Ya(e,t){if(!e.equals(t)){var n=e.v;Ut?cn.set(e,t):cn.set(e,n),e.v=t,e.f&_e&&(e.f&qe&&Ka(e),be(e,e.f&le?Ze:fe)),e.wv=dc(),Xa(e,qe),dr()&&z!==null&&z.f&fe&&!(z.f&(Fe|jt))&&(pe===null?function(r){pe=r}([e]):pe.push(e))}return t}function li(e,t=1){var n=V(e),r=t===1?n++:n--;return ee(e,n),r}function Xa(e,t){var n=e.reactions;if(n!==null)for(var r=dr(),s=n.length,o=0;o<s;o++){var i=n[o],c=i.f;c&qe||(r||i!==z)&&(be(i,t),c&(fe|le)&&(c&_e?Xa(i,Ze):mr(i)))}}var di,dd,Za,Qa,ec;function Ls(e=""){return document.createTextNode(e)}function Rt(e){return Qa.call(e)}function Ms(e){return ec.call(e)}function fd(e,t){return Rt(e)}function pd(e,t){var n=Rt(e);return n instanceof Comment&&n.data===""?Ms(n):n}function xf(e,t=1,n=!1){let r=e;for(;t--;)r=Ms(r);return r}function Tf(e){e.textContent=""}function tc(e){z===null&&F===null&&function(t){throw new Error("https://svelte.dev/e/effect_orphan")}(),F!==null&&F.f&le&&z===null&&function(){throw new Error("https://svelte.dev/e/effect_in_unowned_derived")}(),Ut&&function(t){throw new Error("https://svelte.dev/e/effect_in_teardown")}()}function Qe(e,t,n,r=!0){var s=z,o={ctx:M,deps:null,nodes_start:null,nodes_end:null,f:e|qe,first:null,fn:t,last:null,next:null,parent:s,b:s&&s.b,prev:null,teardown:null,transitions:null,wv:0,ac:null};if(n)try{hr(o),o.f|=Na}catch(c){throw ke(o),c}else t!==null&&mr(o);if(!(n&&o.deps===null&&o.first===null&&o.nodes_start===null&&o.teardown===null&&!(o.f&(La|Cs)))&&r&&(s!==null&&function(c,a){var u=a.last;u===null?a.last=a.first=c:(u.next=c,c.prev=u,a.last=c)}(o,s),F!==null&&F.f&_e)){var i=F;(i.effects??(i.effects=[])).push(o)}return o}function En(e){const t=Qe(Sn,null,!1);return be(t,fe),t.teardown=e,t}function cs(e){if(tc(),!(z!==null&&z.f&Fe&&M!==null&&!M.m))return nc(e);var t=M;(t.e??(t.e=[])).push({fn:e,effect:z,reaction:F})}function nc(e){return Qe(As|Ns,e,!1)}function js(e){return Qe(As,e,!1)}function hd(e,t){var n=M,r={effect:null,ran:!1};n.l.r1.push(r),r.effect=qs(()=>{e(),r.ran||(r.ran=!0,ee(n.l.r2,!0),$e(t))})}function md(){var e=M;qs(()=>{if(V(e.l.r2)){for(var t of e.l.r1){var n=t.effect;n.f&fe&&be(n,Ze),xn(n)&&hr(n),t.ran=!1}e.l.r2.v=!1}})}function qs(e){return Qe(Sn,e,!0)}function gd(e,t=[],n=Ft){const r=t.map(n);return pr(()=>e(...r.map(V)))}function pr(e,t=0){return Qe(Sn|Os|t,e,!0)}function un(e,t=!0){return Qe(Sn|Fe,e,!0,t)}function rc(e){var t=e.teardown;if(t!==null){const n=Ut,r=F;pi(!0),Ie(null);try{t.call(null)}finally{pi(n),Ie(r)}}}function sc(e,t=!1){var s;var n=e.first;for(e.first=e.last=null;n!==null;){(s=n.ac)==null||s.abort(ja);var r=n.next;n.f&jt?n.parent=null:ke(n,t),n=r}}function ke(e,t=!0){var n=!1;(t||e.f&nd)&&e.nodes_start!==null&&e.nodes_end!==null&&(vd(e.nodes_start,e.nodes_end),n=!0),sc(e,t&&!n),Qn(e,0),be(e,Rs);var r=e.transitions;if(r!==null)for(const o of r)o.stop();rc(e);var s=e.parent;s!==null&&s.first!==null&&oc(e),e.next=e.prev=e.teardown=e.ctx=e.deps=e.fn=e.nodes_start=e.nodes_end=e.ac=null}function vd(e,t){for(;e!==null;){var n=e===t?null:Ms(e);e.remove(),e=n}}function oc(e){var t=e.parent,n=e.prev,r=e.next;n!==null&&(n.next=r),r!==null&&(r.prev=n),t!==null&&(t.first===e&&(t.first=r),t.last===e&&(t.last=n))}function us(e,t){var n=[];ic(e,n,!0),_d(n,()=>{ke(e),t&&t()})}function _d(e,t){var n=e.length;if(n>0){var r=()=>--n||t();for(var s of e)s.out(r)}else t()}function ic(e,t,n){if(!(e.f&Ct)){if(e.f^=Ct,e.transitions!==null)for(const o of e.transitions)(o.is_global||n)&&t.push(o);for(var r=e.first;r!==null;){var s=r.next;ic(r,t,!!(r.f&Ds||r.f&Fe)&&n),r=s}}}function fi(e){ac(e,!0)}function ac(e,t){if(e.f&Ct){e.f^=Ct;for(var n=e.first;n!==null;){var r=n.next;ac(n,!!(n.f&Ds||n.f&Fe)&&t),n=r}if(e.transitions!==null)for(const s of e.transitions)(s.is_global||t)&&s.in()}}let ln=[],Lr=[];function cc(){var e=ln;ln=[],an(e)}function Fs(e){ln.length===0&&queueMicrotask(cc),ln.push(e)}function yd(){var e;ln.length>0&&cc(),Lr.length>0&&(e=Lr,Lr=[],an(e))}function uc(e,t){for(;t!==null;){if(t.f&Cs)try{return void t.b.error(e)}catch{}t=t.parent}throw e}let dn=!1,fn=null,lt=!1,Ut=!1;function pi(e){Ut=e}let Yt=[],F=null,Ee=!1;function Ie(e){F=e}let z=null;function Ye(e){z=e}let K=null;function lc(e){F!==null&&F.f&is&&(K===null?K={reaction:F,sources:[e]}:K.sources.push(e))}let Q=null,ae=0,pe=null,Xn=1,Zn=0,He=!1,nt=null;function dc(){return++Xn}function xn(e){var p;var t=e.f;if(t&qe)return!0;if(t&Ze){var n=e.deps,r=!!(t&le);if(n!==null){var s,o,i=!!(t&Yn),c=r&&z!==null&&!He,a=n.length;if(i||c){var u=e,l=u.parent;for(s=0;s<a;s++)o=n[s],!i&&((p=o==null?void 0:o.reactions)!=null&&p.includes(u))||(o.reactions??(o.reactions=[])).push(u);i&&(u.f^=Yn),!c||l===null||l.f&le||(u.f^=le)}for(s=0;s<a;s++)if(xn(o=n[s])&&Va(o),o.wv>e.wv)return!0}r&&(z===null||He)||be(e,fe)}return!1}function fc(e,t,n=!0){var r=e.reactions;if(r!==null)for(var s=0;s<r.length;s++){var o=r[s];(K==null?void 0:K.reaction)===F&&K.sources.includes(e)||(o.f&_e?fc(o,t,!1):t===o&&(n?be(o,qe):o.f&fe&&be(o,Ze),mr(o)))}}function pc(e){var d;var t=Q,n=ae,r=pe,s=F,o=He,i=K,c=M,a=Ee,u=e.f;Q=null,ae=0,pe=null,He=!!(u&le)&&(Ee||!lt||F===null),F=u&(Fe|jt)?null:e,K=null,ci(e.ctx),Ee=!1,Zn++,e.f|=is,e.ac!==null&&(e.ac.abort(ja),e.ac=null);try{var l=(0,e.fn)(),p=e.deps;if(Q!==null){var f;if(Qn(e,ae),p!==null&&ae>0)for(p.length=ae+Q.length,f=0;f<Q.length;f++)p[ae+f]=Q[f];else e.deps=p=Q;if(!He||u&_e&&e.reactions!==null)for(f=ae;f<p.length;f++)((d=p[f]).reactions??(d.reactions=[])).push(e)}else p!==null&&ae<p.length&&(Qn(e,ae),p.length=ae);if(dr()&&pe!==null&&!Ee&&p!==null&&!(e.f&(_e|Ze|qe)))for(f=0;f<pe.length;f++)fc(pe[f],e);return s!==null&&s!==e&&(Zn++,pe!==null&&(r===null?r=pe:r.push(...pe))),l}catch(m){(function(h){var g=z;if(g.f&Na)uc(h,g);else{if(!(g.f&Cs))throw h;g.fn(h)}})(m)}finally{Q=t,ae=n,pe=r,F=s,He=o,K=i,ci(c),Ee=a,e.f^=is}}function bd(e,t){let n=t.reactions;if(n!==null){var r=Xl.call(n,e);if(r!==-1){var s=n.length-1;s===0?n=t.reactions=null:(n[r]=n[s],n.pop())}}n===null&&t.f&_e&&(Q===null||!Q.includes(t))&&(be(t,Ze),t.f&(le|Yn)||(t.f^=Yn),Ga(t),Qn(t,0))}function Qn(e,t){var n=e.deps;if(n!==null)for(var r=t;r<n.length;r++)bd(e,n[r])}function hr(e){var t=e.f;if(!(t&Rs)){be(e,fe);var n=z,r=lt;z=e,lt=!0;try{t&Os?function(o){for(var i=o.first;i!==null;){var c=i.next;i.f&Fe||ke(i),i=c}}(e):sc(e),rc(e);var s=pc(e);e.teardown=typeof s=="function"?s:null,e.wv=Xn,Yl&&sd&&e.f&qe&&e.deps}finally{lt=r,z=n}}}function Sd(){try{(function(){throw new Error("https://svelte.dev/e/effect_update_depth_exceeded")})()}catch(e){if(fn===null)throw e;uc(e,fn)}}function hc(){var e=lt;try{var t=0;for(lt=!0;Yt.length>0;){t++>1e3&&Sd();var n=Yt,r=n.length;Yt=[];for(var s=0;s<r;s++)wd(Ed(n[s]));cn.clear()}}finally{dn=!1,lt=e,fn=null}}function wd(e){var t=e.length;if(t!==0){for(var n=0;n<t;n++){var r=e[n];if(!(r.f&(Rs|Ct))&&xn(r)){var s=Xn;if(hr(r),r.deps===null&&r.first===null&&r.nodes_start===null&&(r.teardown===null?oc(r):r.fn=null),Xn>s&&r.f&Ns)break}}for(;n<t;n+=1)mr(e[n])}}function mr(e){dn||(dn=!0,queueMicrotask(hc));for(var t=fn=e;t.parent!==null;){var n=(t=t.parent).f;if(n&(jt|Fe)){if(!(n&fe))return;t.f^=fe}}Yt.push(t)}function Ed(e){for(var t=[],n=e;n!==null;){var r=n.f,s=!!(r&(Fe|jt));if(!(s&&r&fe||r&Ct)){r&As?t.push(n):s?n.f^=fe:xn(n)&&hr(n);var o=n.first;if(o!==null){n=o;continue}}var i=n.parent;for(n=n.next;n===null&&i!==null;)n=i.next,i=i.parent}return t}function xd(e){for(;;){if(yd(),Yt.length===0)return dn=!1,void(fn=null);dn=!0,hc()}}async function kf(){await Promise.resolve(),xd()}function V(e){var t=!!(e.f&_e);if(nt!==null&&nt.add(e),F===null||Ee){if(t&&e.deps===null&&e.effects===null){var n=e,r=n.parent;r===null||r.f&le||(n.f^=le)}}else if((K==null?void 0:K.reaction)!==F||!(K!=null&&K.sources.includes(e))){var s=F.deps;e.rv<Zn&&(e.rv=Zn,Q===null&&s!==null&&s[ae]===e?ae++:Q===null?Q=[e]:He&&Q.includes(e)||Q.push(e))}return t&&xn(n=e)&&Va(n),Ut&&cn.has(e)?cn.get(e):e.v}function If(e){var t=function(r){var s=nt;nt=new Set;var o,i=nt;try{if($e(r),s!==null)for(o of nt)s.add(o)}finally{nt=s}return i}(()=>$e(e));for(var n of t)Ya(n,n.v)}function $e(e){var t=Ee;try{return Ee=!0,e()}finally{Ee=t}}const Td=-7169;function be(e,t){e.f=e.f&Td|t}function kd(e,t){var n={};for(var r in e)t.includes(r)||(n[r]=e[r]);return n}function mc(e){if(typeof e=="object"&&e&&!(e instanceof EventTarget)){if(Te in e)ls(e);else if(!Array.isArray(e))for(let t in e){const n=e[t];typeof n=="object"&&n&&Te in n&&ls(n)}}}function ls(e,t=new Set){if(!(typeof e!="object"||e===null||e instanceof EventTarget||t.has(e))){t.add(e),e instanceof Date&&e.getTime();for(let r in e)try{ls(e[r],t)}catch{}const n=Ps(e);if(n!==Object.prototype&&n!==Array.prototype&&n!==Map.prototype&&n!==Set.prototype&&n!==Date.prototype){const r=Da(n);for(let s in r){const o=r[s].get;if(o)try{o.call(e)}catch{}}}}}function Id(e){return e.endsWith("capture")&&e!=="gotpointercapture"&&e!=="lostpointercapture"}const $d=["beforeinput","click","change","dblclick","contextmenu","focusin","focusout","input","keydown","keyup","mousedown","mousemove","mouseout","mouseover","mouseup","pointerdown","pointermove","pointerout","pointerover","pointerup","touchend","touchmove","touchstart"];function Pd(e){return $d.includes(e)}const Ad={formnovalidate:"formNoValidate",ismap:"isMap",nomodule:"noModule",playsinline:"playsInline",readonly:"readOnly",defaultvalue:"defaultValue",defaultchecked:"defaultChecked",srcobject:"srcObject",novalidate:"noValidate",allowfullscreen:"allowFullscreen",disablepictureinpicture:"disablePictureInPicture",disableremoteplayback:"disableRemotePlayback"};function Od(e){return e=e.toLowerCase(),Ad[e]??e}const Cd=["touchstart","touchmove"];function Rd(e){return Cd.includes(e)}function Dd(e,t){if(t){const n=document.body;e.autofocus=!0,Fs(()=>{document.activeElement===n&&e.focus()})}}let hi=!1;function $f(e,t,n,r=!0){for(var s of(r&&n(),t))e.addEventListener(s,n);En(()=>{for(var o of t)e.removeEventListener(o,n)})}function gc(e){var t=F,n=z;Ie(null),Ye(null);try{return e()}finally{Ie(t),Ye(n)}}function Pf(e,t,n,r=n){e.addEventListener(t,()=>gc(n));const s=e.__on_r;e.__on_r=s?()=>{s(),r(!0)}:()=>r(!0),hi||(hi=!0,document.addEventListener("reset",o=>{Promise.resolve().then(()=>{var i;if(!o.defaultPrevented)for(const c of o.target.elements)(i=c.__on_r)==null||i.call(c)})},{capture:!0}))}const vc=new Set,ds=new Set;function _c(e,t,n,r={}){function s(o){if(r.capture||Wt.call(t,o),!o.cancelBubble)return gc(()=>n==null?void 0:n.call(this,o))}return e.startsWith("pointer")||e.startsWith("touch")||e==="wheel"?Fs(()=>{t.addEventListener(e,s,r)}):t.addEventListener(e,s,r),s}function Af(e,t,n,r,s){var o={capture:r,passive:s},i=_c(e,t,n,o);(t===document.body||t===window||t===document||t instanceof HTMLMediaElement)&&En(()=>{t.removeEventListener(e,i,o)})}function Nd(e){for(var t=0;t<e.length;t++)vc.add(e[t]);for(var n of ds)n(e)}function Wt(e){var _;var t=this,n=t.ownerDocument,r=e.type,s=((_=e.composedPath)==null?void 0:_.call(e))||[],o=s[0]||e.target,i=0,c=e.__root;if(c){var a=s.indexOf(c);if(a!==-1&&(t===document||t===window))return void(e.__root=t);var u=s.indexOf(t);if(u===-1)return;a<=u&&(i=a)}if((o=s[i]||e.target)!==t){Ra(e,"currentTarget",{configurable:!0,get:()=>o||n});var l=F,p=z;Ie(null),Ye(null);try{for(var f,d=[];o!==null;){var m=o.assignedSlot||o.parentNode||o.host||null;try{var h=o["__"+r];if(h!=null&&(!o.disabled||e.target===o))if(lr(h)){var[g,...v]=h;g.apply(o,[e,...v])}else h.call(o,e)}catch(S){f?d.push(S):f=S}if(e.cancelBubble||m===t||m===null)break;o=m}if(f){for(let S of d)queueMicrotask(()=>{throw S});throw f}}finally{e.__root=t,delete e.currentTarget,Ie(l),Ye(p)}}}function yc(e){var t=document.createElement("template");return t.innerHTML=e.replaceAll("<!>","<!---->"),t.content}function pn(e,t){var n=z;n.nodes_start===null&&(n.nodes_start=e,n.nodes_end=t)}function bc(e,t){var n,r=!!(1&t),s=!!(2&t),o=!e.startsWith("<!>");return()=>{n===void 0&&(n=yc(o?e:"<!>"+e),r||(n=Rt(n)));var i=s||Za?document.importNode(n,!0):n.cloneNode(!0);return r?pn(Rt(i),i.lastChild):pn(i,i),i}}function Of(e,t){return function(n,r,s="svg"){var o,i=`<${s}>${n.startsWith("<!>")?"<!>"+n:n}</${s}>`;return()=>{if(!o){var c=Rt(yc(i));o=Rt(c)}var a=o.cloneNode(!0);return pn(a,a),a}}(e,0,"svg")}function Cf(e=""){var t=Ls(e+"");return pn(t,t),t}function Ld(){var e=document.createDocumentFragment(),t=document.createComment(""),n=Ls();return e.append(t,n),pn(t,n),e}function fs(e,t){e!==null&&e.before(t)}let ps=!0;function Rf(e){ps=e}function Df(e,t){var n=t==null?"":typeof t=="object"?t+"":t;n!==(e.__t??(e.__t=e.nodeValue))&&(e.__t=n,e.nodeValue=n+"")}function Nf(e,t){return function(n,{target:r,anchor:s,props:o={},events:i,context:c,intro:a=!0}){(function(){if(di===void 0){di=window,dd=document,Za=/Firefox/.test(navigator.userAgent);var d=Element.prototype,m=Node.prototype,h=Text.prototype;Qa=Be(m,"firstChild").get,ec=Be(m,"nextSibling").get,ii(d)&&(d.__click=void 0,d.__className=void 0,d.__attributes=null,d.__style=void 0,d.__e=void 0),ii(h)&&(h.__t=void 0)}})();var u=new Set,l=d=>{for(var m=0;m<d.length;m++){var h=d[m];if(!u.has(h)){u.add(h);var g=Rd(h);r.addEventListener(h,Wt,{passive:g});var v=_t.get(h);v===void 0?(document.addEventListener(h,Wt,{passive:g}),_t.set(h,1)):_t.set(h,v+1)}}};l(Zl(vc)),ds.add(l);var p=void 0,f=function(d){const m=Qe(jt,d,!0);return(h={})=>new Promise(g=>{h.outro?us(m,()=>{ke(m),g(void 0)}):(ke(m),g(void 0))})}(()=>{var d=s??r.appendChild(Ls());return un(()=>{c&&(Ba({}),M.c=c),i&&(o.$$events=i),ps=a,p=n(d,o)||{},ps=!0,c&&za()}),()=>{var g;for(var m of u){r.removeEventListener(m,Wt);var h=_t.get(m);--h==0?(document.removeEventListener(m,Wt),_t.delete(m)):_t.set(m,h)}ds.delete(l),d!==s&&((g=d.parentNode)==null||g.removeChild(d))}});return hs.set(p,f),p}(e,t)}const _t=new Map;let hs=new WeakMap;function Lf(e,t){const n=hs.get(e);return n?(hs.delete(e),n(t)):Promise.resolve()}function Md(e,t,[n,r]=[0,0]){var s=e,o=null,i=null,c=oe,a=!1;const u=(p,f=!0)=>{a=!0,l(f,p)},l=(p,f)=>{c!==(c=p)&&(c?(o?fi(o):f&&(o=un(()=>f(s))),i&&us(i,()=>{i=null})):(i?fi(i):f&&(i=un(()=>f(s,[n+1,r]))),o&&us(o,()=>{o=null})))};pr(()=>{a=!1,t(u),a||l(null,null)},n>0?Ds:0)}function jd(e,t,n,r,s){var c;var o=(c=t.$$slots)==null?void 0:c[n],i=!1;o===!0&&(o=t[n==="default"?"children":n],i=!0),o===void 0?s!==null&&s(e):o(e,i?()=>r:r)}function Mf(e){const t={};e.children&&(t.default=!0);for(const n in e.$$slots)t[n]=!0;return t}function qd(e,t){var n,r=void 0;pr(()=>{r!==(r=t())&&(n&&(ke(n),n=null),r&&(n=un(()=>{js(()=>r(e))})))})}function Sc(e){var t,n,r="";if(typeof e=="string"||typeof e=="number")r+=e;else if(typeof e=="object")if(Array.isArray(e)){var s=e.length;for(t=0;t<s;t++)e[t]&&(n=Sc(e[t]))&&(r&&(r+=" "),r+=n)}else for(n in e)e[n]&&(r&&(r+=" "),r+=n);return r}function Fd(e){return typeof e=="object"?function(){for(var t,n,r=0,s="",o=arguments.length;r<o;r++)(t=arguments[r])&&(n=Sc(t))&&(s&&(s+=" "),s+=n);return s}(e):e??""}const mi=[...` 	
\r\f \v\uFEFF`];function gi(e,t=!1){var n=t?" !important;":";",r="";for(var s in e){var o=e[s];o!=null&&o!==""&&(r+=" "+s+": "+o+n)}return r}function Mr(e){return e[0]!=="-"||e[1]!=="-"?e.toLowerCase():e}function wc(e,t,n,r,s,o){var i=e.__className;if(i!==n||i===void 0){var c=function(l,p,f){var d=l==null?"":""+l;if(p&&(d=d?d+" "+p:p),f){for(var m in f)if(f[m])d=d?d+" "+m:m;else if(d.length)for(var h=m.length,g=0;(g=d.indexOf(m,g))>=0;){var v=g+h;g!==0&&!mi.includes(d[g-1])||v!==d.length&&!mi.includes(d[v])?g=v:d=(g===0?"":d.substring(0,g))+d.substring(v+1)}}return d===""?null:d}(n,r,o);c==null?e.removeAttribute("class"):t?e.className=c:e.setAttribute("class",c),e.__className=n}else if(o&&s!==o)for(var a in o){var u=!!o[a];s!=null&&u===!!s[a]||e.classList.toggle(a,u)}return o}function jr(e,t={},n,r){for(var s in n){var o=n[s];t[s]!==o&&(n[s]==null?e.style.removeProperty(s):e.style.setProperty(s,o,r))}}function Ud(e,t,n,r){if(e.__style!==t){var s=function(o,i){if(i){var c,a,u="";if(Array.isArray(i)?(c=i[0],a=i[1]):c=i,o){o=String(o).replaceAll(/\s*\/\*.*?\*\/\s*/g,"").trim();var l=!1,p=0,f=!1,d=[];c&&d.push(...Object.keys(c).map(Mr)),a&&d.push(...Object.keys(a).map(Mr));var m=0,h=-1;const S=o.length;for(var g=0;g<S;g++){var v=o[g];if(f?v==="/"&&o[g-1]==="*"&&(f=!1):l?l===v&&(l=!1):v==="/"&&o[g+1]==="*"?f=!0:v==='"'||v==="'"?l=v:v==="("?p++:v===")"&&p--,!f&&l===!1&&p===0){if(v===":"&&h===-1)h=g;else if(v===";"||g===S-1){if(h!==-1){var _=Mr(o.substring(m,h).trim());d.includes(_)||(v!==";"&&g++,u+=" "+o.substring(m,g).trim()+";")}m=g+1,h=-1}}}}return c&&(u+=gi(c)),a&&(u+=gi(a,!0)),(u=u.trim())===""?null:u}return o==null?null:String(o)}(t,r);s==null?e.removeAttribute("style"):e.style.cssText=s,e.__style=t}else r&&(Array.isArray(r)?(jr(e,n==null?void 0:n[0],r[0]),jr(e,n==null?void 0:n[1],r[1],"important")):jr(e,n,r));return r}function qr(e,t,n=!1){if(e.multiple){if(t==null)return;if(!lr(t))return void console.warn("https://svelte.dev/e/select_multiple_invalid_value");for(var r of e.options)r.selected=t.includes(vi(r))}else{for(r of e.options){var s=vi(r);if(o=s,i=t,Object.is(ui(o),ui(i)))return void(r.selected=!0)}var o,i;n&&t===void 0||(e.selectedIndex=-1)}}function vi(e){return"__value"in e?e.__value:e.value}const St=Symbol("class"),Bt=Symbol("style"),Ec=Symbol("is custom element"),xc=Symbol("is html");function jf(e,t){var n=Us(e);n.value!==(n.value=t??void 0)&&(e.value!==t||t===0&&e.nodeName==="PROGRESS")&&(e.value=t??"")}function Hd(e,t){t?e.hasAttribute("selected")||e.setAttribute("selected",""):e.removeAttribute("selected")}function _i(e,t,n,r){var s=Us(e);s[t]!==(s[t]=n)&&(t==="loading"&&(e[rd]=n),n==null?e.removeAttribute(t):typeof n!="string"&&Tc(e).includes(t)?e[t]=n:e.setAttribute(t,n))}function Bd(e,t,n=[],r,s=!1,o=Ft){const i=n.map(o);var c=void 0,a={},u=e.nodeName==="SELECT",l=!1;if(pr(()=>{var f=t(...i.map(V)),d=function(h,g,v,_,S=!1){var y=Us(h),b=y[Ec],T=!y[xc],w=g||{},L=h.tagName==="OPTION";for(var E in g)E in v||(v[E]=null);v.class?v.class=Fd(v.class):(_||v[St])&&(v.class=null),v[Bt]&&(v.style??(v.style=null));var R=Tc(h);for(const I in v){let C=v[I];if(L&&I==="value"&&C==null)h.value=h.__value="",w[I]=C;else if(I!=="class")if(I!=="style"){var D=w[I];if(C!==D||C===void 0&&h.hasAttribute(I)){w[I]=C;var k=I[0]+I[1];if(k!=="$$")if(k==="on"){const U={},re="$$"+I;let J=I.slice(2);var A=Pd(J);if(Id(J)&&(J=J.slice(0,-7),U.capture=!0),!A&&D){if(C!=null)continue;h.removeEventListener(J,w[re],U),w[re]=null}if(C!=null)if(A)h[`__${J}`]=C,Nd([J]);else{let Se=function(ie){w[I].call(this,ie)};w[re]=_c(J,h,Se,U)}else A&&(h[`__${J}`]=void 0)}else if(I==="style")_i(h,I,C);else if(I==="autofocus")Dd(h,!!C);else if(b||I!=="__value"&&(I!=="value"||C==null))if(I==="selected"&&L)Hd(h,C);else{var $=I;T||($=Od($));var P=$==="defaultValue"||$==="defaultChecked";if(C!=null||b||P)P||R.includes($)&&(b||typeof C!="string")?h[$]=C:typeof C!="function"&&_i(h,$,C);else if(y[I]=null,$==="value"||$==="checked"){let U=h;const re=g===void 0;if($==="value"){let J=U.defaultValue;U.removeAttribute($),U.defaultValue=J,U.value=U.__value=re?J:null}else{let J=U.defaultChecked;U.removeAttribute($),U.defaultChecked=J,U.checked=!!re&&J}}else h.removeAttribute(I)}else h.value=h.__value=C}}else Ud(h,C,g==null?void 0:g[Bt],v[Bt]),w[I]=C,w[Bt]=v[Bt];else wc(h,h.namespaceURI==="http://www.w3.org/1999/xhtml",C,_,g==null?void 0:g[St],v[St]),w[I]=C,w[St]=v[St]}return w}(e,c,f,r,s);l&&u&&"value"in f&&qr(e,f.value);for(let h of Object.getOwnPropertySymbols(a))f[h]||ke(a[h]);for(let h of Object.getOwnPropertySymbols(f)){var m=f[h];h.description!==ld||c&&m===c[h]||(a[h]&&ke(a[h]),a[h]=un(()=>qd(e,()=>m))),d[h]=m}c=d}),u){var p=e;js(()=>{qr(p,c.value,!0),function(f){var d=new MutationObserver(()=>{qr(f,f.__value)});d.observe(f,{childList:!0,subtree:!0,attributes:!0,attributeFilter:["value"]}),En(()=>{d.disconnect()})}(p)})}l=!0}function Us(e){return e.__attributes??(e.__attributes={[Ec]:e.nodeName.includes("-"),[xc]:e.namespaceURI===ud})}var yi=new Map;function Tc(e){var t,n=yi.get(e.nodeName);if(n)return n;yi.set(e.nodeName,n=[]);for(var r=e,s=Element.prototype;s!==r;){for(var o in t=Da(r))t[o].set&&n.push(o);r=Ps(r)}return n}function bi(e,t){return e===t||(e==null?void 0:e[Te])===t}function qf(e={},t,n,r){return js(()=>{var s,o;return qs(()=>{s=o,o=(r==null?void 0:r())||[],$e(()=>{e!==n(...o)&&(t(e,...o),s&&bi(n(...s),e)&&t(null,...s))})}),()=>{Fs(()=>{o&&bi(n(...o),e)&&t(null,...o)})}}),e}function zd(e=!1){const t=M,n=t.l.u;if(!n)return;let r=()=>mc(t.s);if(e){let o=0,i={};const c=Ft(()=>{let a=!1;const u=t.s;for(const l in u)u[l]!==i[l]&&(i[l]=u[l],a=!0);return a&&o++,o});r=()=>V(c)}var s;n.b.length&&(s=()=>{Si(t,r),an(n.b)},tc(),Qe(Sn|Ns,s,!0)),cs(()=>{const o=$e(()=>n.m.map(td));return()=>{for(const i of o)typeof i=="function"&&i()}}),n.a.length&&cs(()=>{Si(t,r),an(n.a)})}function Si(e,t){if(e.l.s)for(const n of e.l.s)V(n);t()}function Hs(e,t,n){if(e==null)return t(void 0),n&&n(void 0),Ne;const r=$e(()=>e.subscribe(t,n));return r.unsubscribe?()=>r.unsubscribe():r}const yt=[];function Wd(e,t){return{subscribe:kc(e,t).subscribe}}function kc(e,t=Ne){let n=null;const r=new Set;function s(i){if(Fa(e,i)&&(e=i,n)){const c=!yt.length;for(const a of r)a[1](),yt.push(a,e);if(c){for(let a=0;a<yt.length;a+=2)yt[a][0](yt[a+1]);yt.length=0}}}function o(i){s(i(e))}return{set:s,update:o,subscribe:function(i,c=Ne){const a=[i,c];return r.add(a),r.size===1&&(n=t(s,o)||Ne),i(e),()=>{r.delete(a),r.size===0&&n&&(n(),n=null)}}}}function Ff(e,t,n){const r=!Array.isArray(e),s=r?[e]:e;if(!s.every(Boolean))throw new Error("derived() expects stores as input, got a falsy value");const o=t.length<2;return Wd(n,(i,c)=>{let a=!1;const u=[];let l=0,p=Ne;const f=()=>{if(l)return;p();const m=t(r?u[0]:u,i,c);o?i(m):p=typeof m=="function"?m:Ne},d=s.map((m,h)=>Hs(m,g=>{u[h]=g,l&=~(1<<h),a&&f()},()=>{l|=1<<h}));return a=!0,f(),function(){an(d),p(),a=!1}})}function Uf(e){return{subscribe:e.subscribe.bind(e)}}function Jd(e){let t;return Hs(e,n=>t=n)(),t}let Jt=!1,ms=Symbol();function Hf(e,t,n){const r=n[t]??(n[t]={store:null,source:as(void 0),unsubscribe:Ne});if(r.store!==e&&!(ms in n))if(r.unsubscribe(),r.store=e??null,e==null)r.source.v=void 0,r.unsubscribe=Ne;else{var s=!0;r.unsubscribe=Hs(e,o=>{s?r.source.v=o:ee(r.source,o)}),s=!1}return e&&ms in n?Jd(e):V(r.source)}function Bf(e,t,n){let r=n[t];return r&&r.store!==e&&(r.unsubscribe(),r.unsubscribe=Ne),e}function zf(e,t){return e.set(t),t}function Wf(){const e={};return[e,function(){En(()=>{for(var t in e)e[t].unsubscribe();Ra(e,ms,{enumerable:!1,value:!0})})}]}function Jf(e,t,n){return e.set(n),t}function Gf(){Jt=!0}const Gd={get(e,t){if(!e.exclude.includes(t))return V(e.version),t in e.special?e.special[t]():e.props[t]},set:(e,t,n)=>(t in e.special||(e.special[t]=xe({get[t](){return e.props[t]}},t,Ha)),e.special[t](n),li(e.version),!0),getOwnPropertyDescriptor(e,t){if(!e.exclude.includes(t))return t in e.props?{enumerable:!0,configurable:!0,value:e.props[t]}:void 0},deleteProperty:(e,t)=>(e.exclude.includes(t)||(e.exclude.push(t),li(e.version)),!0),has:(e,t)=>!e.exclude.includes(t)&&t in e.props,ownKeys:e=>Reflect.ownKeys(e.props).filter(t=>!e.exclude.includes(t))};function wi(e,t){return new Proxy({props:e,exclude:t,special:{},version:fr(0)},Gd)}const Kd={get(e,t){let n=e.props.length;for(;n--;){let r=e.props[n];if(Ht(r)&&(r=r()),typeof r=="object"&&r!==null&&t in r)return r[t]}},set(e,t,n){let r=e.props.length;for(;r--;){let s=e.props[r];Ht(s)&&(s=s());const o=Be(s,t);if(o&&o.set)return o.set(n),!0}return!1},getOwnPropertyDescriptor(e,t){let n=e.props.length;for(;n--;){let r=e.props[n];if(Ht(r)&&(r=r()),typeof r=="object"&&r!==null&&t in r){const s=Be(r,t);return s&&!s.configurable&&(s.configurable=!0),s}}},has(e,t){if(t===Te||t===Ma)return!1;for(let n of e.props)if(Ht(n)&&(n=n()),n!=null&&t in n)return!0;return!1},ownKeys(e){const t=[];for(let n of e.props)if(Ht(n)&&(n=n()),n){for(const r in n)t.includes(r)||t.push(r);for(const r of Object.getOwnPropertySymbols(n))t.includes(r)||t.push(r)}return t}};function Kf(...e){return new Proxy({props:e},Kd)}function xe(e,t,n,r){var v;var s,o,i=!qt||!!(n&id),c=!!(n&ad),a=!!(n&cd),u=r,l=!0,p=()=>(l&&(l=!1,u=a?$e(r):r),u);if(c){var f=Te in e||Ma in e;s=((v=Be(e,t))==null?void 0:v.set)??(f&&t in e?_=>e[t]=_:void 0)}var d,m=!1;if(c?[o,m]=function(_){var S=Jt;try{return Jt=!1,[_(),Jt]}finally{Jt=S}}(()=>e[t]):o=e[t],o===void 0&&r!==void 0&&(o=p(),s&&(i&&function(_){throw new Error("https://svelte.dev/e/props_invalid_value")}(),s(o))),d=i?()=>{var _=e[t];return _===void 0?p():(l=!0,_)}:()=>{var _=e[t];return _!==void 0&&(u=void 0),_===void 0?u:_},i&&!(n&Ha))return d;if(s){var h=e.$$legacy;return function(_,S){return arguments.length>0?(i&&S&&!h&&!m||s(S?d():_),_):d()}}var g=(n&od?Ft:Ja)(d);return c&&V(g),function(_,S){var b;if(arguments.length>0){const T=S?V(g):i&&c?Et(_):_;return ee(g,T),u!==void 0&&(u=T),_}return y=g,(b=y.ctx)!=null&&b.d?g.v:V(g);var y}}function Vd(e){M===null&&wn(),qt&&M.l!==null?Ic(M).m.push(e):cs(()=>{const t=$e(e);if(typeof t=="function")return t})}function Vf(e){M===null&&wn(),Vd(()=>()=>$e(e))}function Yf(){const e=M;return e===null&&wn(),(t,n,r)=>{var o;const s=(o=e.s.$$events)==null?void 0:o[t];if(s){const i=lr(s)?s.slice():[s],c=function(a,u,{bubbles:l=!1,cancelable:p=!1}={}){return new CustomEvent(a,{detail:u,bubbles:l,cancelable:p})}(t,n,r);for(const a of i)a.call(e.x,c);return!c.defaultPrevented}return!0}}function Xf(e){M===null&&wn(),M.l===null&&function(t){throw new Error("https://svelte.dev/e/lifecycle_legacy_only")}(),Ic(M).b.push(e)}function Ic(e){var t=e.l;return t.u??(t.u={a:[],b:[],m:[]})}var Zf=typeof globalThis<"u"?globalThis:typeof window<"u"?window:typeof global<"u"?global:typeof self<"u"?self:{};function Qf(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}class $c extends Error{constructor(t){super(t),this.name="PerformanceException"}}class Yd extends $c{constructor(n,r,s,o="unknown"){super(`Slow framerate detected: ${n.toFixed(1)} fps`);ye(this,"fps");ye(this,"threshold");ye(this,"avgFramerate");ye(this,"webviewId");this.name="SlowFramerateException",this.fps=n,this.threshold=r,this.avgFramerate=s,this.webviewId=o}}class Xd extends $c{constructor(n,r,s=null,o="unknown"){super(`Slow INP detected: ${n.toFixed(1)} ms${s?` on target: ${s}`:""}`);ye(this,"inp");ye(this,"threshold");ye(this,"target");ye(this,"webviewId");this.name="SlowINPException",this.inp=n,this.threshold=r,this.target=s,this.webviewId=o}}function Zd(e){if(window.augmentPerformance=window.augmentPerformance||{},window.augmentPerformance.initialized)return;window.augmentPerformance.initialized=!0;let t=0,n=performance.now(),r=60;const s=[];let o=0;const i=e.lowFramerateThreshold,c=e.slowInpThreshold;if(requestAnimationFrame(function a(u){const l=u-n;if(t++,l>1e3){r=1e3*t/l,t=0,n=u,s.push(r),s.length>10&&s.shift();const p=s.reduce((f,d)=>f+d,0)/s.length;if(r<i){console.error(`[Augment Performance] Slow framerate detected: ${r.toFixed(1)} fps`),console.error(`[Augment Performance] Avg framerate detected: ${p.toFixed(1)} fps`);const f=r<15,d=new Yd(r,i,p,window.location.href);en(m=>{m.setTag("performance_issue","slow_framerate"),m.setTag("fps_value",r.toFixed(1)),m.setTag("avg_fps",p.toFixed(1)),m.setTag("webview_url",window.location.href),m.setExtra("performance_data",{fps:r,avgFps:p,threshold:i,isCritical:f,framerateHistory:[...s]}),m.setLevel("warning"),Yr(d)})}}requestAnimationFrame(a)}),PerformanceObserver.supportedEntryTypes.includes("event"))try{new PerformanceObserver(a=>{(u=>{const l=u.getEntries().filter(d=>"interactionId"in d&&"duration"in d&&d.startTime>0&&d.duration<1e6);if(l.length===0)return;l.sort((d,m)=>m.duration-d.duration);const p=Math.floor(.98*l.length),f=l[Math.min(p,l.length-1)].duration;if(f>c){console.error(`[Augment Performance] Slow INP detected: ${f.toFixed(1)} ms`);let d=null;const m=l[0];m&&"target"in m&&(d=m.target,console.error("[Augment Performance] Slow interaction target:",d,m));const h=new Xd(f,c,d?String(d):null,window.location.href);en(g=>{g.setTag("performance_issue","slow_inp"),g.setTag("inp_value",f.toFixed(1)),g.setTag("webview_url",window.location.href),d&&g.setTag("interaction_target",String(d)),g.setExtra("performance_data",{inp:f,threshold:c,target:d}),g.setLevel("warning"),Yr(h)}),f>o&&(o=f)}})(a)}).observe({entryTypes:["event","first-input"],buffered:!0})}catch(a){console.error("[Augment Performance] Error setting up INP monitoring:",a)}else console.warn("[Augment Performance] PerformanceObserver not supported for INP monitoring");window.augmentPerformance.getFramerate=()=>r,window.augmentPerformance.getWorstINP=()=>o}const Ei=16,xi=200;function Ti(){var e;return((e=window.augmentFlags)==null?void 0:e.enablePerformanceMonitoring)??!1}let ki=!1;(function(){var n,r;const e=!!((r=(n=window.augmentFlags)==null?void 0:n.sentry)!=null&&r.enabled);var t;(t={enabled:Ti(),lowFramerateThreshold:Ei,slowInpThreshold:xi}).enabled&&Zd({lowFramerateThreshold:t.lowFramerateThreshold||Ei,slowInpThreshold:t.slowInpThreshold||xi}),Ti()&&!e&&console.warn("[Augment Performance] Performance monitoring enabled but Sentry is not initialized. Performance issues will not be reported to Sentry.")})(),function(){var t,n;if(!((n=(t=window.augmentFlags)==null?void 0:t.sentry)!=null&&n.enabled))return;const e=window.augmentFlags.sentry;if(e)if(ki)console.warn("Sentry is already initialized, duplicate initialization attempt");else try{(function(r){const s={...r};la(s,"svelte"),Bl(s)})({dsn:e.dsn,release:e.release,environment:e.environment,tracesSampleRate:e.tracesSampleRate||0,replaysSessionSampleRate:e.replaysSessionSampleRate||0,replaysOnErrorSampleRate:e.replaysOnErrorSampleRate||0,sampleRate:e.errorSampleRate||0,sendDefaultPii:e.sendDefaultPii!==void 0&&e.sendDefaultPii,integrations:(()=>{const r=[];return e.tracesSampleRate&&e.tracesSampleRate>0&&r.push(Vl()),r})(),beforeSend:r=>r}),e.tags&&Object.entries(e.tags).forEach(([r,s])=>{(function(o,i){Xe().setTag(o,i)})(r,String(s))}),ki=!0}catch(r){console.error("Failed to initialize Sentry:",r)}else console.warn("Sentry configuration not found in window.augmentDeps")}();let Qd=document.documentElement;function mt(){return Qd??document.documentElement}var Pc=(e=>(e.light="light",e.dark="dark",e))(Pc||{}),Ac=(e=>(e.regular="regular",e.highContrast="high-contrast",e))(Ac||{});const er="data-augment-theme-category",tr="data-augment-theme-intensity";function Oc(){const e=mt().getAttribute(er);if(e&&Object.values(Pc).includes(e))return e}function ep(e){e===void 0?mt().removeAttribute(er):mt().setAttribute(er,e)}function Cc(){const e=mt().getAttribute(tr);if(e&&Object.values(Ac).includes(e))return e}function tp(e){e===void 0?mt().removeAttribute(tr):mt().setAttribute(tr,e)}const Ii=kc(void 0);function ef(e){const t=new MutationObserver(n=>{for(const r of n)if(r.type==="attributes"){e(Oc(),Cc());break}});return t.observe(mt(),{attributeFilter:[er,tr],attributes:!0}),t}ef((e,t)=>{Ii.update(()=>({category:e,intensity:t}))}),Ii.update(()=>({category:Oc(),intensity:Cc()}));function tf(e){return e?{"data-ds-color":e}:{}}function np(e){return{"data-ds-radius":e}}function rp(e,t,n){return n?{[`data-ds-${e}-${t}`]:!0,[`data-${t}`]:!0}:{}}var $i;typeof window<"u"&&(($i=window.__svelte??(window.__svelte={})).v??($i.v=new Set)).add("5"),qt=!0;var nf=bc("<span><!></span>");function sp(e,t){const n=wi(t,["children","$$slots","$$events","$$legacy"]),r=wi(n,["size","weight","type","color","truncate"]);Ba(t,!1);const s=as(),o=as();let i=xe(t,"size",8,3),c=xe(t,"weight",8,"regular"),a=xe(t,"type",8,"default"),u=xe(t,"color",24,()=>{}),l=xe(t,"truncate",8,!1);hd(()=>(V(s),V(o),mc(r)),()=>{ee(s,r.class),ee(o,kd(r,["class"]))}),md(),zd();var p=nf();Bd(p,(f,d)=>({...f,class:`c-text c-text--size-${i()??""} c-text--weight-${c()??""} c-text--type-${a()??""} c-text--color-${u()??""} ${V(s)??""}`,...V(o),[St]:d}),[()=>u()?tf(u()):{},()=>({"c-text--has-color":u()!==void 0,"c-text--truncate":l()})],"svelte-zmgqjq"),jd(fd(p),t,"default",{},null),fs(e,p),za()}var rf=bc('<div data-testid="spinner-augment"><div class="c-spinner__leaf svelte-abmqgo"></div> <div class="c-spinner__leaf svelte-abmqgo"></div> <div class="c-spinner__leaf svelte-abmqgo"></div> <div class="c-spinner__leaf svelte-abmqgo"></div> <div class="c-spinner__leaf svelte-abmqgo"></div> <div class="c-spinner__leaf svelte-abmqgo"></div> <div class="c-spinner__leaf svelte-abmqgo"></div> <div class="c-spinner__leaf svelte-abmqgo"></div></div>');function op(e,t){let n=xe(t,"size",8,2),r=xe(t,"loading",8,!0),s=xe(t,"useCurrentColor",8,!1),o=xe(t,"class",8,"");var i=Ld(),c=pd(i),a=u=>{var l=rf();let p;gd(f=>p=wc(l,1,`c-spinner c-spinner--size-${n()??""} ${o()??""}`,"svelte-abmqgo",p,f),[()=>({"c-spinner--current-color":s()})],Ja),fs(u,l)};Md(c,u=>{r()&&u(a)}),fs(e,i)}export{kd as $,Ba as A,xe as B,ee as C,zd as D,Ds as E,bc as F,$e as G,Ld as H,pd as I,Md as J,jd as K,V as L,Ja as M,za as N,Yf as O,mc as P,xf as Q,js as R,Af as S,gd as T,oe as U,_i as V,Ud as W,sp as X,Cf as Y,Df as Z,hd as _,Bd as a,zf as a$,md as a0,tf as a1,St as a2,Wf as a3,Hf as a4,Bf as a5,Vf as a6,Kf as a7,ef as a8,Oc as a9,Jd as aA,uf as aB,Fa as aC,Ff as aD,Ef as aE,wf as aF,Uf as aG,kf as aH,ff as aI,Zl as aJ,lr as aK,Ct as aL,pf as aM,lf as aN,df as aO,ic as aP,Tf as aQ,_d as aR,hf as aS,Ms as aT,vd as aU,yc as aV,Rt as aW,Ac as aX,ep as aY,tp as aZ,np as a_,Pc as aa,wc as ab,ke as ac,_f as ad,pn as ae,Ls as af,z as ag,Rf as ah,Pf as ai,qs as aj,Vd as ak,di as al,qf as am,ps as an,Os as ao,Na as ap,vf as aq,gc as ar,Ht as as,Ne as at,F as au,mf as av,gf as aw,Ii as ax,op as ay,Nf as az,fs as b,Fd as b0,Be as b1,En as b2,yf as b3,cf as b4,If as b5,jf as b6,Jf as b7,dd as b8,Wd as b9,rp as ba,$f as bb,Lf as bc,Gf as bd,Xf as be,af as bf,cs as bg,li as bh,pr as c,of as d,Ya as e,Of as f,Ye as g,Ie as h,dr as i,ci as j,M as k,wi as l,as as m,un as n,xd as o,us as p,Fs as q,fi as r,fr as s,fd as t,bf as u,Sf as v,kc as w,Zf as x,Qf as y,Mf as z};
