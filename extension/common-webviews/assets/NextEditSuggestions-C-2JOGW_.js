import{aj as B0,b2 as N0,v as Z0,w as I0,u as D0,A as C1,B as c,D as p1,F as H,I as h3,Q as A,L as a,R as j1,S as l1,aF as h1,a4 as x1,m as $,T as q,M as Y,G as R,P as S,V as N,ab as Q1,W as y1,b as E,am as C3,C as M,N as v1,a3 as O1,b4 as i2,l as g2,_ as Q,a0 as R1,H as r0,J as H1,a as h2,a2 as K0,t as p,K as s3,f as u1,aa as C2,ax as i0,Z as U3,b7 as U1,b8 as T0,b9 as V0,ak as o0,a6 as W0,ay as o2,a7 as G0,y as U0,al as q0,a$ as N1,Y as H0}from"./SpinnerAugment-B-W1rkU5.js";import{S as J1,C as Z3}from"./next-edit-types-904A5ehg.js";import{e as p3,i as a0,a as l3,W as e1,c as L1}from"./IconButtonAugment-Cdot7Te3.js";import{a as A1,g as p2,b as v2,c as T1,f as s0,S as J0,I as l0,d as f2,i as Q0,e as c0,n as n3,A as u0,h as Y0,s as q3,j as X0,k as m2,l as t4,m as w2}from"./IconFilePath-OVVJCL_5.js";import{A as e4}from"./async-messaging-h5fbTmxI.js";import{r as n4,D as r4}from"./Drawer-DF8SmKU6.js";import{o as A3}from"./keypress-DD1aQVr0.js";import{t as d0}from"./index-C0pD_8h5.js";import{V as i4}from"./VSCodeCodicon-PwzMb_dE.js";import{c as o4}from"./svelte-component-BFVtr7-z.js";import{a as a4}from"./monaco-render-utils-DfwV7QLY.js";import{e as L2}from"./toggleHighContrast-Cb9MCs64.js";import{a as s4,g as y2,M as l4}from"./index-BPaFHx0a.js";import"./index-Bmm_dv1C.js";import{r as c4}from"./LanguageIcon-C0sRX_q4.js";import{i as g0,_ as M3,a as W1,b as x2,c as j3,d as u4}from"./isObjectLike-KnT2wtGt.js";import{B as d4}from"./ButtonAugment-BsoJM5iW.js";import"./ellipsis-tqj1KnpB.js";import"./preload-helper-Dv6uf1Os.js";const h0=Symbol("code-roll-selection-context");function c3(){let t=D0(h0);return t||(t=C0({})),t}function C0(t){return Z0(h0,I0(t))}function q1(t){return t.activeSuggestion??t.selectedSuggestion??t.nextSuggestion}function y3(t){return t.activeSuggestion?"active":t.selectedSuggestion?"select":"next"}function E1(t,e,n){return e.activeSuggestion?A1(e.activeSuggestion,t)?n?"select":"active":"none":e.selectedSuggestion?A1(e.selectedSuggestion,t)?n?"active":"select":"none":e.nextSuggestion&&A1(e.nextSuggestion,t)?"next":"none"}function _3(t){return["",`--augment-code-roll-selection-background: var(--augment-code-roll-item-background-${t})`,`--augment-code-roll-selection-color: var(--augment-code-roll-item-color-${t})`,"--augment-code-roll-selection-border: var(--augment-code-roll-selection-background)",""].join(";")}const I3={scrollContainer:document.documentElement,scrollIntoView:{behavior:"smooth",block:"nearest"},scrollDelayMS:100,useSmartBlockAlignment:!0,doScroll:!0};function F1(t,e=I3){let n,r=Object.assign({},I3,e);function i(o){let{doScroll:s,scrollIntoView:l,scrollDelayMS:C,useSmartBlockAlignment:u,scrollContainer:d}=Object.assign({},r,o);s&&(u&&t.getBoundingClientRect().height>((d==null?void 0:d.getBoundingClientRect().height)??1/0)&&(l=Object.assign({},l,{block:"start"})),n=setTimeout(()=>{const _=t.getBoundingClientRect();if(_.bottom===0&&_.top===0&&_.height===0&&_.width===0)return;const L=function(f,w,m){const j=f.getBoundingClientRect(),x=w.getBoundingClientRect();if(m==="nearest")if(j.bottom>x.bottom)m="end";else{if(!(j.top<x.top))return w.scrollTop;m="start"}return j.height>x.height||m==="start"?w.scrollTop+j.top:m==="end"?w.scrollTop+j.bottom-x.height:w.scrollTop+j.top-(x.height-j.height)/2}(t,d,(l==null?void 0:l.block)??I3.scrollIntoView.block);d.scrollTo({top:L,behavior:l==null?void 0:l.behavior})},C))}return i(r),{update:i,destroy(){clearTimeout(n)}}}var g4=H('<div role="button" tabindex="0"></div>'),h4=H('<div class="c-suggestion-tree__file-divider svelte-ffrg54"></div> <!>',1),C4=H("<div></div>");function p4(t,e){C1(e,!1);const[n,r]=O1(),i=()=>x1(C,"$ctx",n);let o=c(e,"onCodeAction",8),s=c(e,"sortedPathSuggestionsMap",24,()=>new Map),l=c(e,"show",8,!0);const C=c3();let u=$();p1();var d=C4();let _;p3(d,5,()=>(S(s()),R(()=>s().entries())),([L,f])=>L,(L,f)=>{var w=h1(()=>i2(a(f),2)),m=h4(),j=h3(m),x=A(j,2);p3(x,1,()=>a(w)[1],a0,(v,P)=>{var h=g4();const F=Y(()=>(S(E1),a(P),i(),R(()=>E1(a(P),i(),!0))));var O=h1(()=>A3("Space",()=>o()("select",a(P))));j1(()=>l1("click",h,()=>o()("select",a(P)))),j1(()=>l1("keydown",h,function(...k){var g;(g=a(O))==null||g.apply(this,k)})),l3(h,(k,g)=>F1==null?void 0:F1(k,g),()=>({scrollContainer:a(u),doScroll:A1(a(P),q1(i())),scrollIntoView:{behavior:"smooth",block:"nearest"}})),q(k=>{N(h,"title",`${a(P),R(()=>a(P).lineRange.start+1)??""}: ${a(P),R(()=>a(P).result.changeDescription)??""}`),Q1(h,1,`c-suggestion-tree__tick-mark ${a(F)??""}`,"svelte-ffrg54"),y1(h,k)},[()=>(S(a(F)),R(()=>function(k){let g="var(--ds-color-neutral-7, currentColor)";return k==="active"&&(g="var(--augment-code-roll-item-background-active, currentColor)"),k==="select"&&(g="currentColor"),k==="next"&&(g="var(--ds-color-neutral-10, currentColor)"),`--augment-code-roll-selection-background: ${g}`}(a(F))))],Y),E(v,h)}),q(()=>N(j,"title",a(w)[0])),E(L,m)}),C3(d,L=>M(u,L),()=>a(u)),q(L=>_=Q1(d,1,"c-suggestion-tree__minimized svelte-ffrg54",null,_,L),[()=>({hidden:!l()})],Y),E(t,d),v1(),r()}const D3={duration:300,easing:"ease-out"},K3=(t,e=D3)=>{const n=t.querySelector("summary"),r=t.querySelector(".c-detail__content");if(!n||!r)return;e={...D3,...e};const i=/^(tb|vertical)/.test(getComputedStyle(r).writingMode);let o=!1;const s=d=>{o=!0,d&&(t.open=!0),t.dispatchEvent(new CustomEvent(d?"open-start":"close-start",{detail:t}));const _=r[i?"clientWidth":"clientHeight"],L=r.animate({blockSize:d?["0",`${_}px`]:[`${_}px`,"0"]},e);L.oncancel=L.onfinish=L.onremove=()=>{t.dispatchEvent(new CustomEvent(d?"open-end":"close-end",{detail:t})),d||(t.open=!1),o=!1}},l=new MutationObserver(d=>{for(const _ of d)if(_.type==="attributes"&&_.attributeName==="open"){if(o)return;t.open&&s(!0)}});l.observe(t,{attributes:!0});const C=d=>{d.preventDefault(),o||s(!t.open)},u=A3("Enter",C);return n.addEventListener("click",C),n.addEventListener("keypress",u),{destroy(){l.disconnect(),n.removeEventListener("click",C),n.removeEventListener("keypress",u)},update(d=D3){e={...e,...d}}}};var v4=H('<details><summary class="c-detail__summary svelte-hwtbr4"><!> <!></summary> <div class="c-detail__content svelte-hwtbr4"><!></div></details>'),f4=H('<div><div class="c-detail__summary svelte-hwtbr4"><!></div> <div class="c-detail__content svelte-hwtbr4"><!></div></div>');function p0(t,e){const n=g2(e,["children","$$slots","$$events","$$legacy"]),r=g2(n,["open","duration","expandable","onChangeOpen","class"]);C1(e,!1);let i=c(e,"open",12,!0),o=c(e,"duration",8,300),s=c(e,"expandable",8,!0),l=c(e,"onChangeOpen",24,()=>{}),C=c(e,"class",8,"");const u=typeof o()=="number"?{duration:o()}:o();let d=$(i());const _=x=>()=>{var v;M(d,x!=="close"),(v=l())==null||v(x==="open")},L=x=>()=>{M(d,x==="open")};Q(()=>S(i()),()=>{M(d,i())}),R1(),p1();var f=r0(),w=h3(f),m=x=>{var v=v4(),P=h1(()=>L("close")),h=h1(()=>L("open")),F=h1(()=>_("close")),O=h1(()=>_("open"));h2(v,I=>({...r,style:`--au-detail-duration: ${R(()=>u.duration)??""}ms`,class:`c-detail ${C()??""}`,[K0]:I}),[()=>({"c-detail__rotate":a(d)})],"svelte-hwtbr4");var k=p(v),g=p(k);i4(g,{icon:"chevron-down",class:"c-detail__chevron"});var b=A(g,2);s3(b,e,"summary",{},null);var z=A(k,2),c1=p(z);s3(c1,e,"default",{},null),j1(()=>function(I,D,J,K,W){var r1=()=>{K(J[I])};J.addEventListener(D,r1),W?B0(()=>{J[I]=W()}):r1(),J!==document.body&&J!==window&&J!==document||N0(()=>{J.removeEventListener(D,r1)})}("open","toggle",v,i,i)),l3(v,(I,D)=>K3==null?void 0:K3(I,D),()=>u),j1(()=>l1("close-start",v,function(...I){var D;(D=a(P))==null||D.apply(this,I)})),j1(()=>l1("open-start",v,function(...I){var D;(D=a(h))==null||D.apply(this,I)})),j1(()=>l1("close-end",v,function(...I){var D;(D=a(F))==null||D.apply(this,I)})),j1(()=>l1("open-end",v,function(...I){var D;(D=a(O))==null||D.apply(this,I)})),E(x,v)},j=x=>{var v=f4();h2(v,()=>({...r,class:`c-detail ${C()??""}`}),void 0,"svelte-hwtbr4");var P=p(v),h=p(P);s3(h,e,"summary",{},null);var F=A(P,2),O=p(F);s3(O,e,"default",{},null),E(x,v)};H1(w,x=>{s()?x(m):x(j,!1)}),E(t,f),v1()}const $1=Symbol.for("@ts-pattern/matcher"),m4=Symbol.for("@ts-pattern/isVariadic"),k3="@ts-pattern/anonymous-select-key",H3=t=>!!(t&&typeof t=="object"),x3=t=>t&&!!t[$1],S1=(t,e,n)=>{if(x3(t)){const r=t[$1](),{matched:i,selections:o}=r.match(e);return i&&o&&Object.keys(o).forEach(s=>n(s,o[s])),i}if(H3(t)){if(!H3(e))return!1;if(Array.isArray(t)){if(!Array.isArray(e))return!1;let r=[],i=[],o=[];for(const s of t.keys()){const l=t[s];x3(l)&&l[m4]?o.push(l):o.length?i.push(l):r.push(l)}if(o.length){if(o.length>1)throw new Error("Pattern error: Using `...P.array(...)` several times in a single pattern is not allowed.");if(e.length<r.length+i.length)return!1;const s=e.slice(0,r.length),l=i.length===0?[]:e.slice(-i.length),C=e.slice(r.length,i.length===0?1/0:-i.length);return r.every((u,d)=>S1(u,s[d],n))&&i.every((u,d)=>S1(u,l[d],n))&&(o.length===0||S1(o[0],C,n))}return t.length===e.length&&t.every((s,l)=>S1(s,e[l],n))}return Reflect.ownKeys(t).every(r=>{const i=t[r];return(r in e||x3(o=i)&&o[$1]().matcherType==="optional")&&S1(i,e[r],n);var o})}return Object.is(e,t)},V1=t=>{var e,n,r;return H3(t)?x3(t)?(e=(n=(r=t[$1]()).getSelectionKeys)==null?void 0:n.call(r))!=null?e:[]:Array.isArray(t)?v3(t,V1):v3(Object.values(t),V1):[]},v3=(t,e)=>t.reduce((n,r)=>n.concat(e(r)),[]);function m1(t){return Object.assign(t,{optional:()=>w4(t),and:e=>U(t,e),or:e=>L4(t,e),select:e=>e===void 0?b2(t):b2(e,t)})}function w4(t){return m1({[$1]:()=>({match:e=>{let n={};const r=(i,o)=>{n[i]=o};return e===void 0?(V1(t).forEach(i=>r(i,void 0)),{matched:!0,selections:n}):{matched:S1(t,e,r),selections:n}},getSelectionKeys:()=>V1(t),matcherType:"optional"})})}function U(...t){return m1({[$1]:()=>({match:e=>{let n={};const r=(i,o)=>{n[i]=o};return{matched:t.every(i=>S1(i,e,r)),selections:n}},getSelectionKeys:()=>v3(t,V1),matcherType:"and"})})}function L4(...t){return m1({[$1]:()=>({match:e=>{let n={};const r=(i,o)=>{n[i]=o};return v3(t,V1).forEach(i=>r(i,void 0)),{matched:t.some(i=>S1(i,e,r)),selections:n}},getSelectionKeys:()=>v3(t,V1),matcherType:"or"})})}function Z(t){return{[$1]:()=>({match:e=>({matched:!!t(e)})})}}function b2(...t){const e=typeof t[0]=="string"?t[0]:void 0,n=t.length===2?t[1]:typeof t[0]=="string"?void 0:t[0];return m1({[$1]:()=>({match:r=>{let i={[e??k3]:r};return{matched:n===void 0||S1(n,r,(o,s)=>{i[o]=s}),selections:i}},getSelectionKeys:()=>[e??k3].concat(n===void 0?[]:V1(n))})})}function _1(t){return typeof t=="number"}function Z1(t){return typeof t=="string"}function I1(t){return typeof t=="bigint"}m1(Z(function(t){return!0}));const D1=t=>Object.assign(m1(t),{startsWith:e=>{return D1(U(t,(n=e,Z(r=>Z1(r)&&r.startsWith(n)))));var n},endsWith:e=>{return D1(U(t,(n=e,Z(r=>Z1(r)&&r.endsWith(n)))));var n},minLength:e=>D1(U(t,(n=>Z(r=>Z1(r)&&r.length>=n))(e))),length:e=>D1(U(t,(n=>Z(r=>Z1(r)&&r.length===n))(e))),maxLength:e=>D1(U(t,(n=>Z(r=>Z1(r)&&r.length<=n))(e))),includes:e=>{return D1(U(t,(n=e,Z(r=>Z1(r)&&r.includes(n)))));var n},regex:e=>{return D1(U(t,(n=e,Z(r=>Z1(r)&&!!r.match(n)))));var n}});D1(Z(Z1));const k1=t=>Object.assign(m1(t),{between:(e,n)=>k1(U(t,((r,i)=>Z(o=>_1(o)&&r<=o&&i>=o))(e,n))),lt:e=>k1(U(t,(n=>Z(r=>_1(r)&&r<n))(e))),gt:e=>k1(U(t,(n=>Z(r=>_1(r)&&r>n))(e))),lte:e=>k1(U(t,(n=>Z(r=>_1(r)&&r<=n))(e))),gte:e=>k1(U(t,(n=>Z(r=>_1(r)&&r>=n))(e))),int:()=>k1(U(t,Z(e=>_1(e)&&Number.isInteger(e)))),finite:()=>k1(U(t,Z(e=>_1(e)&&Number.isFinite(e)))),positive:()=>k1(U(t,Z(e=>_1(e)&&e>0))),negative:()=>k1(U(t,Z(e=>_1(e)&&e<0)))});k1(Z(_1));const K1=t=>Object.assign(m1(t),{between:(e,n)=>K1(U(t,((r,i)=>Z(o=>I1(o)&&r<=o&&i>=o))(e,n))),lt:e=>K1(U(t,(n=>Z(r=>I1(r)&&r<n))(e))),gt:e=>K1(U(t,(n=>Z(r=>I1(r)&&r>n))(e))),lte:e=>K1(U(t,(n=>Z(r=>I1(r)&&r<=n))(e))),gte:e=>K1(U(t,(n=>Z(r=>I1(r)&&r>=n))(e))),positive:()=>K1(U(t,Z(e=>I1(e)&&e>0))),negative:()=>K1(U(t,Z(e=>I1(e)&&e<0)))});K1(Z(I1)),m1(Z(function(t){return typeof t=="boolean"})),m1(Z(function(t){return typeof t=="symbol"})),m1(Z(function(t){return t==null})),m1(Z(function(t){return t!=null}));class y4 extends Error{constructor(e){let n;try{n=JSON.stringify(e)}catch{n=e}super(`Pattern matching error: no pattern matches value ${n}`),this.input=void 0,this.input=e}}const J3={matched:!1,value:void 0};class S3{constructor(e,n){this.input=void 0,this.state=void 0,this.input=e,this.state=n}with(...e){if(this.state.matched)return this;const n=e[e.length-1],r=[e[0]];let i;e.length===3&&typeof e[1]=="function"?i=e[1]:e.length>2&&r.push(...e.slice(1,e.length-1));let o=!1,s={};const l=(u,d)=>{o=!0,s[u]=d},C=!r.some(u=>S1(u,this.input,l))||i&&!i(this.input)?J3:{matched:!0,value:n(o?k3 in s?s[k3]:s:this.input,this.input)};return new S3(this.input,C)}when(e,n){if(this.state.matched)return this;const r=!!e(this.input);return new S3(this.input,r?{matched:!0,value:n(this.input,this.input)}:J3)}otherwise(e){return this.state.matched?this.state.value:e(this.input)}exhaustive(){if(this.state.matched)return this.state.value;throw new y4(this.input)}run(){return this.exhaustive()}returnType(){return this}}var x4=u1('<svg width="16" height="16" viewBox="0 0 16 16" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink"><title>nextedit_addition_light</title><g id="nextedit_addition_light" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd"><g id="Pencil_Base"><path d="M3.07557525,3.27946831 C3.10738379,3.27258798 3.13664209,3.26682472 3.16597818,3.26160513 C3.19407786,3.25661079 3.22181021,3.25217747 3.24959807,3.24822758 C3.3431507,3.23490837 3.43787348,3.22705558 3.53270619,3.22474499 C3.54619312,3.22441336 3.56021661,3.22418981 3.57424082,3.22408741 L3.59202055,3.22402251 C3.61600759,3.22402251 3.63999463,3.22437692 3.66397314,3.22508575 C3.69176119,3.22590043 3.72012236,3.22722855 3.74845755,3.22905289 C3.77692744,3.23089046 3.80498198,3.23319023 3.83299719,3.23597733 C3.86236278,3.23889105 3.89230728,3.24242516 3.92218997,3.24651769 C3.95842477,3.25149198 3.99379267,3.25714552 4.02904516,3.2635852 C4.04457753,3.26641925 4.06056799,3.26950351 4.07653203,3.27274998 C4.1217801,3.28195855 4.16647313,3.29238022 4.21089814,3.30408537 C4.22093231,3.3067264 4.23153789,3.30959531 4.24212737,3.31253756 C4.27196202,3.32083528 4.30106886,3.32952376 4.33003598,3.33877116 C4.35855924,3.347869 4.38751122,3.35771229 4.41630528,3.3681193 C4.42116985,3.36987869 4.42551008,3.37146263 4.42984665,3.3730594 C4.4761162,3.39008583 4.52241276,3.4087674 4.56821184,3.42893807 C4.59406406,3.44033198 4.61917606,3.45191971 4.64412424,3.46396063 C4.67111495,3.47697976 4.69839649,3.4907848 4.72546291,3.50513959 C4.75890801,3.52288219 4.79178851,3.54132453 4.82431475,3.56059431 C4.8374698,3.56838641 4.85073285,3.5764165 4.86393439,3.58458539 C4.89491851,3.60376145 4.92539479,3.6235868 4.95550936,3.64416832 C4.9772823,3.65904443 4.99913454,3.67451232 5.02078256,3.69038541 C5.03998798,3.70447076 5.05881967,3.71870909 5.07748715,3.73325923 C5.10440445,3.75423289 5.13126725,3.7760983 5.15775949,3.79862613 C5.1821715,3.81939236 5.20595148,3.84042939 5.22940861,3.86201411 C5.24512436,3.87647694 5.26059993,3.89109333 5.27592752,3.90595256 C5.28442786,3.91418351 5.29385225,3.92345739 5.30321896,3.9328241 L10.2031018,8.83270693 C10.255475,8.88508012 10.3065885,8.93859789 10.3564099,8.99321224 L10.2031018,8.83270693 C10.2748395,8.90444467 10.344214,8.97832987 10.4111413,9.05423915 C10.4223877,9.06699478 10.4335715,9.07981507 10.4446856,9.092692 C10.7663645,9.46539004 11.0297601,9.88553066 11.2252237,10.3388957 L11.6780206,11.3880225 L12.548286,13.4076516 C12.7467158,13.8678966 12.5344727,14.4018581 12.0742277,14.6002879 C11.9977866,14.6332447 11.9179446,14.6552159 11.836969,14.6662015 L11.7149387,14.6744406 C11.592625,14.6744406 11.4703113,14.6497231 11.3556497,14.6002879 L11.2340206,14.5480225 L9.33602055,13.7300225 L8.28689372,13.2772256 C7.83352871,13.081762 7.41338809,12.8183665 7.04069004,12.4966876 L7.0022372,12.4631433 C6.98177889,12.4451057 6.9614676,12.4268903 6.94130575,12.4084989 L7.04069004,12.4966876 C6.95122931,12.4194733 6.86450207,12.3389008 6.78070498,12.2551038 L1.88082214,7.35522092 C0.935753358,6.41015213 0.935753358,4.87789288 1.88082214,3.9328241 L1.90902055,3.90502251 L2.01192506,3.8109306 C2.19120357,3.65606766 2.38780913,3.5318516 2.59488381,3.4382824 C2.62872186,3.42311621 2.65522016,3.41182111 2.68187195,3.40102033 C2.76025666,3.36925866 2.83986347,3.34180278 2.92043821,3.31861145 L3.07557525,3.27946831 Z M9.58610551,9.95149698 L7.89951995,11.6381324 C8.10279642,11.805046 8.32371441,11.9494547 8.55841217,12.068738 L8.76594574,12.166096 L10.2570206,12.8090225 L10.7570206,12.3090225 L10.114094,10.8179477 C9.97930356,10.5053101 9.80144069,10.2137385 9.58610551,9.95149698 Z" id="Combined-Shape" fill-rule="nonzero"></path><rect id="Rectangle" opacity="0.005" x="0" y="0" width="16" height="16" rx="2"></rect></g><g id="Group" transform="translate(9, 1)"><rect id="Rectangle" x="2.25" y="0" width="1.5" height="6" rx="0.75"></rect><rect id="Rectangle-Copy" transform="translate(3, 3) rotate(90) translate(-3, -3)" x="2.25" y="1.13686838e-13" width="1.5" height="6" rx="0.75"></rect></g></g></svg>');function b4(t,e){let n=c(e,"mask",8,!1),r=c(e,"maskColor",8,"white");var i=x4(),o=A(p(i)),s=p(o),l=A(s);q(()=>{N(s,"fill",n()?r():"#0A84FF"),N(l,"fill",n()?r():"#34C759")}),E(t,i)}var _4=u1('<svg width="16" height="16" viewBox="0 0 16 16" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink"><title>nextedit_addition_dark</title><g id="nextedit_addition_dark" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd"><g id="Pencil_Base"><path d="M3.07557525,3.27946831 C3.10738379,3.27258798 3.13664209,3.26682472 3.16597818,3.26160513 C3.19407786,3.25661079 3.22181021,3.25217747 3.24959807,3.24822758 C3.3431507,3.23490837 3.43787348,3.22705558 3.53270619,3.22474499 C3.54619312,3.22441336 3.56021661,3.22418981 3.57424082,3.22408741 L3.59202055,3.22402251 C3.61600759,3.22402251 3.63999463,3.22437692 3.66397314,3.22508575 C3.69176119,3.22590043 3.72012236,3.22722855 3.74845755,3.22905289 C3.77692744,3.23089046 3.80498198,3.23319023 3.83299719,3.23597733 C3.86236278,3.23889105 3.89230728,3.24242516 3.92218997,3.24651769 C3.95842477,3.25149198 3.99379267,3.25714552 4.02904516,3.2635852 C4.04457753,3.26641925 4.06056799,3.26950351 4.07653203,3.27274998 C4.1217801,3.28195855 4.16647313,3.29238022 4.21089814,3.30408537 C4.22093231,3.3067264 4.23153789,3.30959531 4.24212737,3.31253756 C4.27196202,3.32083528 4.30106886,3.32952376 4.33003598,3.33877116 C4.35855924,3.347869 4.38751122,3.35771229 4.41630528,3.3681193 C4.42116985,3.36987869 4.42551008,3.37146263 4.42984665,3.3730594 C4.4761162,3.39008583 4.52241276,3.4087674 4.56821184,3.42893807 C4.59406406,3.44033198 4.61917606,3.45191971 4.64412424,3.46396063 C4.67111495,3.47697976 4.69839649,3.4907848 4.72546291,3.50513959 C4.75890801,3.52288219 4.79178851,3.54132453 4.82431475,3.56059431 C4.8374698,3.56838641 4.85073285,3.5764165 4.86393439,3.58458539 C4.89491851,3.60376145 4.92539479,3.6235868 4.95550936,3.64416832 C4.9772823,3.65904443 4.99913454,3.67451232 5.02078256,3.69038541 C5.03998798,3.70447076 5.05881967,3.71870909 5.07748715,3.73325923 C5.10440445,3.75423289 5.13126725,3.7760983 5.15775949,3.79862613 C5.1821715,3.81939236 5.20595148,3.84042939 5.22940861,3.86201411 C5.24512436,3.87647694 5.26059993,3.89109333 5.27592752,3.90595256 C5.28442786,3.91418351 5.29385225,3.92345739 5.30321896,3.9328241 L10.2031018,8.83270693 C10.255475,8.88508012 10.3065885,8.93859789 10.3564099,8.99321224 L10.2031018,8.83270693 C10.2748395,8.90444467 10.344214,8.97832987 10.4111413,9.05423915 C10.4223877,9.06699478 10.4335715,9.07981507 10.4446856,9.092692 C10.7663645,9.46539004 11.0297601,9.88553066 11.2252237,10.3388957 L11.6780206,11.3880225 L12.548286,13.4076516 C12.7467158,13.8678966 12.5344727,14.4018581 12.0742277,14.6002879 C11.9977866,14.6332447 11.9179446,14.6552159 11.836969,14.6662015 L11.7149387,14.6744406 C11.592625,14.6744406 11.4703113,14.6497231 11.3556497,14.6002879 L11.2340206,14.5480225 L9.33602055,13.7300225 L8.28689372,13.2772256 C7.83352871,13.081762 7.41338809,12.8183665 7.04069004,12.4966876 L7.0022372,12.4631433 C6.98177889,12.4451057 6.9614676,12.4268903 6.94130575,12.4084989 L7.04069004,12.4966876 C6.95122931,12.4194733 6.86450207,12.3389008 6.78070498,12.2551038 L1.88082214,7.35522092 C0.935753358,6.41015213 0.935753358,4.87789288 1.88082214,3.9328241 L1.90902055,3.90502251 L2.01192506,3.8109306 C2.19120357,3.65606766 2.38780913,3.5318516 2.59488381,3.4382824 C2.62872186,3.42311621 2.65522016,3.41182111 2.68187195,3.40102033 C2.76025666,3.36925866 2.83986347,3.34180278 2.92043821,3.31861145 L3.07557525,3.27946831 Z M9.58610551,9.95149698 L7.89951995,11.6381324 C8.10279642,11.805046 8.32371441,11.9494547 8.55841217,12.068738 L8.76594574,12.166096 L10.2570206,12.8090225 L10.7570206,12.3090225 L10.114094,10.8179477 C9.97930356,10.5053101 9.80144069,10.2137385 9.58610551,9.95149698 Z" id="Combined-Shape" fill-rule="nonzero"></path><rect id="Rectangle" opacity="0.005" x="0" y="0" width="16" height="16" rx="2"></rect></g><g id="Group" transform="translate(9, 1)"><rect id="Rectangle" x="2.25" y="0" width="1.5" height="6" rx="0.75"></rect><rect id="Rectangle-Copy" transform="translate(3, 3) rotate(90) translate(-3, -3)" x="2.25" y="1.13686838e-13" width="1.5" height="6" rx="0.75"></rect></g></g></svg>');function k4(t,e){let n=c(e,"mask",8,!1),r=c(e,"maskColor",8,"white");var i=_4(),o=A(p(i)),s=p(o),l=A(s);q(()=>{N(s,"fill",n()?r():"#168AFF"),N(l,"fill",n()?r():"#30D158")}),E(t,i)}var S4=u1('<svg width="16" height="16" viewBox="0 0 16 16" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink"><title>nextedit_deletion_light</title><g id="nextedit_deletion_light" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd"><g id="Pencil_Base"><path d="M3.07557525,3.27946831 C3.10738379,3.27258798 3.13664209,3.26682472 3.16597818,3.26160513 C3.19407786,3.25661079 3.22181021,3.25217747 3.24959807,3.24822758 C3.3431507,3.23490837 3.43787348,3.22705558 3.53270619,3.22474499 C3.54619312,3.22441336 3.56021661,3.22418981 3.57424082,3.22408741 L3.59202055,3.22402251 C3.61600759,3.22402251 3.63999463,3.22437692 3.66397314,3.22508575 C3.69176119,3.22590043 3.72012236,3.22722855 3.74845755,3.22905289 C3.77692744,3.23089046 3.80498198,3.23319023 3.83299719,3.23597733 C3.86236278,3.23889105 3.89230728,3.24242516 3.92218997,3.24651769 C3.95842477,3.25149198 3.99379267,3.25714552 4.02904516,3.2635852 C4.04457753,3.26641925 4.06056799,3.26950351 4.07653203,3.27274998 C4.1217801,3.28195855 4.16647313,3.29238022 4.21089814,3.30408537 C4.22093231,3.3067264 4.23153789,3.30959531 4.24212737,3.31253756 C4.27196202,3.32083528 4.30106886,3.32952376 4.33003598,3.33877116 C4.35855924,3.347869 4.38751122,3.35771229 4.41630528,3.3681193 C4.42116985,3.36987869 4.42551008,3.37146263 4.42984665,3.3730594 C4.4761162,3.39008583 4.52241276,3.4087674 4.56821184,3.42893807 C4.59406406,3.44033198 4.61917606,3.45191971 4.64412424,3.46396063 C4.67111495,3.47697976 4.69839649,3.4907848 4.72546291,3.50513959 C4.75890801,3.52288219 4.79178851,3.54132453 4.82431475,3.56059431 C4.8374698,3.56838641 4.85073285,3.5764165 4.86393439,3.58458539 C4.89491851,3.60376145 4.92539479,3.6235868 4.95550936,3.64416832 C4.9772823,3.65904443 4.99913454,3.67451232 5.02078256,3.69038541 C5.03998798,3.70447076 5.05881967,3.71870909 5.07748715,3.73325923 C5.10440445,3.75423289 5.13126725,3.7760983 5.15775949,3.79862613 C5.1821715,3.81939236 5.20595148,3.84042939 5.22940861,3.86201411 C5.24512436,3.87647694 5.26059993,3.89109333 5.27592752,3.90595256 C5.28442786,3.91418351 5.29385225,3.92345739 5.30321896,3.9328241 L10.2031018,8.83270693 C10.255475,8.88508012 10.3065885,8.93859789 10.3564099,8.99321224 L10.2031018,8.83270693 C10.2748395,8.90444467 10.344214,8.97832987 10.4111413,9.05423915 C10.4223877,9.06699478 10.4335715,9.07981507 10.4446856,9.092692 C10.7663645,9.46539004 11.0297601,9.88553066 11.2252237,10.3388957 L11.6780206,11.3880225 L12.548286,13.4076516 C12.7467158,13.8678966 12.5344727,14.4018581 12.0742277,14.6002879 C11.9977866,14.6332447 11.9179446,14.6552159 11.836969,14.6662015 L11.7149387,14.6744406 C11.592625,14.6744406 11.4703113,14.6497231 11.3556497,14.6002879 L11.2340206,14.5480225 L9.33602055,13.7300225 L8.28689372,13.2772256 C7.83352871,13.081762 7.41338809,12.8183665 7.04069004,12.4966876 L7.0022372,12.4631433 C6.98177889,12.4451057 6.9614676,12.4268903 6.94130575,12.4084989 L7.04069004,12.4966876 C6.95122931,12.4194733 6.86450207,12.3389008 6.78070498,12.2551038 L1.88082214,7.35522092 C0.935753358,6.41015213 0.935753358,4.87789288 1.88082214,3.9328241 L1.90902055,3.90502251 L2.01192506,3.8109306 C2.19120357,3.65606766 2.38780913,3.5318516 2.59488381,3.4382824 C2.62872186,3.42311621 2.65522016,3.41182111 2.68187195,3.40102033 C2.76025666,3.36925866 2.83986347,3.34180278 2.92043821,3.31861145 L3.07557525,3.27946831 Z M9.58610551,9.95149698 L7.89951995,11.6381324 C8.10279642,11.805046 8.32371441,11.9494547 8.55841217,12.068738 L8.76594574,12.166096 L10.2570206,12.8090225 L10.7570206,12.3090225 L10.114094,10.8179477 C9.97930356,10.5053101 9.80144069,10.2137385 9.58610551,9.95149698 Z" id="Combined-Shape" fill-rule="nonzero"></path><rect id="Rectangle" opacity="0.005" x="0" y="0" width="16" height="16" rx="2"></rect></g><g id="Group" transform="translate(9, 3.25)"><rect id="Rectangle-Copy" transform="translate(3, 0.75) rotate(90) translate(-3, -0.75)" x="2.25" y="-2.25" width="1.5" height="6" rx="0.75"></rect></g></g></svg>');function A4(t,e){let n=c(e,"mask",8,!1),r=c(e,"maskColor",8,"white");var i=S4(),o=A(p(i)),s=p(o),l=A(s);q(()=>{N(s,"fill",n()?r():"#0A84FF"),N(l,"fill",n()?r():"#FF5D4E")}),E(t,i)}var M4=u1('<svg width="16" height="16" viewBox="0 0 16 16" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink"><title>nextedit_deletion_dark</title><g id="nextedit_deletion_dark" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd"><g id="Pencil_Base"><path d="M3.07557525,3.27946831 C3.10738379,3.27258798 3.13664209,3.26682472 3.16597818,3.26160513 C3.19407786,3.25661079 3.22181021,3.25217747 3.24959807,3.24822758 C3.3431507,3.23490837 3.43787348,3.22705558 3.53270619,3.22474499 C3.54619312,3.22441336 3.56021661,3.22418981 3.57424082,3.22408741 L3.59202055,3.22402251 C3.61600759,3.22402251 3.63999463,3.22437692 3.66397314,3.22508575 C3.69176119,3.22590043 3.72012236,3.22722855 3.74845755,3.22905289 C3.77692744,3.23089046 3.80498198,3.23319023 3.83299719,3.23597733 C3.86236278,3.23889105 3.89230728,3.24242516 3.92218997,3.24651769 C3.95842477,3.25149198 3.99379267,3.25714552 4.02904516,3.2635852 C4.04457753,3.26641925 4.06056799,3.26950351 4.07653203,3.27274998 C4.1217801,3.28195855 4.16647313,3.29238022 4.21089814,3.30408537 C4.22093231,3.3067264 4.23153789,3.30959531 4.24212737,3.31253756 C4.27196202,3.32083528 4.30106886,3.32952376 4.33003598,3.33877116 C4.35855924,3.347869 4.38751122,3.35771229 4.41630528,3.3681193 C4.42116985,3.36987869 4.42551008,3.37146263 4.42984665,3.3730594 C4.4761162,3.39008583 4.52241276,3.4087674 4.56821184,3.42893807 C4.59406406,3.44033198 4.61917606,3.45191971 4.64412424,3.46396063 C4.67111495,3.47697976 4.69839649,3.4907848 4.72546291,3.50513959 C4.75890801,3.52288219 4.79178851,3.54132453 4.82431475,3.56059431 C4.8374698,3.56838641 4.85073285,3.5764165 4.86393439,3.58458539 C4.89491851,3.60376145 4.92539479,3.6235868 4.95550936,3.64416832 C4.9772823,3.65904443 4.99913454,3.67451232 5.02078256,3.69038541 C5.03998798,3.70447076 5.05881967,3.71870909 5.07748715,3.73325923 C5.10440445,3.75423289 5.13126725,3.7760983 5.15775949,3.79862613 C5.1821715,3.81939236 5.20595148,3.84042939 5.22940861,3.86201411 C5.24512436,3.87647694 5.26059993,3.89109333 5.27592752,3.90595256 C5.28442786,3.91418351 5.29385225,3.92345739 5.30321896,3.9328241 L10.2031018,8.83270693 C10.255475,8.88508012 10.3065885,8.93859789 10.3564099,8.99321224 L10.2031018,8.83270693 C10.2748395,8.90444467 10.344214,8.97832987 10.4111413,9.05423915 C10.4223877,9.06699478 10.4335715,9.07981507 10.4446856,9.092692 C10.7663645,9.46539004 11.0297601,9.88553066 11.2252237,10.3388957 L11.6780206,11.3880225 L12.548286,13.4076516 C12.7467158,13.8678966 12.5344727,14.4018581 12.0742277,14.6002879 C11.9977866,14.6332447 11.9179446,14.6552159 11.836969,14.6662015 L11.7149387,14.6744406 C11.592625,14.6744406 11.4703113,14.6497231 11.3556497,14.6002879 L11.2340206,14.5480225 L9.33602055,13.7300225 L8.28689372,13.2772256 C7.83352871,13.081762 7.41338809,12.8183665 7.04069004,12.4966876 L7.0022372,12.4631433 C6.98177889,12.4451057 6.9614676,12.4268903 6.94130575,12.4084989 L7.04069004,12.4966876 C6.95122931,12.4194733 6.86450207,12.3389008 6.78070498,12.2551038 L1.88082214,7.35522092 C0.935753358,6.41015213 0.935753358,4.87789288 1.88082214,3.9328241 L1.90902055,3.90502251 L2.01192506,3.8109306 C2.19120357,3.65606766 2.38780913,3.5318516 2.59488381,3.4382824 C2.62872186,3.42311621 2.65522016,3.41182111 2.68187195,3.40102033 C2.76025666,3.36925866 2.83986347,3.34180278 2.92043821,3.31861145 L3.07557525,3.27946831 Z M9.58610551,9.95149698 L7.89951995,11.6381324 C8.10279642,11.805046 8.32371441,11.9494547 8.55841217,12.068738 L8.76594574,12.166096 L10.2570206,12.8090225 L10.7570206,12.3090225 L10.114094,10.8179477 C9.97930356,10.5053101 9.80144069,10.2137385 9.58610551,9.95149698 Z" id="Combined-Shape" fill-rule="nonzero"></path><rect id="Rectangle" opacity="0.005" x="0" y="0" width="16" height="16" rx="2"></rect></g><g id="Group" transform="translate(9, 3.25)"><rect id="Rectangle-Copy" transform="translate(3, 0.75) rotate(90) translate(-3, -0.75)" x="2.25" y="-2.25" width="1.5" height="6" rx="0.75"></rect></g></g></svg>');function j4(t,e){let n=c(e,"mask",8,!1),r=c(e,"maskColor",8,"white");var i=M4(),o=A(p(i)),s=p(o),l=A(s);q(()=>{N(s,"fill",n()?r():"#168AFF"),N(l,"fill",n()?r():"#FF7E72")}),E(t,i)}var E4=u1('<svg width="16" height="16" viewBox="0 0 16 16" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink"><title>nextedit_change_light</title><g id="nextedit_change_light" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd"><g id="Pencil_Base"><path d="M3.07557525,3.27946831 C3.10738379,3.27258798 3.13664209,3.26682472 3.16597818,3.26160513 C3.19407786,3.25661079 3.22181021,3.25217747 3.24959807,3.24822758 C3.3431507,3.23490837 3.43787348,3.22705558 3.53270619,3.22474499 C3.54619312,3.22441336 3.56021661,3.22418981 3.57424082,3.22408741 L3.59202055,3.22402251 C3.61600759,3.22402251 3.63999463,3.22437692 3.66397314,3.22508575 C3.69176119,3.22590043 3.72012236,3.22722855 3.74845755,3.22905289 C3.77692744,3.23089046 3.80498198,3.23319023 3.83299719,3.23597733 C3.86236278,3.23889105 3.89230728,3.24242516 3.92218997,3.24651769 C3.95842477,3.25149198 3.99379267,3.25714552 4.02904516,3.2635852 C4.04457753,3.26641925 4.06056799,3.26950351 4.07653203,3.27274998 C4.1217801,3.28195855 4.16647313,3.29238022 4.21089814,3.30408537 C4.22093231,3.3067264 4.23153789,3.30959531 4.24212737,3.31253756 C4.27196202,3.32083528 4.30106886,3.32952376 4.33003598,3.33877116 C4.35855924,3.347869 4.38751122,3.35771229 4.41630528,3.3681193 C4.42116985,3.36987869 4.42551008,3.37146263 4.42984665,3.3730594 C4.4761162,3.39008583 4.52241276,3.4087674 4.56821184,3.42893807 C4.59406406,3.44033198 4.61917606,3.45191971 4.64412424,3.46396063 C4.67111495,3.47697976 4.69839649,3.4907848 4.72546291,3.50513959 C4.75890801,3.52288219 4.79178851,3.54132453 4.82431475,3.56059431 C4.8374698,3.56838641 4.85073285,3.5764165 4.86393439,3.58458539 C4.89491851,3.60376145 4.92539479,3.6235868 4.95550936,3.64416832 C4.9772823,3.65904443 4.99913454,3.67451232 5.02078256,3.69038541 C5.03998798,3.70447076 5.05881967,3.71870909 5.07748715,3.73325923 C5.10440445,3.75423289 5.13126725,3.7760983 5.15775949,3.79862613 C5.1821715,3.81939236 5.20595148,3.84042939 5.22940861,3.86201411 C5.24512436,3.87647694 5.26059993,3.89109333 5.27592752,3.90595256 C5.28442786,3.91418351 5.29385225,3.92345739 5.30321896,3.9328241 L10.2031018,8.83270693 C10.255475,8.88508012 10.3065885,8.93859789 10.3564099,8.99321224 L10.2031018,8.83270693 C10.2748395,8.90444467 10.344214,8.97832987 10.4111413,9.05423915 C10.4223877,9.06699478 10.4335715,9.07981507 10.4446856,9.092692 C10.7663645,9.46539004 11.0297601,9.88553066 11.2252237,10.3388957 L11.6780206,11.3880225 L12.548286,13.4076516 C12.7467158,13.8678966 12.5344727,14.4018581 12.0742277,14.6002879 C11.9977866,14.6332447 11.9179446,14.6552159 11.836969,14.6662015 L11.7149387,14.6744406 C11.592625,14.6744406 11.4703113,14.6497231 11.3556497,14.6002879 L11.2340206,14.5480225 L9.33602055,13.7300225 L8.28689372,13.2772256 C7.83352871,13.081762 7.41338809,12.8183665 7.04069004,12.4966876 L7.0022372,12.4631433 C6.98177889,12.4451057 6.9614676,12.4268903 6.94130575,12.4084989 L7.04069004,12.4966876 C6.95122931,12.4194733 6.86450207,12.3389008 6.78070498,12.2551038 L1.88082214,7.35522092 C0.935753358,6.41015213 0.935753358,4.87789288 1.88082214,3.9328241 L1.90902055,3.90502251 L2.01192506,3.8109306 C2.19120357,3.65606766 2.38780913,3.5318516 2.59488381,3.4382824 C2.62872186,3.42311621 2.65522016,3.41182111 2.68187195,3.40102033 C2.76025666,3.36925866 2.83986347,3.34180278 2.92043821,3.31861145 L3.07557525,3.27946831 Z M9.58610551,9.95149698 L7.89951995,11.6381324 C8.10279642,11.805046 8.32371441,11.9494547 8.55841217,12.068738 L8.76594574,12.166096 L10.2570206,12.8090225 L10.7570206,12.3090225 L10.114094,10.8179477 C9.97930356,10.5053101 9.80144069,10.2137385 9.58610551,9.95149698 Z" id="Combined-Shape" fill-rule="nonzero"></path><rect id="Rectangle" opacity="0.005" x="0" y="0" width="16" height="16" rx="2"></rect></g><g id="Group-2" transform="translate(10.5, 1.5)"><g id="Group" transform="translate(0, 1.5)"><rect id="Rectangle-Copy" transform="translate(2, 0.5) rotate(90) translate(-2, -0.5)" x="1.5" y="-1.5" width="1" height="4" rx="0.5"></rect><rect id="Rectangle-Copy" transform="translate(2, 4.5) rotate(90) translate(-2, -4.5)" x="1.5" y="2.5" width="1" height="4" rx="0.5"></rect></g><g id="Group" transform="translate(2, 2) rotate(90) translate(-2, -2)translate(0, 1.5)"><path d="M2,-1.5 C2.27614237,-1.5 2.5,-1.27614237 2.5,-1 L2.5,2 C2.5,2.27614237 2.27614237,2.5 2,2.5 C1.72385763,2.5 1.5,2.27614237 1.5,2 L1.5,-1 C1.5,-1.27614237 1.72385763,-1.5 2,-1.5 Z" id="Rectangle-Copy" transform="translate(2, 0.5) rotate(90) translate(-2, -0.5)"></path></g></g></g></svg>');function _2(t,e){let n=c(e,"mask",8,!1),r=c(e,"maskColor",8,"white");var i=E4(),o=A(p(i)),s=p(o),l=A(s);q(()=>{N(s,"fill",n()?r():"#0A84FF"),N(l,"fill",n()?r():"#F4A414")}),E(t,i)}var F4=u1('<svg width="16" height="16" viewBox="0 0 16 16" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink"><title>nextedit_change_dark</title><g id="nextedit_change_dark" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd"><g id="Pencil_Base"><path d="M3.07557525,3.27946831 C3.10738379,3.27258798 3.13664209,3.26682472 3.16597818,3.26160513 C3.19407786,3.25661079 3.22181021,3.25217747 3.24959807,3.24822758 C3.3431507,3.23490837 3.43787348,3.22705558 3.53270619,3.22474499 C3.54619312,3.22441336 3.56021661,3.22418981 3.57424082,3.22408741 L3.59202055,3.22402251 C3.61600759,3.22402251 3.63999463,3.22437692 3.66397314,3.22508575 C3.69176119,3.22590043 3.72012236,3.22722855 3.74845755,3.22905289 C3.77692744,3.23089046 3.80498198,3.23319023 3.83299719,3.23597733 C3.86236278,3.23889105 3.89230728,3.24242516 3.92218997,3.24651769 C3.95842477,3.25149198 3.99379267,3.25714552 4.02904516,3.2635852 C4.04457753,3.26641925 4.06056799,3.26950351 4.07653203,3.27274998 C4.1217801,3.28195855 4.16647313,3.29238022 4.21089814,3.30408537 C4.22093231,3.3067264 4.23153789,3.30959531 4.24212737,3.31253756 C4.27196202,3.32083528 4.30106886,3.32952376 4.33003598,3.33877116 C4.35855924,3.347869 4.38751122,3.35771229 4.41630528,3.3681193 C4.42116985,3.36987869 4.42551008,3.37146263 4.42984665,3.3730594 C4.4761162,3.39008583 4.52241276,3.4087674 4.56821184,3.42893807 C4.59406406,3.44033198 4.61917606,3.45191971 4.64412424,3.46396063 C4.67111495,3.47697976 4.69839649,3.4907848 4.72546291,3.50513959 C4.75890801,3.52288219 4.79178851,3.54132453 4.82431475,3.56059431 C4.8374698,3.56838641 4.85073285,3.5764165 4.86393439,3.58458539 C4.89491851,3.60376145 4.92539479,3.6235868 4.95550936,3.64416832 C4.9772823,3.65904443 4.99913454,3.67451232 5.02078256,3.69038541 C5.03998798,3.70447076 5.05881967,3.71870909 5.07748715,3.73325923 C5.10440445,3.75423289 5.13126725,3.7760983 5.15775949,3.79862613 C5.1821715,3.81939236 5.20595148,3.84042939 5.22940861,3.86201411 C5.24512436,3.87647694 5.26059993,3.89109333 5.27592752,3.90595256 C5.28442786,3.91418351 5.29385225,3.92345739 5.30321896,3.9328241 L10.2031018,8.83270693 C10.255475,8.88508012 10.3065885,8.93859789 10.3564099,8.99321224 L10.2031018,8.83270693 C10.2748395,8.90444467 10.344214,8.97832987 10.4111413,9.05423915 C10.4223877,9.06699478 10.4335715,9.07981507 10.4446856,9.092692 C10.7663645,9.46539004 11.0297601,9.88553066 11.2252237,10.3388957 L11.6780206,11.3880225 L12.548286,13.4076516 C12.7467158,13.8678966 12.5344727,14.4018581 12.0742277,14.6002879 C11.9977866,14.6332447 11.9179446,14.6552159 11.836969,14.6662015 L11.7149387,14.6744406 C11.592625,14.6744406 11.4703113,14.6497231 11.3556497,14.6002879 L11.2340206,14.5480225 L9.33602055,13.7300225 L8.28689372,13.2772256 C7.83352871,13.081762 7.41338809,12.8183665 7.04069004,12.4966876 L7.0022372,12.4631433 C6.98177889,12.4451057 6.9614676,12.4268903 6.94130575,12.4084989 L7.04069004,12.4966876 C6.95122931,12.4194733 6.86450207,12.3389008 6.78070498,12.2551038 L1.88082214,7.35522092 C0.935753358,6.41015213 0.935753358,4.87789288 1.88082214,3.9328241 L1.90902055,3.90502251 L2.01192506,3.8109306 C2.19120357,3.65606766 2.38780913,3.5318516 2.59488381,3.4382824 C2.62872186,3.42311621 2.65522016,3.41182111 2.68187195,3.40102033 C2.76025666,3.36925866 2.83986347,3.34180278 2.92043821,3.31861145 L3.07557525,3.27946831 Z M9.58610551,9.95149698 L7.89951995,11.6381324 C8.10279642,11.805046 8.32371441,11.9494547 8.55841217,12.068738 L8.76594574,12.166096 L10.2570206,12.8090225 L10.7570206,12.3090225 L10.114094,10.8179477 C9.97930356,10.5053101 9.80144069,10.2137385 9.58610551,9.95149698 Z" id="Combined-Shape" fill-rule="nonzero"></path><rect id="Rectangle" opacity="0.005" x="0" y="0" width="16" height="16" rx="2"></rect></g><g id="Group-2" transform="translate(10.5, 1.5)"><g id="Group" transform="translate(0, 1.5)"><rect id="Rectangle-Copy" transform="translate(2, 0.5) rotate(90) translate(-2, -0.5)" x="1.5" y="-1.5" width="1" height="4" rx="0.5"></rect><rect id="Rectangle-Copy" transform="translate(2, 4.5) rotate(90) translate(-2, -4.5)" x="1.5" y="2.5" width="1" height="4" rx="0.5"></rect></g><g id="Group" transform="translate(2, 2) rotate(90) translate(-2, -2)translate(0, 1.5)"><path d="M2,-1.5 C2.27614237,-1.5 2.5,-1.27614237 2.5,-1 L2.5,2 C2.5,2.27614237 2.27614237,2.5 2,2.5 C1.72385763,2.5 1.5,2.27614237 1.5,2 L1.5,-1 C1.5,-1.27614237 1.72385763,-1.5 2,-1.5 Z" id="Rectangle-Copy" transform="translate(2, 0.5) rotate(90) translate(-2, -0.5)"></path></g></g></g></svg>');function k2(t,e){let n=c(e,"mask",8,!1),r=c(e,"maskColor",8,"white");var i=F4(),o=A(p(i)),s=p(o),l=A(s);q(()=>{N(s,"fill",n()?r():"#168AFF"),N(l,"fill",n()?r():"#FFC255")}),E(t,i)}var O4=u1('<svg width="16" height="16" viewBox="0 0 16 16" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink"><title>nextedit_applied_light</title><g id="nextedit_applied_light" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd"><g id="􀆅" transform="translate(8.5216, 0.8311)" fill-rule="nonzero"><path d="M2.68994141,6.32666016 C3.01578776,6.32666016 3.26074219,6.2047526 3.42480469,5.9609375 L6.55908203,1.30566406 C6.61832682,1.21907552 6.66162109,1.13191732 6.68896484,1.04418945 C6.71630859,0.956461589 6.72998047,0.872721354 6.72998047,0.79296875 C6.72998047,0.567382812 6.65079753,0.37882487 6.49243164,0.227294922 C6.33406576,0.075764974 6.13867188,0 5.90625,0 C5.74902344,0 5.61572266,0.0313313802 5.50634766,0.0939941406 C5.39697266,0.156656901 5.29329427,0.263183594 5.1953125,0.413574219 L2.67626953,4.34765625 L1.42871094,2.91210938 C1.26692708,2.72753906 1.06184896,2.63525391 0.813476562,2.63525391 C0.578776042,2.63525391 0.384521484,2.71101888 0.230712891,2.86254883 C0.0769042969,3.01407878 0,3.20377604 0,3.43164063 C0,3.53417969 0.0165201823,3.62988281 0.0495605469,3.71875 C0.0826009115,3.80761719 0.143554688,3.90218099 0.232421875,4.00244141 L1.98925781,6.01904297 C2.16927083,6.22412109 2.40283203,6.32666016 2.68994141,6.32666016 Z" id="Path"></path></g><g id="Pencil_Base"><path d="M3.07557525,3.27946831 C3.10738379,3.27258798 3.13664209,3.26682472 3.16597818,3.26160513 C3.19407786,3.25661079 3.22181021,3.25217747 3.24959807,3.24822758 C3.3431507,3.23490837 3.43787348,3.22705558 3.53270619,3.22474499 C3.54619312,3.22441336 3.56021661,3.22418981 3.57424082,3.22408741 L3.59202055,3.22402251 C3.61600759,3.22402251 3.63999463,3.22437692 3.66397314,3.22508575 C3.69176119,3.22590043 3.72012236,3.22722855 3.74845755,3.22905289 C3.77692744,3.23089046 3.80498198,3.23319023 3.83299719,3.23597733 C3.86236278,3.23889105 3.89230728,3.24242516 3.92218997,3.24651769 C3.95842477,3.25149198 3.99379267,3.25714552 4.02904516,3.2635852 C4.04457753,3.26641925 4.06056799,3.26950351 4.07653203,3.27274998 C4.1217801,3.28195855 4.16647313,3.29238022 4.21089814,3.30408537 C4.22093231,3.3067264 4.23153789,3.30959531 4.24212737,3.31253756 C4.27196202,3.32083528 4.30106886,3.32952376 4.33003598,3.33877116 C4.35855924,3.347869 4.38751122,3.35771229 4.41630528,3.3681193 C4.42116985,3.36987869 4.42551008,3.37146263 4.42984665,3.3730594 C4.4761162,3.39008583 4.52241276,3.4087674 4.56821184,3.42893807 C4.59406406,3.44033198 4.61917606,3.45191971 4.64412424,3.46396063 C4.67111495,3.47697976 4.69839649,3.4907848 4.72546291,3.50513959 C4.75890801,3.52288219 4.79178851,3.54132453 4.82431475,3.56059431 C4.8374698,3.56838641 4.85073285,3.5764165 4.86393439,3.58458539 C4.89491851,3.60376145 4.92539479,3.6235868 4.95550936,3.64416832 C4.9772823,3.65904443 4.99913454,3.67451232 5.02078256,3.69038541 C5.03998798,3.70447076 5.05881967,3.71870909 5.07748715,3.73325923 C5.10440445,3.75423289 5.13126725,3.7760983 5.15775949,3.79862613 C5.1821715,3.81939236 5.20595148,3.84042939 5.22940861,3.86201411 C5.24512436,3.87647694 5.26059993,3.89109333 5.27592752,3.90595256 C5.28442786,3.91418351 5.29385225,3.92345739 5.30321896,3.9328241 L10.2031018,8.83270693 C10.255475,8.88508012 10.3065885,8.93859789 10.3564099,8.99321224 L10.2031018,8.83270693 C10.2748395,8.90444467 10.344214,8.97832987 10.4111413,9.05423915 C10.4223877,9.06699478 10.4335715,9.07981507 10.4446856,9.092692 C10.7663645,9.46539004 11.0297601,9.88553066 11.2252237,10.3388957 L11.6780206,11.3880225 L12.548286,13.4076516 C12.7467158,13.8678966 12.5344727,14.4018581 12.0742277,14.6002879 C11.9977866,14.6332447 11.9179446,14.6552159 11.836969,14.6662015 L11.7149387,14.6744406 C11.592625,14.6744406 11.4703113,14.6497231 11.3556497,14.6002879 L11.2340206,14.5480225 L9.33602055,13.7300225 L8.28689372,13.2772256 C7.83352871,13.081762 7.41338809,12.8183665 7.04069004,12.4966876 L7.0022372,12.4631433 C6.98177889,12.4451057 6.9614676,12.4268903 6.94130575,12.4084989 L7.04069004,12.4966876 C6.95122931,12.4194733 6.86450207,12.3389008 6.78070498,12.2551038 L1.88082214,7.35522092 C0.935753358,6.41015213 0.935753358,4.87789288 1.88082214,3.9328241 L1.90902055,3.90502251 L2.01192506,3.8109306 C2.19120357,3.65606766 2.38780913,3.5318516 2.59488381,3.4382824 C2.62872186,3.42311621 2.65522016,3.41182111 2.68187195,3.40102033 C2.76025666,3.36925866 2.83986347,3.34180278 2.92043821,3.31861145 L3.07557525,3.27946831 Z M9.58610551,9.95149698 L7.89951995,11.6381324 C8.10279642,11.805046 8.32371441,11.9494547 8.55841217,12.068738 L8.76594574,12.166096 L10.2570206,12.8090225 L10.7570206,12.3090225 L10.114094,10.8179477 C9.97930356,10.5053101 9.80144069,10.2137385 9.58610551,9.95149698 Z" id="Combined-Shape" fill-rule="nonzero"></path><rect id="Rectangle" opacity="0.005" x="0" y="0" width="16" height="16"></rect></g></g></svg>');function R4(t,e){let n=c(e,"mask",8,!1),r=c(e,"maskColor",8,"white");var i=O4(),o=A(p(i)),s=p(o),l=A(s);let C;q(u=>{N(s,"fill",n()?r():"#34C759"),N(l,"fill",n()?r():"#000000"),C=y1(l,"",C,u)},[()=>({opacity:n()?"1":"0.2"})],Y),E(t,i)}var $4=u1('<svg width="16" height="16" viewBox="0 0 16 16" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink"><title>nextedit_applied_dark</title><g id="nextedit_applied_dark" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd"><g id="􀆅" transform="translate(8.5167, 0.8311)" fill-rule="nonzero"><path d="M2.68994141,6.32666016 C3.01578776,6.32666016 3.26074219,6.2047526 3.42480469,5.9609375 L6.55908203,1.30566406 C6.61832682,1.21907552 6.66162109,1.13191732 6.68896484,1.04418945 C6.71630859,0.956461589 6.72998047,0.872721354 6.72998047,0.79296875 C6.72998047,0.567382812 6.65079753,0.37882487 6.49243164,0.227294922 C6.33406576,0.075764974 6.13867188,0 5.90625,0 C5.74902344,0 5.61572266,0.0313313802 5.50634766,0.0939941406 C5.39697266,0.156656901 5.29329427,0.263183594 5.1953125,0.413574219 L2.67626953,4.34765625 L1.42871094,2.91210938 C1.26692708,2.72753906 1.06184896,2.63525391 0.813476562,2.63525391 C0.578776042,2.63525391 0.384521484,2.71101888 0.230712891,2.86254883 C0.0769042969,3.01407878 0,3.20377604 0,3.43164063 C0,3.53417969 0.0165201823,3.62988281 0.0495605469,3.71875 C0.0826009115,3.80761719 0.143554688,3.90218099 0.232421875,4.00244141 L1.98925781,6.01904297 C2.16927083,6.22412109 2.40283203,6.32666016 2.68994141,6.32666016 Z" id="Path"></path></g><g id="Pencil_Base"><path d="M3.07557525,3.27946831 C3.10738379,3.27258798 3.13664209,3.26682472 3.16597818,3.26160513 C3.19407786,3.25661079 3.22181021,3.25217747 3.24959807,3.24822758 C3.3431507,3.23490837 3.43787348,3.22705558 3.53270619,3.22474499 C3.54619312,3.22441336 3.56021661,3.22418981 3.57424082,3.22408741 L3.59202055,3.22402251 C3.61600759,3.22402251 3.63999463,3.22437692 3.66397314,3.22508575 C3.69176119,3.22590043 3.72012236,3.22722855 3.74845755,3.22905289 C3.77692744,3.23089046 3.80498198,3.23319023 3.83299719,3.23597733 C3.86236278,3.23889105 3.89230728,3.24242516 3.92218997,3.24651769 C3.95842477,3.25149198 3.99379267,3.25714552 4.02904516,3.2635852 C4.04457753,3.26641925 4.06056799,3.26950351 4.07653203,3.27274998 C4.1217801,3.28195855 4.16647313,3.29238022 4.21089814,3.30408537 C4.22093231,3.3067264 4.23153789,3.30959531 4.24212737,3.31253756 C4.27196202,3.32083528 4.30106886,3.32952376 4.33003598,3.33877116 C4.35855924,3.347869 4.38751122,3.35771229 4.41630528,3.3681193 C4.42116985,3.36987869 4.42551008,3.37146263 4.42984665,3.3730594 C4.4761162,3.39008583 4.52241276,3.4087674 4.56821184,3.42893807 C4.59406406,3.44033198 4.61917606,3.45191971 4.64412424,3.46396063 C4.67111495,3.47697976 4.69839649,3.4907848 4.72546291,3.50513959 C4.75890801,3.52288219 4.79178851,3.54132453 4.82431475,3.56059431 C4.8374698,3.56838641 4.85073285,3.5764165 4.86393439,3.58458539 C4.89491851,3.60376145 4.92539479,3.6235868 4.95550936,3.64416832 C4.9772823,3.65904443 4.99913454,3.67451232 5.02078256,3.69038541 C5.03998798,3.70447076 5.05881967,3.71870909 5.07748715,3.73325923 C5.10440445,3.75423289 5.13126725,3.7760983 5.15775949,3.79862613 C5.1821715,3.81939236 5.20595148,3.84042939 5.22940861,3.86201411 C5.24512436,3.87647694 5.26059993,3.89109333 5.27592752,3.90595256 C5.28442786,3.91418351 5.29385225,3.92345739 5.30321896,3.9328241 L10.2031018,8.83270693 C10.255475,8.88508012 10.3065885,8.93859789 10.3564099,8.99321224 L10.2031018,8.83270693 C10.2748395,8.90444467 10.344214,8.97832987 10.4111413,9.05423915 C10.4223877,9.06699478 10.4335715,9.07981507 10.4446856,9.092692 C10.7663645,9.46539004 11.0297601,9.88553066 11.2252237,10.3388957 L11.6780206,11.3880225 L12.548286,13.4076516 C12.7467158,13.8678966 12.5344727,14.4018581 12.0742277,14.6002879 C11.9977866,14.6332447 11.9179446,14.6552159 11.836969,14.6662015 L11.7149387,14.6744406 C11.592625,14.6744406 11.4703113,14.6497231 11.3556497,14.6002879 L11.2340206,14.5480225 L9.33602055,13.7300225 L8.28689372,13.2772256 C7.83352871,13.081762 7.41338809,12.8183665 7.04069004,12.4966876 L7.0022372,12.4631433 C6.98177889,12.4451057 6.9614676,12.4268903 6.94130575,12.4084989 L7.04069004,12.4966876 C6.95122931,12.4194733 6.86450207,12.3389008 6.78070498,12.2551038 L1.88082214,7.35522092 C0.935753358,6.41015213 0.935753358,4.87789288 1.88082214,3.9328241 L1.90902055,3.90502251 L2.01192506,3.8109306 C2.19120357,3.65606766 2.38780913,3.5318516 2.59488381,3.4382824 C2.62872186,3.42311621 2.65522016,3.41182111 2.68187195,3.40102033 C2.76025666,3.36925866 2.83986347,3.34180278 2.92043821,3.31861145 L3.07557525,3.27946831 Z M9.58610551,9.95149698 L7.89951995,11.6381324 C8.10279642,11.805046 8.32371441,11.9494547 8.55841217,12.068738 L8.76594574,12.166096 L10.2570206,12.8090225 L10.7570206,12.3090225 L10.114094,10.8179477 C9.97930356,10.5053101 9.80144069,10.2137385 9.58610551,9.95149698 Z" id="Combined-Shape" fill-rule="nonzero"></path><rect id="Rectangle" opacity="0.005" x="0" y="0" width="16" height="16"></rect></g></g></svg>');function P4(t,e){let n=c(e,"mask",8,!1),r=c(e,"maskColor",8,"white");var i=$4(),o=A(p(i)),s=p(o),l=A(s);let C;q(u=>{N(s,"fill",n()?r():"#30D158"),N(l,"fill",n()?r():"#FFFFFF"),C=y1(l,"",C,u)},[()=>({opacity:n()?"1":"0.4"})],Y),E(t,i)}var z4=u1('<svg width="16" height="16" viewBox="0 0 16 16" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink"><title>nextedit_rejected_light</title><g id="nextedit_rejected_light" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd"><g id="Pencil_Base"><path d="M3.07557525,3.27946831 C3.10738379,3.27258798 3.13664209,3.26682472 3.16597818,3.26160513 C3.19407786,3.25661079 3.22181021,3.25217747 3.24959807,3.24822758 C3.3431507,3.23490837 3.43787348,3.22705558 3.53270619,3.22474499 C3.54619312,3.22441336 3.56021661,3.22418981 3.57424082,3.22408741 L3.59202055,3.22402251 C3.61600759,3.22402251 3.63999463,3.22437692 3.66397314,3.22508575 C3.69176119,3.22590043 3.72012236,3.22722855 3.74845755,3.22905289 C3.77692744,3.23089046 3.80498198,3.23319023 3.83299719,3.23597733 C3.86236278,3.23889105 3.89230728,3.24242516 3.92218997,3.24651769 C3.95842477,3.25149198 3.99379267,3.25714552 4.02904516,3.2635852 C4.04457753,3.26641925 4.06056799,3.26950351 4.07653203,3.27274998 C4.1217801,3.28195855 4.16647313,3.29238022 4.21089814,3.30408537 C4.22093231,3.3067264 4.23153789,3.30959531 4.24212737,3.31253756 C4.27196202,3.32083528 4.30106886,3.32952376 4.33003598,3.33877116 C4.35855924,3.347869 4.38751122,3.35771229 4.41630528,3.3681193 C4.42116985,3.36987869 4.42551008,3.37146263 4.42984665,3.3730594 C4.4761162,3.39008583 4.52241276,3.4087674 4.56821184,3.42893807 C4.59406406,3.44033198 4.61917606,3.45191971 4.64412424,3.46396063 C4.67111495,3.47697976 4.69839649,3.4907848 4.72546291,3.50513959 C4.75890801,3.52288219 4.79178851,3.54132453 4.82431475,3.56059431 C4.8374698,3.56838641 4.85073285,3.5764165 4.86393439,3.58458539 C4.89491851,3.60376145 4.92539479,3.6235868 4.95550936,3.64416832 C4.9772823,3.65904443 4.99913454,3.67451232 5.02078256,3.69038541 C5.03998798,3.70447076 5.05881967,3.71870909 5.07748715,3.73325923 C5.10440445,3.75423289 5.13126725,3.7760983 5.15775949,3.79862613 C5.1821715,3.81939236 5.20595148,3.84042939 5.22940861,3.86201411 C5.24512436,3.87647694 5.26059993,3.89109333 5.27592752,3.90595256 C5.28442786,3.91418351 5.29385225,3.92345739 5.30321896,3.9328241 L10.2031018,8.83270693 C10.255475,8.88508012 10.3065885,8.93859789 10.3564099,8.99321224 L10.2031018,8.83270693 C10.2748395,8.90444467 10.344214,8.97832987 10.4111413,9.05423915 C10.4223877,9.06699478 10.4335715,9.07981507 10.4446856,9.092692 C10.7663645,9.46539004 11.0297601,9.88553066 11.2252237,10.3388957 L11.6780206,11.3880225 L12.548286,13.4076516 C12.7467158,13.8678966 12.5344727,14.4018581 12.0742277,14.6002879 C11.9977866,14.6332447 11.9179446,14.6552159 11.836969,14.6662015 L11.7149387,14.6744406 C11.592625,14.6744406 11.4703113,14.6497231 11.3556497,14.6002879 L11.2340206,14.5480225 L9.33602055,13.7300225 L8.28689372,13.2772256 C7.83352871,13.081762 7.41338809,12.8183665 7.04069004,12.4966876 L7.0022372,12.4631433 C6.98177889,12.4451057 6.9614676,12.4268903 6.94130575,12.4084989 L7.04069004,12.4966876 C6.95122931,12.4194733 6.86450207,12.3389008 6.78070498,12.2551038 L1.88082214,7.35522092 C0.935753358,6.41015213 0.935753358,4.87789288 1.88082214,3.9328241 L1.90902055,3.90502251 L2.01192506,3.8109306 C2.19120357,3.65606766 2.38780913,3.5318516 2.59488381,3.4382824 C2.62872186,3.42311621 2.65522016,3.41182111 2.68187195,3.40102033 C2.76025666,3.36925866 2.83986347,3.34180278 2.92043821,3.31861145 L3.07557525,3.27946831 Z M9.58610551,9.95149698 L7.89951995,11.6381324 C8.10279642,11.805046 8.32371441,11.9494547 8.55841217,12.068738 L8.76594574,12.166096 L10.2570206,12.8090225 L10.7570206,12.3090225 L10.114094,10.8179477 C9.97930356,10.5053101 9.80144069,10.2137385 9.58610551,9.95149698 Z" id="Combined-Shape" fill-rule="nonzero"></path><rect id="Rectangle" opacity="0.005" x="0" y="0" width="16" height="16" rx="2"></rect></g><g id="Group" transform="translate(12, 4) rotate(45) translate(-12, -4)translate(9, 1)"><rect id="Rectangle" x="2.25" y="0" width="1.5" height="6" rx="0.75"></rect><rect id="Rectangle-Copy" transform="translate(3, 3) rotate(90) translate(-3, -3)" x="2.25" y="1.13686838e-13" width="1.5" height="6" rx="0.75"></rect></g></g></svg>');function B4(t,e){let n=c(e,"mask",8,!0),r=c(e,"maskColor",8,"white");var i=z4(),o=A(p(i)),s=p(o),l=A(s);q(()=>{N(s,"fill",n()?r():"#0A84FF"),N(l,"fill",n()?r():"#FF5D4E")}),E(t,i)}var N4=u1('<svg width="16" height="16" viewBox="0 0 16 16" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink"><title>nextedit_rejected_dark</title><g id="nextedit_rejected_dark" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd"><g id="Pencil_Base"><path d="M3.07557525,3.27946831 C3.10738379,3.27258798 3.13664209,3.26682472 3.16597818,3.26160513 C3.19407786,3.25661079 3.22181021,3.25217747 3.24959807,3.24822758 C3.3431507,3.23490837 3.43787348,3.22705558 3.53270619,3.22474499 C3.54619312,3.22441336 3.56021661,3.22418981 3.57424082,3.22408741 L3.59202055,3.22402251 C3.61600759,3.22402251 3.63999463,3.22437692 3.66397314,3.22508575 C3.69176119,3.22590043 3.72012236,3.22722855 3.74845755,3.22905289 C3.77692744,3.23089046 3.80498198,3.23319023 3.83299719,3.23597733 C3.86236278,3.23889105 3.89230728,3.24242516 3.92218997,3.24651769 C3.95842477,3.25149198 3.99379267,3.25714552 4.02904516,3.2635852 C4.04457753,3.26641925 4.06056799,3.26950351 4.07653203,3.27274998 C4.1217801,3.28195855 4.16647313,3.29238022 4.21089814,3.30408537 C4.22093231,3.3067264 4.23153789,3.30959531 4.24212737,3.31253756 C4.27196202,3.32083528 4.30106886,3.32952376 4.33003598,3.33877116 C4.35855924,3.347869 4.38751122,3.35771229 4.41630528,3.3681193 C4.42116985,3.36987869 4.42551008,3.37146263 4.42984665,3.3730594 C4.4761162,3.39008583 4.52241276,3.4087674 4.56821184,3.42893807 C4.59406406,3.44033198 4.61917606,3.45191971 4.64412424,3.46396063 C4.67111495,3.47697976 4.69839649,3.4907848 4.72546291,3.50513959 C4.75890801,3.52288219 4.79178851,3.54132453 4.82431475,3.56059431 C4.8374698,3.56838641 4.85073285,3.5764165 4.86393439,3.58458539 C4.89491851,3.60376145 4.92539479,3.6235868 4.95550936,3.64416832 C4.9772823,3.65904443 4.99913454,3.67451232 5.02078256,3.69038541 C5.03998798,3.70447076 5.05881967,3.71870909 5.07748715,3.73325923 C5.10440445,3.75423289 5.13126725,3.7760983 5.15775949,3.79862613 C5.1821715,3.81939236 5.20595148,3.84042939 5.22940861,3.86201411 C5.24512436,3.87647694 5.26059993,3.89109333 5.27592752,3.90595256 C5.28442786,3.91418351 5.29385225,3.92345739 5.30321896,3.9328241 L10.2031018,8.83270693 C10.255475,8.88508012 10.3065885,8.93859789 10.3564099,8.99321224 L10.2031018,8.83270693 C10.2748395,8.90444467 10.344214,8.97832987 10.4111413,9.05423915 C10.4223877,9.06699478 10.4335715,9.07981507 10.4446856,9.092692 C10.7663645,9.46539004 11.0297601,9.88553066 11.2252237,10.3388957 L11.6780206,11.3880225 L12.548286,13.4076516 C12.7467158,13.8678966 12.5344727,14.4018581 12.0742277,14.6002879 C11.9977866,14.6332447 11.9179446,14.6552159 11.836969,14.6662015 L11.7149387,14.6744406 C11.592625,14.6744406 11.4703113,14.6497231 11.3556497,14.6002879 L11.2340206,14.5480225 L9.33602055,13.7300225 L8.28689372,13.2772256 C7.83352871,13.081762 7.41338809,12.8183665 7.04069004,12.4966876 L7.0022372,12.4631433 C6.98177889,12.4451057 6.9614676,12.4268903 6.94130575,12.4084989 L7.04069004,12.4966876 C6.95122931,12.4194733 6.86450207,12.3389008 6.78070498,12.2551038 L1.88082214,7.35522092 C0.935753358,6.41015213 0.935753358,4.87789288 1.88082214,3.9328241 L1.90902055,3.90502251 L2.01192506,3.8109306 C2.19120357,3.65606766 2.38780913,3.5318516 2.59488381,3.4382824 C2.62872186,3.42311621 2.65522016,3.41182111 2.68187195,3.40102033 C2.76025666,3.36925866 2.83986347,3.34180278 2.92043821,3.31861145 L3.07557525,3.27946831 Z M9.58610551,9.95149698 L7.89951995,11.6381324 C8.10279642,11.805046 8.32371441,11.9494547 8.55841217,12.068738 L8.76594574,12.166096 L10.2570206,12.8090225 L10.7570206,12.3090225 L10.114094,10.8179477 C9.97930356,10.5053101 9.80144069,10.2137385 9.58610551,9.95149698 Z" id="Combined-Shape" fill-rule="nonzero"></path><rect id="Rectangle" opacity="0.005" x="0" y="0" width="16" height="16" rx="2"></rect></g><g id="Group" transform="translate(12, 4) rotate(45) translate(-12, -4)translate(9, 1)"><rect id="Rectangle" x="2.25" y="0" width="1.5" height="6" rx="0.75"></rect><rect id="Rectangle-Copy" transform="translate(3, 3) rotate(90) translate(-3, -3)" x="2.25" y="1.13686838e-13" width="1.5" height="6" rx="0.75"></rect></g></g></svg>');function Z4(t,e){let n=c(e,"mask",8,!0),r=c(e,"maskColor",8,"white");var i=N4(),o=A(p(i)),s=p(o),l=A(s);q(()=>{N(s,"fill",n()?r():"#168AFF"),N(l,"fill",n()?r():"#FF7E72")}),E(t,i)}var I4=u1('<svg width="16" height="16" viewBox="0 0 16 16" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink"><title>Option 2_light</title><g id="Option-2_light" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd"><path d="M11.0070258,6 C11.1334895,6 11.2318501,5.90866511 11.2529274,5.76814988 C11.5409836,3.95550351 11.8641686,3.52693208 13.7751756,3.2529274 C13.9156909,3.23185012 14,3.13348946 14,3 C14,2.8735363 13.9156909,2.77517564 13.7751756,2.75409836 C11.8571429,2.48009368 11.618267,2.07259953 11.2529274,0.217798595 C11.2248244,0.0913348946 11.1334895,4.88498131e-15 11.0070258,4.88498131e-15 C10.8735363,4.88498131e-15 10.7751756,0.0913348946 10.7540984,0.224824356 C10.4660422,2.07259953 10.1498829,2.48009368 8.23887588,2.75409836 C8.09836066,2.78220141 8.00702576,2.8735363 8.00702576,3 C8.00702576,3.13348946 8.09836066,3.23185012 8.23887588,3.2529274 C10.1569087,3.52693208 10.4028103,3.92740047 10.7540984,5.77517564 C10.7822014,5.91569087 10.8805621,6 11.0070258,6 Z" id="Path"></path><g id="Pencil" transform="translate(0.172, 2.224)"><path d="M4.81096943,2.24362978 L8.60328164,6.03648403 C8.98206926,6.41527164 9.28555422,6.86248408 9.4976383,7.35439917 L10.8207006,10.4231551 L7.35439917,9.4976383 C6.86248408,9.28555422 6.41527164,8.98206926 6.03645345,8.60325107 L2.24362978,4.81096943 L4.81096943,2.24362978 Z M2.40551485,0.605057322 C2.66486762,0.603004601 2.92459099,0.65617863 3.16550077,0.764612139 L0.782130647,3.14739928 C0.671050497,2.89397491 0.616248167,2.62929203 0.618310403,2.36812709 C0.621891891,1.91456136 0.798131359,1.47507103 1.13660119,1.13660119 C1.48731739,0.785884996 1.94585368,0.608695442 2.40551485,0.605057322 Z" id="Combined-Shape" stroke-width="1.21"></path><path d="M10.506,8.164 L11.3762654,10.1836291 C11.5746952,10.6438741 11.3624521,11.1778356 10.9022072,11.3762654 C10.6728839,11.4751357 10.4129523,11.4751357 10.1836291,11.3762654 L8.164,10.506 L10.506,8.164 Z M4.13119841,0.708801589 L9.03108125,5.60868442 C9.11487834,5.69248152 9.19545077,5.77920876 9.27266509,5.86866949 L5.86866949,9.27266509 C5.77920876,9.19545077 5.69248152,9.11487834 5.60868442,9.03108125 L0.708801589,4.13119841 C-0.236267196,3.18612962 -0.236267196,1.65387038 0.708801589,0.708801589 C1.65387038,-0.236267196 3.18612962,-0.236267196 4.13119841,0.708801589 Z" id="Combined-Shape"></path></g></g></svg>');function D4(t,e){let n=c(e,"mask",8,!1),r=c(e,"maskColor",8,"white");var i=I4(),o=A(p(i)),s=p(o),l=A(s),C=p(l),u=A(C);q(()=>{N(s,"fill",n()?r():"#007AFF"),N(C,"stroke",n()?r():"#007AFF"),N(u,"fill",n()?r():"#007AFF")}),E(t,i)}var K4=u1('<svg width="16" height="16" viewBox="0 0 16 16" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink"><title>Option 2_dark</title><g id="Option-2_dark" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd"><path d="M11.0070258,6 C11.1334895,6 11.2318501,5.90866511 11.2529274,5.76814988 C11.5409836,3.95550351 11.8641686,3.52693208 13.7751756,3.2529274 C13.9156909,3.23185012 14,3.13348946 14,3 C14,2.8735363 13.9156909,2.77517564 13.7751756,2.75409836 C11.8571429,2.48009368 11.618267,2.07259953 11.2529274,0.217798595 C11.2248244,0.0913348946 11.1334895,4.88498131e-15 11.0070258,4.88498131e-15 C10.8735363,4.88498131e-15 10.7751756,0.0913348946 10.7540984,0.224824356 C10.4660422,2.07259953 10.1498829,2.48009368 8.23887588,2.75409836 C8.09836066,2.78220141 8.00702576,2.8735363 8.00702576,3 C8.00702576,3.13348946 8.09836066,3.23185012 8.23887588,3.2529274 C10.1569087,3.52693208 10.4028103,3.92740047 10.7540984,5.77517564 C10.7822014,5.91569087 10.8805621,6 11.0070258,6 Z" id="Path"></path><g id="Pencil" transform="translate(0.172, 2.224)"><path d="M4.81096943,2.24362978 L8.60328164,6.03648403 C8.98206926,6.41527164 9.28555422,6.86248408 9.4976383,7.35439917 L10.8207006,10.4231551 L7.35439917,9.4976383 C6.86248408,9.28555422 6.41527164,8.98206926 6.03645345,8.60325107 L2.24362978,4.81096943 L4.81096943,2.24362978 Z M2.40551485,0.605057322 C2.66486762,0.603004601 2.92459099,0.65617863 3.16550077,0.764612139 L0.782130647,3.14739928 C0.671050497,2.89397491 0.616248167,2.62929203 0.618310403,2.36812709 C0.621891891,1.91456136 0.798131359,1.47507103 1.13660119,1.13660119 C1.48731739,0.785884996 1.94585368,0.608695442 2.40551485,0.605057322 Z" id="Combined-Shape" stroke-width="1.21"></path><path d="M10.506,8.164 L11.3762654,10.1836291 C11.5746952,10.6438741 11.3624521,11.1778356 10.9022072,11.3762654 C10.6728839,11.4751357 10.4129523,11.4751357 10.1836291,11.3762654 L8.164,10.506 L10.506,8.164 Z M4.13119841,0.708801589 L9.03108125,5.60868442 C9.11487834,5.69248152 9.19545077,5.77920876 9.27266509,5.86866949 L5.86866949,9.27266509 C5.77920876,9.19545077 5.69248152,9.11487834 5.60868442,9.03108125 L0.708801589,4.13119841 C-0.236267196,3.18612962 -0.236267196,1.65387038 0.708801589,0.708801589 C1.65387038,-0.236267196 3.18612962,-0.236267196 4.13119841,0.708801589 Z" id="Combined-Shape"></path></g></g></svg>');function T4(t,e){let n=c(e,"mask",8,!1),r=c(e,"maskColor",8,"white");var i=K4(),o=A(p(i)),s=p(o),l=A(s),C=p(l),u=A(C);q(()=>{N(s,"fill",n()?r():"#BF5AF2"),N(C,"stroke",n()?r():"#389BFF"),N(u,"fill",n()?r():"#389BFF")}),E(t,i)}var V4=u1('<svg width="16" height="16" viewBox="0 0 16 16" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink"><title>Option 2_Inactive_light</title><g id="Option-2_Inactive_light" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd"><path d="M11.0070258,6 C11.1334895,6 11.2318501,5.90866511 11.2529274,5.76814988 C11.5409836,3.95550351 11.8641686,3.52693208 13.7751756,3.2529274 C13.9156909,3.23185012 14,3.13348946 14,3 C14,2.8735363 13.9156909,2.77517564 13.7751756,2.75409836 C11.8571429,2.48009368 11.618267,2.07259953 11.2529274,0.217798595 C11.2248244,0.0913348946 11.1334895,4.88498131e-15 11.0070258,4.88498131e-15 C10.8735363,4.88498131e-15 10.7751756,0.0913348946 10.7540984,0.224824356 C10.4660422,2.07259953 10.1498829,2.48009368 8.23887588,2.75409836 C8.09836066,2.78220141 8.00702576,2.8735363 8.00702576,3 C8.00702576,3.13348946 8.09836066,3.23185012 8.23887588,3.2529274 C10.1569087,3.52693208 10.4028103,3.92740047 10.7540984,5.77517564 C10.7822014,5.91569087 10.8805621,6 11.0070258,6 Z" id="Path"></path><g id="Pencil" transform="translate(0.172, 2.224)" stroke-width="1.21"><path d="M2.42,0.605 C2.88449901,0.605 3.34899801,0.782200397 3.70339881,1.13660119 L8.60328164,6.03648403 C8.98206926,6.41527164 9.28555422,6.86248408 9.4976383,7.35439917 L10.8207006,10.4231551 L7.35439917,9.4976383 C6.86248408,9.28555422 6.41527164,8.98206926 6.03648403,8.60328164 L1.13660119,3.70339881 C0.782200397,3.34899801 0.605,2.88449901 0.605,2.42 C0.605,1.95550099 0.782200397,1.49100199 1.13660119,1.13660119 C1.49100199,0.782200397 1.95550099,0.605 2.42,0.605 Z" id="Combined-Shape"></path></g></g></svg>');function W4(t,e){let n=c(e,"mask",8,!1),r=c(e,"maskColor",8,"white");var i=V4(),o=A(p(i)),s=p(o),l=A(s);q(()=>{N(s,"fill",n()?r():"#BF5AF2"),N(l,"stroke",n()?r():"#007AFF")}),E(t,i)}var G4=u1('<svg width="16" height="16" viewBox="0 0 16 16" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink"><title>Option 2_inactive_dark</title><g id="Option-2_inactive_dark" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd"><path d="M11.0070258,6 C11.1334895,6 11.2318501,5.90866511 11.2529274,5.76814988 C11.5409836,3.95550351 11.8641686,3.52693208 13.7751756,3.2529274 C13.9156909,3.23185012 14,3.13348946 14,3 C14,2.8735363 13.9156909,2.77517564 13.7751756,2.75409836 C11.8571429,2.48009368 11.618267,2.07259953 11.2529274,0.217798595 C11.2248244,0.0913348946 11.1334895,4.88498131e-15 11.0070258,4.88498131e-15 C10.8735363,4.88498131e-15 10.7751756,0.0913348946 10.7540984,0.224824356 C10.4660422,2.07259953 10.1498829,2.48009368 8.23887588,2.75409836 C8.09836066,2.78220141 8.00702576,2.8735363 8.00702576,3 C8.00702576,3.13348946 8.09836066,3.23185012 8.23887588,3.2529274 C10.1569087,3.52693208 10.4028103,3.92740047 10.7540984,5.77517564 C10.7822014,5.91569087 10.8805621,6 11.0070258,6 Z" id="Path"></path><g id="Pencil" transform="translate(0.172, 2.224)" stroke-width="1.21"><path d="M2.42,0.605 C2.88449901,0.605 3.34899801,0.782200397 3.70339881,1.13660119 L8.60328164,6.03648403 C8.98206926,6.41527164 9.28555422,6.86248408 9.4976383,7.35439917 L10.8207006,10.4231551 L7.35439917,9.4976383 C6.86248408,9.28555422 6.41527164,8.98206926 6.03648403,8.60328164 L1.13660119,3.70339881 C0.782200397,3.34899801 0.605,2.88449901 0.605,2.42 C0.605,1.95550099 0.782200397,1.49100199 1.13660119,1.13660119 C1.49100199,0.782200397 1.95550099,0.605 2.42,0.605 Z" id="Combined-Shape"></path></g></g></svg>');function U4(t,e){let n=c(e,"mask",8,!1),r=c(e,"maskColor",8,"white");var i=G4(),o=A(p(i)),s=p(o),l=A(s);q(()=>{N(s,"fill",n()?r():"#BF5AF2"),N(l,"stroke",n()?r():"#389BFF")}),E(t,i)}var q4=H('<span class="c-pencil-icon svelte-6fsamc"><!> <!></span>');function v0(t,e){C1(e,!1);const[n,r]=O1(),i=()=>x1(i0,"$themeStore",n),o=$(),s=$(),l={insertion:{light:b4,dark:k4},deletion:{light:A4,dark:j4},modification:{light:_2,dark:k2},noop:{light:_2,dark:k2},active:{light:D4,dark:T4},inactive:{light:W4,dark:U4},accepted:{light:R4,dark:P4},rejected:{light:B4,dark:Z4}};let C=c(e,"mask",8,!1),u=c(e,"maskColor",8,"currentColor"),d=c(e,"suggestion",8),_=c(e,"themeCategory",28,()=>{}),L=$();Q(()=>(i(),S(_())),()=>{var j;(j=i())!=null&&j.category&&_(_()??i().category)}),Q(()=>(S(d()),J1),()=>{M(L,function(j){return new S3(j,J3)}(d()).with({state:J1.stale},{state:J1.accepted},()=>"accepted").otherwise(({changeType:j})=>j))}),Q(()=>a(L),()=>{M(o,l[a(L)]??l.active)}),Q(()=>(a(o),S(_()),C2),()=>{M(s,a(o)[_()??C2.light])}),R1(),p1();var f=q4(),w=p(f);o4(w,()=>a(s),(j,x)=>{x(j,{get mask(){return C()},get maskColor(){return u()}})});var m=A(w,2);s3(m,e,"default",{},null),E(t,f),v1(),r()}var H4=H('<button><!> <span class="c-suggestion-tree-item-button__description svelte-hekzdv"><span class="c-suggestion-tree-item__description__linenumber"> </span> <span class="c-suggestion-tree-item__description__path svelte-hekzdv"> </span></span></button>'),J4=H('<li class="c-suggestion-tree-item__suggestion svelte-3abz9e"><!> <!></li>');function Q4(t,e){C1(e,!1);const[n,r]=O1(),i=()=>x1(_,"$ctx",n),o=$();let s=c(e,"suggestion",8),l=c(e,"onCodeAction",8),C=c(e,"shouldScrollIntoView",8,!1),u=c(e,"scrollContainer",8),d=$(!1),_=c3(),L=c(e,"state",28,()=>E1(s(),i(),!0));Q(()=>(S(s()),i()),()=>{L(E1(s(),i(),!0))}),Q(()=>(S(s()),T1),()=>{M(o,s().state===J1.accepted?T1("reject","undo"):T1("reject","accept"))}),R1(),p1();var f=J4(),w=p(f);(function(x,v){C1(v,!1);const[P,h]=O1(),F=()=>x1(g,"$ctx",P);let O=c(v,"suggestion",8),k=c(v,"onCodeAction",8);const g=c3();let b=$(E1(O(),F(),!0));Q(()=>(S(O()),F()),()=>{M(b,E1(O(),F(),!0))}),R1(),p1();var z=H4(),c1=h1(()=>A3("Space",()=>k()("select",O())));N(z,"tabindex",0);var I=p(z);const D=Y(()=>a(b)!=="none");v0(I,{get mask(){return a(D)},get suggestion(){return O()}});var J=A(I,2),K=p(J),W=p(K),r1=A(K,2),b1=p(r1);q((X,T)=>{N(z,"title",`${X??""}:${T??""}`),Q1(z,1,`c-suggestion-tree-item-button ${a(b)??""}`,"svelte-hekzdv"),U3(W,`${S(O()),R(()=>O().lineRange.start+1)??""}:`),U3(b1,(S(O()),R(()=>O().result.changeDescription)))},[()=>(S(p2),S(O()),R(()=>p2(O(),!0))),()=>(S(v2),S(O()),R(()=>v2(O())))],Y),l1("click",z,function(){k()("select",O())}),l1("dblclick",z,function(){A1(O(),F().activeSuggestion)?k()("dismiss"):k()("active",O())}),l1("keydown",z,function(...X){var T;(T=a(c1))==null||T.apply(this,X)}),E(x,z),v1(),h()})(w,{get suggestion(){return s()},get onCodeAction(){return l()}});var m=A(w,2),j=x=>{J0(x,{get onCodeAction(){return l()},get codeActions(){return a(o)},get value(){return s()}})};H1(m,x=>{a(d)&&x(j)}),l3(f,(x,v)=>F1==null?void 0:F1(x,v),()=>({scrollContainer:u(),doScroll:C(),scrollIntoView:{behavior:"smooth",block:"nearest"}})),j1(()=>l1("mouseenter",f,()=>M(d,!0))),j1(()=>l1("mouseleave",f,()=>M(d,!1))),q(x=>y1(f,x),[()=>(S(_3),S(L()),R(()=>_3(L())))],Y),d0(3,f,()=>s0),E(t,f),v1(),r()}var Y4=H('<ul class="c-suggestion-tree-item__inner svelte-bnynfs"></ul>'),X4=H('<li class="svelte-bnynfs"><!></li>'),t5=H("<ul></ul>"),e5=H("<!> <!> <div><!></div>",1);function n5(t,e){C1(e,!1);const[n,r]=O1(),i=()=>x1(u,"$ctx",n);let o=c(e,"onCodeAction",8),s=c(e,"sortedPathSuggestionsMap",24,()=>new Map),l=c(e,"minimized",8,!1),C=$();const u=c3();function d(h,F=!0){h!=null&&h.qualifiedPathName.relPath&&(U1(u,R(i).nextSuggestion=h,R(i)),U1(u,R(i).isOpen={...i().isOpen,[h.qualifiedPathName.relPath]:F},R(i)))}function _(h,F){h.preventDefault(),h.stopPropagation();const O=q1(i()),k=Q0(s(),O),g=c0(s(),k+(F?-1:1));g&&g!==O&&(d(g),y3(i())==="active"?o()("active",g):o()("select",g))}Q(()=>(i(),a(C)),()=>{i().nextSuggestion&&y3(i())==="next"&&!A1(i().nextSuggestion,a(C))&&(M(C,i().nextSuggestion),d(i().nextSuggestion))}),R1(),p1();var L=e5();l1("keydown",T0.body,function(h){switch(h.code){case"KeyZ":if(!h.metaKey&&!h.ctrlKey||(h.preventDefault(),h.stopPropagation(),!i().nextSuggestion))return;d(i().nextSuggestion),o()("undo",i().nextSuggestion);break;case"KeyK":case"ArrowUp":if(h.metaKey||h.altKey||h.ctrlKey||h.shiftKey)return;_(h,!0);break;case"KeyJ":case"ArrowDown":if(h.metaKey||h.altKey||h.ctrlKey||h.shiftKey)return;_(h);break;case"ArrowRight":case"Space":{if(h.metaKey||h.altKey||h.ctrlKey||h.shiftKey)return;h.preventDefault(),h.stopPropagation();const F=q1(i());d(F),o()(y3(i())==="select"?"active":"select",F);break}case"ArrowLeft":case"Escape":if(h.metaKey||h.altKey||h.ctrlKey||h.shiftKey)return;h.preventDefault(),h.stopPropagation(),o()("dismiss");break;case"Enter":if(h.metaKey||h.altKey||h.ctrlKey||h.shiftKey||!i().nextSuggestion)return;h.preventDefault(),h.stopPropagation(),o()("accept",i().nextSuggestion);break;case"Backspace":if(h.metaKey||h.altKey||h.ctrlKey||h.shiftKey||!i().nextSuggestion)return;h.preventDefault(),h.stopPropagation(),o()("reject",i().nextSuggestion)}});var f=h3(L);const w=Y(()=>(S(l()),S(s()),R(()=>l()&&!!s().size)));p4(f,{get sortedPathSuggestionsMap(){return s()},get onCodeAction(){return o()},get show(){return a(w)}});var m=A(f,2);const j=Y(()=>(S(l()),S(s()),R(()=>!l()&&!!s().size)));(function(h,F){C1(F,!1);const[O,k]=O1(),g=()=>x1(D,"$ctx",O);let b=c(F,"onCodeAction",8),z=c(F,"sortedPathSuggestionsMap",24,()=>new Map),c1=c(F,"show",8,!0),I=$();const D=c3();p1();var J=t5();let K;p3(J,5,z,([W,r1])=>W,(W,r1)=>{var b1=h1(()=>i2(a(r1),2));let X=()=>a(b1)[0],T=()=>a(b1)[1];var y=X4(),B=p(y);const n1=Y(()=>(S(f2),T(),R(()=>f2(T().length)))),d1=Y(()=>(g(),X(),R(()=>{var V;return((V=g().isOpen)==null?void 0:V[X()])??!0})));p0(B,{get duration(){return a(n1)},class:"c-suggestion-tree-item",get open(){return a(d1)},onChangeOpen:V=>U1(D,R(g).isOpen={...g().isOpen,[X()]:V},R(g)),children:(V,t1)=>{var i1=Y4();p3(i1,5,T,f1=>f1.result.suggestionId,(f1,o1)=>{const g3=Y(()=>(S(A1),a(o1),S(q1),g(),R(()=>A1(a(o1),q1(g())))));Q4(f1,{get onCodeAction(){return b()},get suggestion(){return a(o1)},get shouldScrollIntoView(){return a(g3)},get scrollContainer(){return a(I)}})}),E(V,i1)},$$slots:{default:!0,summary:(V,t1)=>{const i1=Y(()=>(T(),S(T1),R(()=>T().every(f1=>f1.state==="accepted")?T1("rejectAllInFile"):T1("rejectAllInFile","acceptAllInFile"))));l0(V,{get filepath(){return X()},slot:"summary",class:"c-suggestion-tree-item__lang-summary",get value(){return T()},get onCodeAction(){return b()},get codeActions(){return a(i1)}})}}}),d0(3,y,()=>s0),E(W,y)}),C3(J,W=>M(I,W),()=>a(I)),q(W=>K=Q1(J,1,"c-suggestion-tree__maximized svelte-bnynfs",null,K,W),[()=>({hidden:!c1()})],Y),E(h,J),v1(),k()})(m,{get sortedPathSuggestionsMap(){return s()},get onCodeAction(){return o()},get show(){return a(j)}});var x=A(m,2);let v;var P=p(x);s3(P,e,"no-suggestions",{},null),q(h=>v=Q1(x,1,"c-suggestion-tree__no-suggestions svelte-l320gs",null,v,h),[()=>({hidden:s().size})],Y),E(t,L),v1(),r()}var r5=H('<div class="c-code-roll-item-header svelte-3zqetr"><!> <!></div>');function S2(t,e,n,r){const i=t.split(""),o=e.toSorted(({lineRange:{start:s}},{lineRange:{start:l}})=>l-s);for(const s of o){const l=[s.result.charStart,n(s)],C=r(s);C&&l.push(...C.split("")),i.splice(...l)}return i.join("")}const A2=((t=0)=>()=>Date.now()+"-"+t++)(),i5=(t,e,n,r,i,o)=>{var u,d,_,L;if(!t||!n)return[];n=function(f,w){return S2(f,w,m=>m.result.suggestedCode.split("").length,m=>m.result.existingCode)}(n,e.filter(f=>f.state===J1.accepted));const s=function(f,w){return S2(f,w,m=>m.result.charEnd-m.result.charStart,m=>m.result.suggestedCode)}(n,e);(d=(u=t.getModel())==null?void 0:u.original)==null||d.dispose(),(L=(_=t.getModel())==null?void 0:_.modified)==null||L.dispose();const l=o.editor.createModel(n,r,o.Uri.parse("file://"+i+`#${A2()}`)),C=o.editor.createModel(s,r,o.Uri.parse("file://"+i+`#${A2()}`));return t.setModel({original:l,modified:C}),[l,C]};function o5(t){var e;return`${t.requestId}#${(e=t.result)==null?void 0:e.suggestionId}`}function M2(t){return t.map(o5).join(":")}function j2(t){const e=t.toSorted((i,o)=>i.start-o.start),n=[];let r=e.shift();for(;e.length;){const i=e.shift();i.start<=r.end+1?r.end=Math.max(r.end,i.end):(n.push(r),r=i)}return n.push(r),n}function E2(t){return t.reduce((e,n)=>e+=n.end-n.start+1,0)}function F2(t){return t.reduce((e,n,r)=>r===0?e:e+=n.start-t[r-1].end-1,0)}function O2(t){var r;let e,n;if(t.modifiedEndLineNumber===0)n=t.originalEndLineNumber-t.originalStartLineNumber+1,e=t.originalStartLineNumber-1;else if((r=t.charChanges)!=null&&r.length){const i=j2(t.charChanges.map(d=>({start:d.originalStartLineNumber,end:d.originalEndLineNumber}))),o=j2(t.charChanges.map(d=>({start:d.modifiedStartLineNumber,end:d.modifiedEndLineNumber}))),s=E2(i),l=E2(o),C=F2(i),u=F2(o);n=s+l+Math.max(C,u),e=t.modifiedStartLineNumber-1}else{if(t.originalEndLineNumber!==0)throw new Error("Unexpected line change");n=t.modifiedEndLineNumber-t.modifiedStartLineNumber+1,e=t.modifiedStartLineNumber-1}return{lineCount:n,afterLineNumber:e}}function a5(...t){return t.reduce((e,n)=>({...s5(e,n),charChanges:[...e.charChanges??[],...n.charChanges??[]]}))}function s5(...t){return t.reduce((e,n)=>({originalStartLineNumber:Math.min(e.originalStartLineNumber,n.originalStartLineNumber),originalEndLineNumber:Math.max(e.originalEndLineNumber,n.originalEndLineNumber),modifiedStartLineNumber:Math.min(e.modifiedStartLineNumber,n.modifiedStartLineNumber),modifiedEndLineNumber:Math.max(e.modifiedEndLineNumber,n.modifiedEndLineNumber)}))}function l5(t,e){if(e.originalStartLineNumber===t.lineRange.start+1)return!0;const n=Math.min(e.originalStartLineNumber,e.modifiedStartLineNumber),r=Math.max(e.originalEndLineNumber,e.modifiedEndLineNumber);return n>=t.lineRange.start&&n<=t.lineRange.stop||r>=t.lineRange.start&&r<=t.lineRange.stop||n<=t.lineRange.start&&r>=t.lineRange.stop}function T3(t,e){const n=new Map,r=t.toSorted(({lineRange:{start:i}},{lineRange:{start:o}})=>i-o);t:for(const i of e.toSorted(({modifiedStartLineNumber:o,originalStartLineNumber:s},{modifiedStartLineNumber:l,originalStartLineNumber:C})=>o-l||s-C))for(const o of r)if(l5(o,i)){const s=n.get(o);n.set(o,s?a5(s,i):i);continue t}return n}var c5=H('<div class="c-code-roll-suggestion-window__view-zone"></div> <div class="c-code-roll-suggestion-window svelte-1ci0na9"><div class="c-code-roll-suggestion-window__item svelte-1ci0na9"><div class="c-code-roll-suggestion-window__item-title svelte-1ci0na9"><button class="c-code-roll-suggestion-window__item-title-text svelte-1ci0na9" tabindex="0"><!> </button> <!></div> <div class="c-code-roll-suggestion-window__border svelte-1ci0na9"><div class="c-code-roll-suggestion-window__window svelte-1ci0na9"></div></div></div></div>',1),u5=H('<div class="c-diff-view__loading"><!></div>'),d5=H('<div><div class="c-diff-view svelte-syu9kz"></div> <!></div>');function g5(t,e){C1(e,!1);const[n,r]=O1(),i=()=>x1(i0,"$themeStore",n),o=()=>x1(v,"$monaco",n),s=$();let l=c(e,"height",12,500),C=c(e,"language",8),u=c(e,"originalCode",8),d=c(e,"suggestions",8),_=c(e,"codeActions",24,()=>[]),L=c(e,"onCodeAction",8,n3),f=c(e,"path",8),w=c(e,"busy",12,!0),m=c(e,"expanded",8,!1),j=c(e,"scrollContainer",24,()=>{}),x=c(e,"options",24,()=>({enableSplitViewResizing:!1,automaticLayout:!0,readOnly:!0,overviewRulerLanes:0,lineHeight:20,renderLineHighlight:"none",contextmenu:!1,renderSideBySide:!1,renderIndicators:!0,renderMarginRevertIcon:!1,originalEditable:!1,diffCodeLens:!1,renderOverviewRuler:!1,ignoreTrimWhitespace:!1,maxComputationTime:3e3,scrollBeyondLastColumn:0,scrollBeyondLastLine:!1,scrollPredominantAxis:!1,scrollbar:{alwaysConsumeMouseWheel:!1,vertical:"hidden",horizontal:"hidden"},cursorSurroundingLines:0,cursorSurroundingLinesStyle:"all",hideUnchangedRegions:{enabled:!m(),revealLineCount:5,minimumLineCount:3,contextLineCount:5},lineNumbers:String,hover:{enabled:!1}}));const v=s4.getContext().monaco;let P=$(M2(d())),h=$(""),F=[],O=$(),k=document.createElement("div");k.classList.add("c-diff-view__editor");let g,b=$(),z=$([]),c1=$(!1);function I(y){const B=function(n1,{enabled:d1=!0,revealLineCount:V=5,minimumLineCount:t1=3,contextLineCount:i1=5}={enabled:!0,revealLineCount:5,minimumLineCount:3,contextLineCount:5},f1,o1){const g3={lines:0,decorations:0,hasTopDecorations:!1};if(!n1.length||!d1)return g3;let Y1=0,P1=!1;const X1=[...n1].sort((z1,B1)=>z1.lineRange.start-B1.lineRange.start);let a1=1;for(let z1=0,B1=1;z1<X1.length;z1++,B1++){const g1=X1[z1],t3=X1[B1];if(a1+=Math.min(g1.lineRange.start+1,V),a1+=o1!=null&&o1.get(g1)?O2(o1.get(g1)).lineCount:Y0(g1),g1.lineRange.start-V>1?(P1=!0,Y1++):g1.lineRange.start-V==1&&a1++,t3){const e3=t3.lineRange.start-g1.lineRange.start;e3>i1+V?(Y1++,a1+=i1,a1+=V):a1+=e3}else g1.lineRange.stop<f1?(Y1++,a1+=Math.min(f1-g1.lineRange.stop,V)):(a1+=i1,a1+=V)}return{lines:Math.max(a1,t1),decorations:Y1,hasTopDecorations:P1}}(d(),x().hideUnchangedRegions,X0(u()),a(z).length>0?T3(d(),a(z)):void 0);l(m()&&y?y.getModifiedEditor().getContentHeight():B.lines*(x().lineHeight??20)+24*B.decorations),M(c1,B.hasTopDecorations)}function D(){var y,B,n1,d1,V,t1;(n1=(B=(y=a(b))==null?void 0:y.getModel())==null?void 0:B.original)==null||n1.dispose(),(t1=(V=(d1=a(b))==null?void 0:d1.getModel())==null?void 0:V.modified)==null||t1.dispose(),o()&&(a(b)||(M(b,o().editor.createDiffEditor(k,a(s))),F.push(a(b))),a(b).onDidDispose(()=>M(b,void 0)),F.push(...i5(a(b),d(),u(),C(),f(),o())),I(a(b)),g==null||g.dispose(),g=a(b).onDidUpdateDiff(()=>{var i1;w(!1),M(z,((i1=a(b))==null?void 0:i1.getLineChanges())??[]),I(a(b))}))}o0(()=>{a(O).appendChild(k),D()}),W0(()=>{F.forEach(y=>{var B;return(B=y==null?void 0:y.dispose)==null?void 0:B.call(y)}),k.remove()}),Q(()=>(S(x()),i()),()=>{var y,B;M(s,{...x(),theme:y2((y=i())==null?void 0:y.category,(B=i())==null?void 0:B.intensity)})}),Q(()=>(a(b),S(m())),()=>{var y;(y=a(b))==null||y.updateOptions({hideUnchangedRegions:{enabled:!m(),revealLineCount:5,minimumLineCount:3,contextLineCount:5}})}),Q(()=>(i(),o(),a(b)),()=>{var n1,d1,V,t1;const y=i(),B=y2(y==null?void 0:y.category,y==null?void 0:y.intensity);(n1=o())==null||n1.editor.setTheme(B),(d1=a(b))==null||d1.getModifiedEditor().updateOptions({theme:B}),(V=a(b))==null||V.getOriginalEditor().updateOptions({theme:B}),(t1=a(b))==null||t1.layout()}),Q(()=>(S(d()),a(P),S(u()),a(h)),()=>{const y=M2(d());a(P)===y&&u()===a(h)||(M(P,y),M(h,u()),D())}),R1(),p1();var J=d5();let K;var W=p(J);let r1;l3(W,(y,B)=>{var n1;return(n1=n4)==null?void 0:n1(y,B)},()=>({onResize:()=>{var y;return(y=a(b))==null?void 0:y.layout()}})),C3(W,y=>M(O,y),()=>a(O));var b1=A(W,2),X=y=>{var B=u5(),n1=p(B);o2(n1,{}),E(y,B)},T=y=>{var B=r0(),n1=h3(B);p3(n1,1,()=>(S(T3),S(d()),a(z),R(()=>T3(d(),a(z)))),a0,(d1,V)=>{var t1=h1(()=>i2(a(V),2)),i1=h1(()=>O2(a(t1)[1]));(function(f1,o1){C1(o1,!1);const[g3,Y1]=O1(),P1=()=>x1(j0,"$ctx",g3),X1=$(),a1=$(),z1=$(),B1=$(),g1=$(),t3=$();let e3=c(o1,"diffEditor",8),s1=c(o1,"suggestion",8),A0=c(o1,"codeActions",24,()=>[]),$3=c(o1,"onCodeAction",8),f3=c(o1,"afterLineNumber",8),m3=c(o1,"lineCount",8,0),M0=c(o1,"scrollContainer",8),l2=$(0);const j0=c3();Q(()=>(S(s1()),P1()),()=>{M(X1,E1(s1(),P1(),!1))}),Q(()=>(S(e3()),L2),()=>{M(a1,e3().getModifiedEditor().getOption(L2.EditorOption.lineHeight))}),Q(()=>(S(m3()),a(a1)),()=>{M(z1,m3()*a(a1))}),Q(()=>(S(s1()),Z3),()=>{M(B1,s1().changeType===Z3.insertion||s1().changeType===Z3.modification)}),Q(()=>(a(B1),S(f3()),S(m3())),()=>{M(g1,a(B1)?f3()+m3():f3())}),Q(()=>a(g1),()=>{M(t3,a(g1)>999?0:5)}),R1(),p1();var c2=c5(),P3=h3(c2);l3(P3,(w1,M1)=>{var d2;return(d2=a4)==null?void 0:d2(w1,M1)},()=>({editor:e3(),afterLineNumber:f3(),heightInPx:a(a1),onDomNodeTop(w1){M(l2,w1)}}));var z3=A(P3,2),B3=p(z3),N3=p(B3),w3=p(N3),E0=h1(()=>A3("Enter",q3($3(),"active",s1()))),F0=h1(()=>q3($3(),"active",s1())),u2=p(w3);const O0=Y(()=>a(X1)!=="none");v0(u2,{get mask(){return a(O0)},get suggestion(){return s1()}});var R0=A(u2),$0=A(w3,2);u0($0,{compact:!0,get actions(){return A0()},get onAction(){return $3()},get value(){return s1()}});var P0=A(N3,2),z0=p(P0);l3(B3,(w1,M1)=>F1==null?void 0:F1(w1,M1),()=>({scrollContainer:M0(),doScroll:A1(s1(),q1(P1())),scrollIntoView:{behavior:"smooth",block:"center"},useSmartBlockAlignment:!0})),q(w1=>{y1(P3,`--augment-code-roll-left-alignment:${a(t3)??""}px`),y1(z3,`
    --augment-code-roll-left-alignment:${a(t3)??""}px;
    --augment-code-roll-suggestion-window-line-height:${a(a1)??""}px;
    top:${a(l2)??""}px;
  `),N(z3,"data-result-id",(S(s1()),R(()=>`${s1().result.suggestionId}:${s1().requestId}`))),y1(B3,w1),y1(N3,`height:${a(a1)??""}px`),U3(R0,` ${S(s1()),R(()=>s1().result.changeDescription)??""}`),y1(z0,`height:${a(z1)??""}px;`)},[()=>(S(_3),S(E1),S(s1()),P1(),R(()=>_3(E1(s1(),P1(),!1))))],Y),l1("keydown",w3,function(...w1){var M1;(M1=a(E0))==null||M1.apply(this,w1)}),l1("click",w3,function(...w1){var M1;(M1=a(F0))==null||M1.apply(this,w1)}),E(f1,c2),v1(),Y1()})(d1,G0({get diffEditor(){return a(b)},get suggestion(){return a(t1)[0]},get codeActions(){return _()},get onCodeAction(){return L()}},()=>a(i1),{get scrollContainer(){return j()}}))}),E(y,B)};H1(b1,y=>{w()||!a(b)?y(X):y(T,!1)}),q((y,B)=>{K=Q1(J,1,"c-diff-view__container svelte-syu9kz",null,K,y),y1(J,`--augment-codeblock-min-height:${l()??""}px;`),r1=y1(W,"",r1,B)},[()=>({"has-top-decorations":a(c1)}),()=>({display:w()?"none":"flex"})],Y),E(t,J),v1(),r()}var h5=H('<div class="c-code-roll-item__loading svelte-1i59d33"><!></div>'),C5=H('<div class="c-code-roll-item-diff svelte-1i59d33"><!></div>'),p5=function(){this.__data__=[],this.size=0},f0=function(t,e){return t===e||t!=t&&e!=e},v5=f0,E3=function(t,e){for(var n=t.length;n--;)if(v5(t[n][0],e))return n;return-1},f5=E3,m5=Array.prototype.splice,w5=function(t){var e=this.__data__,n=f5(e,t);return!(n<0)&&(n==e.length-1?e.pop():m5.call(e,n,1),--this.size,!0)},L5=E3,y5=function(t){var e=this.__data__,n=L5(e,t);return n<0?void 0:e[n][1]},x5=E3,b5=function(t){return x5(this.__data__,t)>-1},_5=E3,k5=function(t,e){var n=this.__data__,r=_5(n,t);return r<0?(++this.size,n.push([t,e])):n[r][1]=e,this},S5=p5,A5=w5,M5=y5,j5=b5,E5=k5;function r3(t){var e=-1,n=t==null?0:t.length;for(this.clear();++e<n;){var r=t[e];this.set(r[0],r[1])}}r3.prototype.clear=S5,r3.prototype.delete=A5,r3.prototype.get=M5,r3.prototype.has=j5,r3.prototype.set=E5;var F3=r3,F5=F3,O5=function(){this.__data__=new F5,this.size=0},R5=function(t){var e=this.__data__,n=e.delete(t);return this.size=e.size,n},$5=function(t){return this.__data__.get(t)},P5=function(t){return this.__data__.has(t)},z5=M3,B5=g0,R2,m0=function(t){if(!B5(t))return!1;var e=z5(t);return e=="[object Function]"||e=="[object GeneratorFunction]"||e=="[object AsyncFunction]"||e=="[object Proxy]"},V3=W1["__core-js_shared__"],$2=(R2=/[^.]+$/.exec(V3&&V3.keys&&V3.keys.IE_PROTO||""))?"Symbol(src)_1."+R2:"",N5=function(t){return!!$2&&$2 in t},Z5=Function.prototype.toString,w0=function(t){if(t!=null){try{return Z5.call(t)}catch{}try{return t+""}catch{}}return""},I5=m0,D5=N5,K5=g0,T5=w0,V5=/^\[object .+?Constructor\]$/,W5=Function.prototype,G5=Object.prototype,U5=W5.toString,q5=G5.hasOwnProperty,H5=RegExp("^"+U5.call(q5).replace(/[\\^$.*+?()[\]{}|]/g,"\\$&").replace(/hasOwnProperty|(function).*?(?=\\\()| for .+?(?=\\\])/g,"$1.*?")+"$"),J5=function(t,e){return t==null?void 0:t[e]},Q5=function(t){return!(!K5(t)||D5(t))&&(I5(t)?H5:V5).test(T5(t))},Y5=J5,u3=function(t,e){var n=Y5(t,e);return Q5(n)?n:void 0},a2=u3(W1,"Map"),O3=u3(Object,"create"),P2=O3,X5=function(){this.__data__=P2?P2(null):{},this.size=0},t9=function(t){var e=this.has(t)&&delete this.__data__[t];return this.size-=e?1:0,e},e9=O3,n9=Object.prototype.hasOwnProperty,r9=function(t){var e=this.__data__;if(e9){var n=e[t];return n==="__lodash_hash_undefined__"?void 0:n}return n9.call(e,t)?e[t]:void 0},i9=O3,o9=Object.prototype.hasOwnProperty,a9=function(t){var e=this.__data__;return i9?e[t]!==void 0:o9.call(e,t)},s9=O3,l9=function(t,e){var n=this.__data__;return this.size+=this.has(t)?0:1,n[t]=s9&&e===void 0?"__lodash_hash_undefined__":e,this},c9=X5,u9=t9,d9=r9,g9=a9,h9=l9;function i3(t){var e=-1,n=t==null?0:t.length;for(this.clear();++e<n;){var r=t[e];this.set(r[0],r[1])}}i3.prototype.clear=c9,i3.prototype.delete=u9,i3.prototype.get=d9,i3.prototype.has=g9,i3.prototype.set=h9;var z2=i3,C9=F3,p9=a2,v9=function(t){var e=typeof t;return e=="string"||e=="number"||e=="symbol"||e=="boolean"?t!=="__proto__":t===null},R3=function(t,e){var n=t.__data__;return v9(e)?n[typeof e=="string"?"string":"hash"]:n.map},f9=R3,m9=function(t){var e=f9(this,t).delete(t);return this.size-=e?1:0,e},w9=R3,L9=function(t){return w9(this,t).get(t)},y9=R3,x9=function(t){return y9(this,t).has(t)},b9=R3,_9=function(t,e){var n=b9(this,t),r=n.size;return n.set(t,e),this.size+=n.size==r?0:1,this},k9=function(){this.size=0,this.__data__={hash:new z2,map:new(p9||C9),string:new z2}},S9=m9,A9=L9,M9=x9,j9=_9;function o3(t){var e=-1,n=t==null?0:t.length;for(this.clear();++e<n;){var r=t[e];this.set(r[0],r[1])}}o3.prototype.clear=k9,o3.prototype.delete=S9,o3.prototype.get=A9,o3.prototype.has=M9,o3.prototype.set=j9;var L0=o3,E9=F3,F9=a2,O9=L0,R9=function(t,e){var n=this.__data__;if(n instanceof E9){var r=n.__data__;if(!F9||r.length<199)return r.push([t,e]),this.size=++n.size,this;n=this.__data__=new O9(r)}return n.set(t,e),this.size=n.size,this},$9=F3,P9=O5,z9=R5,B9=$5,N9=P5,Z9=R9;function a3(t){var e=this.__data__=new $9(t);this.size=e.size}a3.prototype.clear=P9,a3.prototype.delete=z9,a3.prototype.get=B9,a3.prototype.has=N9,a3.prototype.set=Z9;var I9=a3,D9=L0,K9=function(t){return this.__data__.set(t,"__lodash_hash_undefined__"),this},T9=function(t){return this.__data__.has(t)};function b3(t){var e=-1,n=t==null?0:t.length;for(this.__data__=new D9;++e<n;)this.add(t[e])}b3.prototype.add=b3.prototype.push=K9,b3.prototype.has=T9;var V9=function(t,e){for(var n=-1,r=t==null?0:t.length;++n<r;)if(e(t[n],n,t))return!0;return!1},W9=function(t,e){return t.has(e)},G9=b3,U9=V9,q9=W9,y0=function(t,e,n,r,i,o){var s=1&n,l=t.length,C=e.length;if(l!=C&&!(s&&C>l))return!1;var u=o.get(t),d=o.get(e);if(u&&d)return u==e&&d==t;var _=-1,L=!0,f=2&n?new G9:void 0;for(o.set(t,e),o.set(e,t);++_<l;){var w=t[_],m=e[_];if(r)var j=s?r(m,w,_,e,t,o):r(w,m,_,t,e,o);if(j!==void 0){if(j)continue;L=!1;break}if(f){if(!U9(e,function(x,v){if(!q9(f,v)&&(w===x||i(w,x,n,r,o)))return f.push(v)})){L=!1;break}}else if(w!==m&&!i(w,m,n,r,o)){L=!1;break}}return o.delete(t),o.delete(e),L},H9=function(t){var e=-1,n=Array(t.size);return t.forEach(function(r,i){n[++e]=[i,r]}),n},J9=function(t){var e=-1,n=Array(t.size);return t.forEach(function(r){n[++e]=r}),n},B2=W1.Uint8Array,Q9=f0,Y9=y0,X9=H9,t8=J9,N2=x2?x2.prototype:void 0,W3=N2?N2.valueOf:void 0,e8=function(t,e,n,r,i,o,s){switch(n){case"[object DataView]":if(t.byteLength!=e.byteLength||t.byteOffset!=e.byteOffset)return!1;t=t.buffer,e=e.buffer;case"[object ArrayBuffer]":return!(t.byteLength!=e.byteLength||!o(new B2(t),new B2(e)));case"[object Boolean]":case"[object Date]":case"[object Number]":return Q9(+t,+e);case"[object Error]":return t.name==e.name&&t.message==e.message;case"[object RegExp]":case"[object String]":return t==e+"";case"[object Map]":var l=X9;case"[object Set]":var C=1&r;if(l||(l=t8),t.size!=e.size&&!C)return!1;var u=s.get(t);if(u)return u==e;r|=2,s.set(t,e);var d=Y9(l(t),l(e),r,i,o,s);return s.delete(t),d;case"[object Symbol]":if(W3)return W3.call(t)==W3.call(e)}return!1},n8=function(t,e){for(var n=-1,r=e.length,i=t.length;++n<r;)t[i+n]=e[n];return t},s2=Array.isArray,r8=n8,i8=s2,o8=function(t,e,n){var r=e(t);return i8(t)?r:r8(r,n(t))},a8=function(t,e){for(var n=-1,r=t==null?0:t.length,i=0,o=[];++n<r;){var s=t[n];e(s,n,t)&&(o[i++]=s)}return o},s8=a8,l8=function(){return[]},c8=Object.prototype.propertyIsEnumerable,Z2=Object.getOwnPropertySymbols,u8=Z2?function(t){return t==null?[]:(t=Object(t),s8(Z2(t),function(e){return c8.call(t,e)}))}:l8,d8=function(t,e){for(var n=-1,r=Array(t);++n<t;)r[n]=e(n);return r},g8=M3,h8=j3,I2=function(t){return h8(t)&&g8(t)=="[object Arguments]"},C8=j3,x0=Object.prototype,p8=x0.hasOwnProperty,v8=x0.propertyIsEnumerable,f8=I2(function(){return arguments}())?I2:function(t){return C8(t)&&p8.call(t,"callee")&&!v8.call(t,"callee")},Q3={exports:{}},m8=function(){return!1};(function(t,e){var n=W1,r=m8,i=e&&!e.nodeType&&e,o=i&&t&&!t.nodeType&&t,s=o&&o.exports===i?n.Buffer:void 0,l=(s?s.isBuffer:void 0)||r;t.exports=l})(Q3,Q3.exports);var b0=Q3.exports,w8=/^(?:0|[1-9]\d*)$/,L8=function(t,e){var n=typeof t;return!!(e=e??9007199254740991)&&(n=="number"||n!="symbol"&&w8.test(t))&&t>-1&&t%1==0&&t<e},_0=function(t){return typeof t=="number"&&t>-1&&t%1==0&&t<=9007199254740991},y8=M3,x8=_0,b8=j3,G={};G["[object Float32Array]"]=G["[object Float64Array]"]=G["[object Int8Array]"]=G["[object Int16Array]"]=G["[object Int32Array]"]=G["[object Uint8Array]"]=G["[object Uint8ClampedArray]"]=G["[object Uint16Array]"]=G["[object Uint32Array]"]=!0,G["[object Arguments]"]=G["[object Array]"]=G["[object ArrayBuffer]"]=G["[object Boolean]"]=G["[object DataView]"]=G["[object Date]"]=G["[object Error]"]=G["[object Function]"]=G["[object Map]"]=G["[object Number]"]=G["[object Object]"]=G["[object RegExp]"]=G["[object Set]"]=G["[object String]"]=G["[object WeakMap]"]=!1;var _8=function(t){return b8(t)&&x8(t.length)&&!!G[y8(t)]},k8=function(t){return function(e){return t(e)}},Y3={exports:{}};(function(t,e){var n=u4,r=e&&!e.nodeType&&e,i=r&&t&&!t.nodeType&&t,o=i&&i.exports===r&&n.process,s=function(){try{var l=i&&i.require&&i.require("util").types;return l||o&&o.binding&&o.binding("util")}catch{}}();t.exports=s})(Y3,Y3.exports);var D2=Y3.exports,S8=_8,A8=k8,K2=D2&&D2.isTypedArray,k0=K2?A8(K2):S8,M8=d8,j8=f8,E8=s2,F8=b0,O8=L8,R8=k0,$8=Object.prototype.hasOwnProperty,P8=function(t,e){var n=E8(t),r=!n&&j8(t),i=!n&&!r&&F8(t),o=!n&&!r&&!i&&R8(t),s=n||r||i||o,l=s?M8(t.length,String):[],C=l.length;for(var u in t)!e&&!$8.call(t,u)||s&&(u=="length"||i&&(u=="offset"||u=="parent")||o&&(u=="buffer"||u=="byteLength"||u=="byteOffset")||O8(u,C))||l.push(u);return l},z8=Object.prototype,B8=function(t){var e=t&&t.constructor;return t===(typeof e=="function"&&e.prototype||z8)},N8=function(t,e){return function(n){return t(e(n))}}(Object.keys,Object),Z8=B8,I8=N8,D8=Object.prototype.hasOwnProperty,K8=function(t){if(!Z8(t))return I8(t);var e=[];for(var n in Object(t))D8.call(t,n)&&n!="constructor"&&e.push(n);return e},T8=m0,V8=_0,W8=P8,G8=K8,U8=function(t){return t!=null&&V8(t.length)&&!T8(t)},q8=o8,H8=u8,J8=function(t){return U8(t)?W8(t):G8(t)},T2=function(t){return q8(t,J8,H8)},Q8=Object.prototype.hasOwnProperty,Y8=function(t,e,n,r,i,o){var s=1&n,l=T2(t),C=l.length;if(C!=T2(e).length&&!s)return!1;for(var u=C;u--;){var d=l[u];if(!(s?d in e:Q8.call(e,d)))return!1}var _=o.get(t),L=o.get(e);if(_&&L)return _==e&&L==t;var f=!0;o.set(t,e),o.set(e,t);for(var w=s;++u<C;){var m=t[d=l[u]],j=e[d];if(r)var x=s?r(j,m,d,e,t,o):r(m,j,d,t,e,o);if(!(x===void 0?m===j||i(m,j,n,r,o):x)){f=!1;break}w||(w=d=="constructor")}if(f&&!w){var v=t.constructor,P=e.constructor;v==P||!("constructor"in t)||!("constructor"in e)||typeof v=="function"&&v instanceof v&&typeof P=="function"&&P instanceof P||(f=!1)}return o.delete(t),o.delete(e),f},X3=u3(W1,"DataView"),t2=a2,e2=u3(W1,"Promise"),n2=u3(W1,"Set"),r2=u3(W1,"WeakMap"),S0=M3,d3=w0,V2="[object Map]",W2="[object Promise]",G2="[object Set]",U2="[object WeakMap]",q2="[object DataView]",X8=d3(X3),t6=d3(t2),e6=d3(e2),n6=d3(n2),r6=d3(r2),G1=S0;(X3&&G1(new X3(new ArrayBuffer(1)))!=q2||t2&&G1(new t2)!=V2||e2&&G1(e2.resolve())!=W2||n2&&G1(new n2)!=G2||r2&&G1(new r2)!=U2)&&(G1=function(t){var e=S0(t),n=e=="[object Object]"?t.constructor:void 0,r=n?d3(n):"";if(r)switch(r){case X8:return q2;case t6:return V2;case e6:return W2;case n6:return G2;case r6:return U2}return e});var G3=I9,i6=y0,o6=e8,a6=Y8,H2=G1,J2=s2,Q2=b0,s6=k0,Y2="[object Arguments]",X2="[object Array]",L3="[object Object]",t0=Object.prototype.hasOwnProperty,l6=function(t,e,n,r,i,o){var s=J2(t),l=J2(e),C=s?X2:H2(t),u=l?X2:H2(e),d=(C=C==Y2?L3:C)==L3,_=(u=u==Y2?L3:u)==L3,L=C==u;if(L&&Q2(t)){if(!Q2(e))return!1;s=!0,d=!1}if(L&&!d)return o||(o=new G3),s||s6(t)?i6(t,e,n,r,i,o):o6(t,e,C,n,r,i,o);if(!(1&n)){var f=d&&t0.call(t,"__wrapped__"),w=_&&t0.call(e,"__wrapped__");if(f||w){var m=f?t.value():t,j=w?e.value():e;return o||(o=new G3),i(m,j,n,r,o)}}return!!L&&(o||(o=new G3),a6(t,e,n,r,i,o))},e0=j3,c6=function t(e,n,r,i,o){return e===n||(e==null||n==null||!e0(e)&&!e0(n)?e!=e&&n!=n:l6(e,n,r,i,t,o))},u6=c6;const d6=U0(function(t,e){return u6(t,e)});function g6(t,e){C1(e,!1);let n=c(e,"suggestions",8),r=c(e,"filepath",8),i=c(e,"readFile",8),o=c(e,"onFileAction",8,n3),s=c(e,"onCodeAction",8,n3),l=c(e,"fileActions",24,()=>[]),C=c(e,"codeActions",24,()=>[]),u=c(e,"expandable",8,!0),d=c(e,"scrollContainer",24,()=>{}),_=$(""),L=$(!1),f=$(null),w=$(null);Q(()=>(S(n()),a(f),a(w),S(r())),()=>{d6(n(),a(f))&&a(w)!==null&&m2(r(),a(w))||(a(w)!==null&&m2(r(),a(w))||M(L,!1),M(w,r()),M(f,n()),async function(m){M(_,await i()(m)),M(L,!0)}(r()))}),R1(),p1(),p0(t,{open:!0,class:"c-code-roll-item ",get expandable(){return u()},children:(m,j)=>{(function(x,v){C1(v,!1);let P=c(v,"suggestions",8),h=c(v,"filepath",8),F=c(v,"originalCode",8),O=c(v,"loaded",8,!1),k=c(v,"language",24,()=>c4(h().relPath)),g=c(v,"onCodeAction",8,n3),b=c(v,"codeActions",24,()=>[]),z=c(v,"scrollContainer",24,()=>{});p1();var c1=C5(),I=p(c1),D=K=>{g5(K,{get onCodeAction(){return g()},get codeActions(){return b()},get suggestions(){return P()},get originalCode(){return F()},get language(){return k()},get path(){return S(h()),R(()=>h().relPath)},get scrollContainer(){return z()}})},J=K=>{var W=h5(),r1=p(W);o2(r1,{}),E(K,W)};H1(I,K=>{O()?K(D):K(J,!1)}),E(x,c1),v1()})(m,{get codeActions(){return C()},get loaded(){return a(L)},get suggestions(){return n()},get filepath(){return r()},get originalCode(){return a(_)},get onCodeAction(){return s()},get scrollContainer(){return d()}})},$$slots:{default:!0,summary:(m,j)=>{(function(x,v){C1(v,!1);let P=c(v,"filepath",8),h=c(v,"onFileAction",8,n3),F=c(v,"fileActions",24,()=>[]),O=c(v,"suggestions",8);p1();var k=r5(),g=p(k);l0(g,{get filepath(){return S(P()),R(()=>P().relPath)},get onCodeAction(){return n3},get value(){return O()}});var b=A(g,2);u0(b,{get actions(){return F()},get onAction(){return h()},get value(){return P()}}),E(x,k),v1()})(m,{get filepath(){return r()},get onFileAction(){return o()},get fileActions(){return l()},slot:"summary",get suggestions(){return n()}})}}}),v1()}function n0(t,e,n){return t.addEventListener(e,n),()=>{t.removeEventListener(e,n)}}function h6(t){t()}var C6=H('<div class="c-next-edit-suggestions--empty svelte-xgtx0g"><p>No Suggestions</p> <!></div>'),p6=H('<div class="c-next-edit-suggestions--empty svelte-xgtx0g"><!></div>'),v6=H('<div slot="left" class="c-next-edit-suggestions__left svelte-xgtx0g"><!></div>'),f6=H('<div class="c-next-edit-suggestions__right--empty svelte-xgtx0g"></div>'),m6=H('<div class="c-next-edit-suggestions__right svelte-xgtx0g" slot="right"><!></div>'),w6=H('<main><div class="c-next-edit-suggestions__container svelte-xgtx0g" tabindex="0" role="button"><!></div></main>');function Z6(t,e){C1(e,!1);const[n,r]=O1(),i=()=>x1(u,"$ctx",n),o=$(),s=function(k){return async function(g){const b=await k.send({type:e1.readFileRequest,data:{pathName:g}});if("error"in b.data)throw new Error(b.data.error);return b.data.content}}(new e4(L1.postMessage,3e3)),l=(C="(min-width: 500px)",V0(!1,k=>{const g=window.matchMedia(C);k((g==null?void 0:g.matches)??!1);const b=z=>k(z.matches);return g.addEventListener("change",b),()=>{g.removeEventListener("change",b)}}));var C;const u=C0({}),d=T1("active","|","reject","accept"),_=T1("active","|","reject","undo");let L=$(new Map),f=$([]),w=$(!0),m=$(!1),j=$();function x(k){const g=function(b){if(b===-1)return-1;let z=b;do z--;while(z>=0&&a(f)[b].state==="stale");if(z!==-1)return z;z=b;do z++;while(z<a(f).length&&a(f)[b].state==="stale");return z===a(f).length?-1:z}(a(f).findIndex(A1.bind(null,k)));return c0(a(L),g)}const v=(k,g)=>{switch(k){case"acceptAllInFile":return Array.isArray(g)?void L1.postMessage({type:e1.nextEditSuggestionsAction,data:{acceptAllInFile:g}}):void 0;case"rejectAllInFile":return Array.isArray(g)?void L1.postMessage({type:e1.nextEditSuggestionsAction,data:{rejectAllInFile:g}}):void 0;case"undoAllInFile":return Array.isArray(g)?void L1.postMessage({type:e1.nextEditSuggestionsAction,data:{undoAllInFile:g}}):void 0;case"refresh":return M(w,!0),void L1.postMessage({type:e1.nextEditRefreshStarted,data:"refresh"});case"accept":return!g||Array.isArray(g)?void 0:(U1(u,R(i).selectedSuggestion=x(g),R(i)),void L1.postMessage({type:e1.nextEditSuggestionsAction,data:{accept:g}}));case"reject":return!g||Array.isArray(g)?void 0:void L1.postMessage({type:e1.nextEditSuggestionsAction,data:{reject:g}});case"active":return!g||Array.isArray(g)?void 0:(L1.postMessage({type:e1.nextEditOpenSuggestion,data:g}),void N1(u,{...i(),activeSuggestion:g,selectedSuggestion:g}));case"select":return!g||Array.isArray(g)?void 0:void N1(u,{...i(),activeSuggestion:void 0,selectedSuggestion:g});case"dismiss":return y3(i())==="active"?(N1(u,{...i(),activeSuggestion:void 0}),void L1.postMessage({type:e1.nextEditDismiss})):void 0;case"undo":return!g||Array.isArray(g)?void 0:void L1.postMessage({type:e1.nextEditSuggestionsAction,data:{undo:g}})}};function P(k){M(O,!0)}function h(k){U1(u,R(i).selectedSuggestion=void 0,R(i)),M(O,!1)}o0(()=>(L1.postMessage({type:e1.nextEditLoaded}),function(...k){return function(){k.forEach(h6)}}(n0(window,"focus",P),n0(window,"blur",h))));let F=$(),O=$(!1);Q(()=>a(F),()=>{a(F)&&a(F).focus()}),Q(()=>i(),()=>{M(o,q1(i()))}),Q(()=>(a(O),i()),()=>{a(O)&&i().nextSuggestion&&i().selectedSuggestion===void 0&&i().nextSuggestion!==i().selectedSuggestion&&U1(u,R(i).selectedSuggestion=i().nextSuggestion,R(i))}),R1(),p1(),l1("message",q0,function(k){const g=k.data;switch(g.type){case e1.nextEditPreviewActive:N1(u,{...i(),activeSuggestion:g.data,nextSuggestion:g.data});break;case e1.nextEditDismiss:N1(u,{...i(),activeSuggestion:void 0});break;case e1.nextEditActiveSuggestionChanged:U1(u,R(i).activeSuggestion=g.data,R(i));break;case e1.nextEditToggleSuggestionTree:M(m,!a(m));break;case e1.nextEditRefreshStarted:M(w,!0);break;case e1.nextEditRefreshFinished:M(w,!1);break;case e1.nextEditSuggestionsChanged:M(w,!1),M(L,new Map(t4(g.data.suggestions??[]))),M(f,[...a(L).values()].flat()),w2(a(f),i().nextSuggestion)||N1(u,{...i(),nextSuggestion:void 0}),w2(a(f),i().activeSuggestion)||N1(u,{...i(),activeSuggestion:void 0});break;case e1.nextEditNextSuggestionChanged:N1(u,{...i(),nextSuggestion:g.data});break;case e1.nextEditPanelFocus:a(F)&&a(F).focus()}}),l4.Root(t,{children:(k,g)=>{var b=w6();let z;var c1=p(b),I=p(c1),D=K=>{var W=C6(),r1=A(p(W),2),b1=h1(()=>q3(v,"refresh"));d4(r1,{get loading(){return a(w)},$$events:{click(...X){var T;(T=a(b1))==null||T.apply(this,X)}},children:(X,T)=>{var y=H0("Refresh");E(X,y)},$$slots:{default:!0}}),E(K,W)},J=(K,W)=>{var r1=X=>{var T=p6(),y=p(T);o2(y,{}),E(X,T)},b1=X=>{r4(X,{showButton:!1,class:"c-next-edit-suggestions__drawer",initialWidth:300,expandedMinWidth:150,deadzone:50,minimizedWidth:40,get minimized(){return a(m)},set minimized(T){M(m,T)},$$slots:{left:(T,y)=>{var B=v6();n5(p(B),{get sortedPathSuggestionsMap(){return a(L)},onCodeAction:v,get minimized(){return a(m)}}),E(T,B)},right:(T,y)=>{var B=m6(),n1=p(B),d1=t1=>{const i1=Y(()=>[a(o)]),f1=Y(()=>(a(o),S(J1),R(()=>a(o).state===J1.accepted?_:d)));g6(t1,{get filepath(){return a(o),R(()=>a(o).qualifiedPathName)},get suggestions(){return a(i1)},onCodeAction:v,get codeActions(){return a(f1)},get readFile(){return s},expandable:!1,get scrollContainer(){return a(j)}})},V=t1=>{var i1=f6();E(t1,i1)};H1(n1,t1=>{a(o)?t1(d1):t1(V,!1)}),C3(B,t1=>M(j,t1),()=>a(j)),E(T,B)}},$$legacy:!0})};H1(K,X=>{a(w)?X(r1):X(b1,!1)},W)};H1(I,K=>{a(f),R(()=>a(f).length===0)?K(D):K(J,!1)}),C3(c1,K=>M(F,K),()=>a(F)),q(K=>z=Q1(b,1,"c-next-edit-suggestions svelte-xgtx0g",null,z,K),[()=>({"c-next-edit-suggestions__narrow":!x1(l,"$query",n)})],Y),E(k,b)},$$slots:{default:!0}}),v1(),r()}export{Z6 as default};
