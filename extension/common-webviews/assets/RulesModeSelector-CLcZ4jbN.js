import{A as ee,B as W,_ as Q,C as v,m as h,P as _,L as e,a0 as ae,D as se,H as te,I as A,J as k,G as f,b as a,N as le,a3 as oe,Y as y,F as T,T as U,Z as Y,t as re,Q as q,M as ie,a4 as ne,a5 as de}from"./SpinnerAugment-B-W1rkU5.js";import{e as ce,i as ue}from"./IconButtonAugment-Cdot7Te3.js";import{A as ve,D as d}from"./index-BZ1aQDkr.js";import{B as F}from"./ButtonAugment-BsoJM5iW.js";import{C as he}from"./chevron-down-BvquqDa7.js";import{T as fe}from"./CardAugment-6M90JowR.js";import{R}from"./message-broker-DiyMl4p8.js";var pe=T('<div class="c-dropdown-label svelte-9n7h82"><!></div>'),me=T("<!> <!>",1),ge=T("<!> <!>",1),$e=T("<!> <!>",1);function Se(J,E){ee(E,!1);const[z,P]=oe(),p=()=>ne(e(C),"$focusedIndex",z),w=h(),S=h(),s=h();let Z=W(E,"onSave",8),i=W(E,"rule",8);const x=[{label:"Always",value:R.ALWAYS_ATTACHED,description:"These Rules will be included in every message you send to the agent."},{label:"Manual",value:R.MANUAL,description:"These Rules will be included when manually tagged in your message. You can tag Rules by @-mentioning them."},{label:"Auto",value:R.AGENT_REQUESTED,description:"These Rules will be included when the Agent decides to fetch them based on this file's description."}];let C=h(void 0),I=h(()=>{});Q(()=>_(i()),()=>{v(w,i().path)}),Q(()=>_(i()),()=>{v(S,i().type)}),Q(()=>e(S),()=>{v(s,x.find(t=>t.value===e(S)))}),ae(),se();var B=te(),j=A(B),K=t=>{fe(t,{content:"Workspace guidelines are always applied",children:(r,X)=>{F(r,{color:"accent",size:1,disabled:!0,children:(m,D)=>{var L=y("Always");a(m,L)},$$slots:{default:!0}})},$$slots:{default:!0}})},O=t=>{d.Root(t,{get requestClose(){return e(I)},set requestClose(r){v(I,r)},get focusedIndex(){return e(C)},set focusedIndex(r){de(v(C,r),"$focusedIndex",z)},children:(r,X)=>{var m=$e(),D=A(m);d.Trigger(D,{children:(M,V)=>{var c=pe(),g=re(c);F(g,{color:"neutral",size:1,variant:"soft",children:(u,N)=>{var l=y();U(()=>Y(l,(e(s),f(()=>e(s).label)))),a(u,l)},$$slots:{default:!0,iconRight:(u,N)=>{he(u,{slot:"iconRight"})}}}),a(M,c)},$$slots:{default:!0}});var L=q(D,2);d.Content(L,{side:"bottom",align:"start",children:(M,V)=>{var c=ge(),g=A(c);ce(g,1,()=>x,ue,(l,o)=>{const $=ie(()=>(e(s),e(o),f(()=>e(s).label===e(o).label)));d.Item(l,{onSelect:()=>async function(n){e(I)();try{await Z()(n.value,n.value!==R.AGENT_REQUESTED||i().description?i().description:"Example description")}catch(b){console.error("RulesModeSelector: Error in onSave:",b)}}(e(o)),get highlight(){return e($)},children:(n,b)=>{var G=y();U(()=>Y(G,(e(o),f(()=>e(o).label)))),a(n,G)},$$slots:{default:!0}})});var u=q(g,2),N=l=>{var o=me(),$=A(o);d.Separator($,{});var n=q($,2);d.Label(n,{children:(b,G)=>{var H=y();U(()=>Y(H,(p(),e(s),f(()=>p()!==void 0?x[p()].description:e(s).description)))),a(b,H)},$$slots:{default:!0}}),a(l,o)};k(u,l=>{(p()!==void 0||e(s))&&l(N)}),a(M,c)},$$slots:{default:!0}}),a(r,m)},$$slots:{default:!0},$$legacy:!0})};k(j,t=>{e(w),f(()=>e(w)===ve)?t(K):t(O,!1)}),a(J,B),le(),P()}export{Se as R};
