import{l as J,A as at,B as i,m as g,_ as N,a0 as st,D as lt,F as _,H as Q,I as M,J as X,b as $,C as c,L as s,M as vt,a7 as tt,K as R,Q as K,t as G,N as nt,G as it,P as H,$ as rt,z as ht,ak as dt,a as ft,a2 as pt,am as gt,R as p,S as m,T as $t,V as mt,Z as yt}from"./SpinnerAugment-B-W1rkU5.js";import{I as wt,b as l,a as xt}from"./IconButtonAugment-Cdot7Te3.js";import{T as bt,a as et}from"./CardAugment-6M90JowR.js";import{B as zt}from"./ButtonAugment-BsoJM5iW.js";import{B as Ct,b as Tt}from"./BaseTextInput-NmI9DKfY.js";var Lt=_("<!> <!> <!>",1),Rt=_('<div class="c-successful-button svelte-1dvyzw2"><!></div>');function Nt(P,t){var a;const Y=J(t,["children","$$slots","$$events","$$legacy"]),A=J(Y,["defaultColor","tooltip","stateVariant","onClick","tooltipDuration","icon","stickyColor","persistOnTooltipClose","tooltipNested"]);at(t,!1);const y=g(),w=g(),T=g();let x,b=i(t,"defaultColor",8),B=i(t,"tooltip",24,()=>{}),S=i(t,"stateVariant",24,()=>{}),Z=i(t,"onClick",8),E=i(t,"tooltipDuration",8,1500),u=i(t,"icon",8,!1),V=i(t,"stickyColor",8,!0),j=i(t,"persistOnTooltipClose",8,!1),q=i(t,"tooltipNested",24,()=>{}),r=g("neutral"),z=g(b()),I=g(void 0),D=g((a=B())==null?void 0:a.neutral);async function F(o){var h;try{c(r,await Z()(o)??"neutral")}catch{c(r,"failure")}c(D,(h=B())==null?void 0:h[s(r)]),clearTimeout(x),x=setTimeout(()=>{var f;(f=s(I))==null||f(),V()||c(r,"neutral")},E())}N(()=>(s(y),s(w),H(A)),()=>{c(y,A.variant),c(w,rt(A,["variant"]))}),N(()=>(H(S()),s(r),s(y)),()=>{var o;c(T,((o=S())==null?void 0:o[s(r)])??s(y))}),N(()=>(s(r),H(b())),()=>{s(r)==="success"?c(z,"success"):s(r)==="failure"?c(z,"error"):c(z,b())}),st(),lt();var v=Rt(),L=G(v);const n=vt(()=>(H(et),it(()=>[et.Hover])));bt(L,{onOpenChange:function(o){var h;j()||o||(clearTimeout(x),x=void 0,c(D,(h=B())==null?void 0:h.neutral),V()||c(r,"neutral"))},get content(){return s(D)},get triggerOn(){return s(n)},get nested(){return q()},get requestClose(){return s(I)},set requestClose(o){c(I,o)},children:(o,h)=>{var f=Q(),U=M(f),ct=O=>{wt(O,tt(()=>s(w),{get color(){return s(z)},get variant(){return s(T)},$$events:{click:F,keyup(e){l.call(this,t,e)},keydown(e){l.call(this,t,e)},mousedown(e){l.call(this,t,e)},mouseover(e){l.call(this,t,e)},focus(e){l.call(this,t,e)},mouseleave(e){l.call(this,t,e)},blur(e){l.call(this,t,e)},contextmenu(e){l.call(this,t,e)}},children:(e,W)=>{var d=Lt(),C=M(d);R(C,t,"iconLeft",{},null);var k=K(C,2);R(k,t,"default",{},null);var ut=K(k,2);R(ut,t,"iconRight",{},null),$(e,d)},$$slots:{default:!0}}))},ot=O=>{zt(O,tt(()=>s(w),{get color(){return s(z)},get variant(){return s(T)},$$events:{click:F,keyup(e){l.call(this,t,e)},keydown(e){l.call(this,t,e)},mousedown(e){l.call(this,t,e)},mouseover(e){l.call(this,t,e)},focus(e){l.call(this,t,e)},mouseleave(e){l.call(this,t,e)},blur(e){l.call(this,t,e)},contextmenu(e){l.call(this,t,e)}},children:(e,W)=>{var d=Q(),C=M(d);R(C,t,"default",{},null),$(e,d)},$$slots:{default:!0,iconLeft:(e,W)=>{var d=Q(),C=M(d);R(C,t,"iconLeft",{},null),$(e,d)},iconRight:(e,W)=>{var d=Q(),C=M(d);R(C,t,"iconRight",{},null),$(e,d)}}}))};X(U,O=>{u()?O(ct):O(ot,!1)}),$(o,f)},$$slots:{default:!0},$$legacy:!0}),$(P,v),nt()}var Ht=_('<label class="c-text-area-label svelte-c1sr7w"> </label>'),Et=_('<div class="c-text-area-label-container svelte-c1sr7w"><!> <!></div>'),It=_("<textarea></textarea>"),Ot=_('<div class="c-text-area svelte-c1sr7w"><!> <!></div>');function St(P,t){const Y=ht(t),A=J(t,["children","$$slots","$$events","$$legacy"]),y=J(A,["label","variant","size","color","resize","textInput","type","value","id"]);at(t,!1);const w=g(),T=g(),x=g();let b=i(t,"label",24,()=>{}),B=i(t,"variant",8,"surface"),S=i(t,"size",8,2),Z=i(t,"color",24,()=>{}),E=i(t,"resize",8,"none"),u=i(t,"textInput",28,()=>{}),V=i(t,"type",8,"default"),j=i(t,"value",12,""),q=i(t,"id",24,()=>{});function r(){if(!u())return;u(u().style.height="auto",!0);const v=.8*window.innerHeight,L=Math.min(u().scrollHeight,v);u(u().style.height=`${L}px`,!0),u(u().style.overflowY=u().scrollHeight>v?"auto":"hidden",!0)}dt(()=>{if(u()){r();const v=()=>r();return window.addEventListener("resize",v),()=>{window.removeEventListener("resize",v)}}}),N(()=>H(q()),()=>{c(w,q()||`text-field-${Math.random().toString(36).substring(2,11)}`)}),N(()=>(s(T),s(x),H(y)),()=>{c(T,y.class),c(x,rt(y,["class"]))}),st(),lt();var z=Ot(),I=G(z),D=v=>{var L=Et(),n=G(L),a=h=>{var f=Ht(),U=G(f);$t(()=>{mt(f,"for",s(w)),yt(U,b())}),$(h,f)};X(n,h=>{b()&&h(a)});var o=K(n,2);R(o,t,"topRightAction",{},null),$(v,L)};X(I,v=>{H(b()),it(()=>b()||Y.topRightAction)&&v(D)});var F=K(I,2);Ct(F,{get type(){return V()},get variant(){return B()},get size(){return S()},get color(){return Z()},children:(v,L)=>{var n=It();ft(n,a=>({id:s(w),spellCheck:"false",class:`c-text-area__input c-base-text-input__input ${s(T)}`,...s(x),[pt]:a}),[()=>({"c-textarea--resize-none":E()==="none","c-textarea--resize-both":E()==="both","c-textarea--resize-horizontal":E()==="horizontal","c-textarea--resize-vertical":E()==="vertical"})],"svelte-c1sr7w"),gt(n,a=>u(a),()=>u()),p(()=>Tt(n,j)),xt(n,a=>function(o){r();const h=()=>r();return o.addEventListener("input",h),setTimeout(r,0),{destroy(){o.removeEventListener("input",h)}}}(a)),p(()=>m("click",n,function(a){l.call(this,t,a)})),p(()=>m("focus",n,function(a){l.call(this,t,a)})),p(()=>m("keydown",n,function(a){l.call(this,t,a)})),p(()=>m("change",n,function(a){l.call(this,t,a)})),p(()=>m("input",n,function(a){l.call(this,t,a)})),p(()=>m("keyup",n,function(a){l.call(this,t,a)})),p(()=>m("blur",n,function(a){l.call(this,t,a)})),p(()=>m("select",n,function(a){l.call(this,t,a)})),p(()=>m("mouseup",n,function(a){l.call(this,t,a)})),$(v,n)},$$slots:{default:!0}}),$(P,z),nt()}export{Nt as S,St as T};
