import{A as w,B as t,_ as o,a0 as L,F as d,T as k,ab as A,W as F,L as r,m as n,V as N,Z as j,t as x,b as y,N as B,C as e,P as c}from"./SpinnerAugment-B-W1rkU5.js";var C=d("<span> </span>");function G(f,a){w(a,!1);let g=t(a,"class",8,""),p=t(a,"iconName",8,""),l=t(a,"fill",8,!1),m=t(a,"grade",8,"normal"),v=t(a,"title",24,()=>{}),b=n(),h=n(),i=n();o(()=>c(l()),()=>{e(b,l()?"1":"0")}),o(()=>c(l()),()=>{e(h,l()?"700":"400")}),o(()=>c(m()),()=>{switch(m()){case"low":e(i,"-25");break;case"normal":e(i,"0");break;case"high":e(i,"200")}}),L();var s=C(),$=x(s);k(()=>{A(s,1,`material-symbols-outlined ${g()}`,"svelte-htlsjs"),F(s,`font-variation-settings: 'FILL' ${r(b)??""}, 'wght' ${r(h)??""}, 'GRAD' ${r(i)??""};`),N(s,"title",v()),j($,p())}),y(f,s),B()}export{G as M};
