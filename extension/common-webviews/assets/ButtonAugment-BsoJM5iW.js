import{z as D,l as y,B as i,a7 as E,F as u,J as g,G as h,t as c,Q as z,T as N,ab as O,b as o,K as m,X as P,H as S,I as U,L as x,M as C}from"./SpinnerAugment-B-W1rkU5.js";import{B as V,b as s}from"./IconButtonAugment-Cdot7Te3.js";var W=u('<div class="c-button--icon svelte-p25y67"><!></div>'),Y=u('<div class="c-button--text svelte-p25y67"><!></div>'),Z=u('<div class="c-button--icon svelte-p25y67"><!></div>'),_=u("<div><!> <!> <!></div>");function st(w,t){const d=D(t),B=y(t,["children","$$slots","$$events","$$legacy"]),L=y(B,["size","variant","color","highContrast","disabled","radius","loading","alignment"]);let n=i(t,"size",8,2),$=i(t,"variant",8,"solid"),R=i(t,"color",8,"neutral"),F=i(t,"highContrast",8,!1),G=i(t,"disabled",8,!1),H=i(t,"radius",8,"medium"),I=i(t,"loading",8,!1),J=i(t,"alignment",8,"center");V(w,E({get size(){return n()},get variant(){return $()},get color(){return R()},get highContrast(){return F()},get disabled(){return G()},get loading(){return I()},get alignment(){return J()},get radius(){return H()}},()=>L,{$$events:{click(e){s.call(this,t,e)},keyup(e){s.call(this,t,e)},keydown(e){s.call(this,t,e)},mousedown(e){s.call(this,t,e)},mouseover(e){s.call(this,t,e)},focus(e){s.call(this,t,e)},mouseleave(e){s.call(this,t,e)},blur(e){s.call(this,t,e)},contextmenu(e){s.call(this,t,e)}},children:(e,k)=>{var v=_(),b=c(v),j=a=>{var l=W(),r=c(l);m(r,t,"iconLeft",{},null),o(a,l)};g(b,a=>{h(()=>d.iconLeft)&&a(j)});var f=z(b,2),A=a=>{var l=Y(),r=c(l);const Q=C(()=>n()===.5?1:n()),T=C(()=>$()==="ghost"||n()===.5?"regular":"medium");P(r,{get size(){return x(Q)},get weight(){return x(T)},children:(X,tt)=>{var p=S(),q=U(p);m(q,t,"default",{},null),o(X,p)},$$slots:{default:!0}}),o(a,l)};g(f,a=>{h(()=>d.default)&&a(A)});var K=z(f,2),M=a=>{var l=Z(),r=c(l);m(r,t,"iconRight",{},null),o(a,l)};g(K,a=>{h(()=>d.iconRight)&&a(M)}),N(()=>O(v,1,`c-button--content c-button--size-${n()}`,"svelte-p25y67")),o(e,v)},$$slots:{default:!0}}))}export{st as B};
