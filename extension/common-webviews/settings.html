<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Augment - Tool Configuration</title>
    <script nonce="nonce-JyU+9mzt9o8d1x39tFtCuA==">
/**
 * Monaco bootstrap script
 *
 * This script is included directly in HTML files to load Monaco editor.
 * It's kept as a simple JS file to avoid any build/transpilation requirements.
 */

// Define the Monaco CDN version
const MONACO_VERSION = "0.52.2";
const MONACO_CDN_BASE = `https://cdnjs.cloudflare.com/ajax/libs/monaco-editor/${MONACO_VERSION}/min`;

// Initialize augmentDeps if it doesn't exist
window.augmentDeps = window.augmentDeps || {};

// Create a promise that will resolve when Monaco is ready
let monacoResolve;
window.augmentDeps.monaco = new Promise((resolve) => {
  monacoResolve = resolve;
});

// If Monaco is already loaded, don't load it again
if (window.monaco) {
  console.log("Monaco already loaded, skipping bootstrap");
  initializeMonacoDeps();
} else {
  // Load the Monaco loader script
  const loaderScript = document.createElement("script");
  loaderScript.src = `${MONACO_CDN_BASE}/vs/loader.min.js`;
  loaderScript.onload = initializeMonaco;
  document.head.appendChild(loaderScript);
}

// Initialize Monaco after the loader script has loaded
function initializeMonaco() {
  // require is provided by loader.min.js
  require.config({
    paths: { vs: `${MONACO_CDN_BASE}/vs` },
  });

  require(["vs/editor/editor.main"], () => {
    initializeMonacoDeps();
  });
}

// Initialize Monaco dependencies after Monaco has loaded
function initializeMonacoDeps() {
  // Resolve the monaco promise
  if (monacoResolve) {
    monacoResolve(window.monaco);
  }
}

</script>
    <meta property="csp-nonce" nonce="nonce-JyU+9mzt9o8d1x39tFtCuA==">
    <script type="module" crossorigin src="./assets/settings-BX0qT5WR.js" nonce="nonce-JyU+9mzt9o8d1x39tFtCuA=="></script>
    <link rel="modulepreload" crossorigin href="./assets/SpinnerAugment-B-W1rkU5.js" nonce="nonce-JyU+9mzt9o8d1x39tFtCuA==">
    <link rel="modulepreload" crossorigin href="./assets/design-system-init-CO3OATOl.js" nonce="nonce-JyU+9mzt9o8d1x39tFtCuA==">
    <link rel="modulepreload" crossorigin href="./assets/IconButtonAugment-Cdot7Te3.js" nonce="nonce-JyU+9mzt9o8d1x39tFtCuA==">
    <link rel="modulepreload" crossorigin href="./assets/async-messaging-h5fbTmxI.js" nonce="nonce-JyU+9mzt9o8d1x39tFtCuA==">
    <link rel="modulepreload" crossorigin href="./assets/message-broker-DiyMl4p8.js" nonce="nonce-JyU+9mzt9o8d1x39tFtCuA==">
    <link rel="modulepreload" crossorigin href="./assets/types-CGlLNakm.js" nonce="nonce-JyU+9mzt9o8d1x39tFtCuA==">
    <link rel="modulepreload" crossorigin href="./assets/focusTrapStack-wx6NNrdM.js" nonce="nonce-JyU+9mzt9o8d1x39tFtCuA==">
    <link rel="modulepreload" crossorigin href="./assets/isObjectLike-KnT2wtGt.js" nonce="nonce-JyU+9mzt9o8d1x39tFtCuA==">
    <link rel="modulepreload" crossorigin href="./assets/CardAugment-6M90JowR.js" nonce="nonce-JyU+9mzt9o8d1x39tFtCuA==">
    <link rel="modulepreload" crossorigin href="./assets/BaseTextInput-NmI9DKfY.js" nonce="nonce-JyU+9mzt9o8d1x39tFtCuA==">
    <link rel="modulepreload" crossorigin href="./assets/index-BZ1aQDkr.js" nonce="nonce-JyU+9mzt9o8d1x39tFtCuA==">
    <link rel="modulepreload" crossorigin href="./assets/user-vtiOgHx6.js" nonce="nonce-JyU+9mzt9o8d1x39tFtCuA==">
    <link rel="modulepreload" crossorigin href="./assets/download-WeyF9LzE.js" nonce="nonce-JyU+9mzt9o8d1x39tFtCuA==">
    <link rel="modulepreload" crossorigin href="./assets/keypress-DD1aQVr0.js" nonce="nonce-JyU+9mzt9o8d1x39tFtCuA==">
    <link rel="modulepreload" crossorigin href="./assets/VSCodeCodicon-PwzMb_dE.js" nonce="nonce-JyU+9mzt9o8d1x39tFtCuA==">
    <link rel="modulepreload" crossorigin href="./assets/svelte-component-BFVtr7-z.js" nonce="nonce-JyU+9mzt9o8d1x39tFtCuA==">
    <link rel="modulepreload" crossorigin href="./assets/CollapseButtonAugment-FMX0ZXJp.js" nonce="nonce-JyU+9mzt9o8d1x39tFtCuA==">
    <link rel="modulepreload" crossorigin href="./assets/index-C0pD_8h5.js" nonce="nonce-JyU+9mzt9o8d1x39tFtCuA==">
    <link rel="modulepreload" crossorigin href="./assets/ellipsis-tqj1KnpB.js" nonce="nonce-JyU+9mzt9o8d1x39tFtCuA==">
    <link rel="modulepreload" crossorigin href="./assets/Drawer-DF8SmKU6.js" nonce="nonce-JyU+9mzt9o8d1x39tFtCuA==">
    <link rel="modulepreload" crossorigin href="./assets/ButtonAugment-BsoJM5iW.js" nonce="nonce-JyU+9mzt9o8d1x39tFtCuA==">
    <link rel="modulepreload" crossorigin href="./assets/CalloutAugment-BjqqGsdn.js" nonce="nonce-JyU+9mzt9o8d1x39tFtCuA==">
    <link rel="modulepreload" crossorigin href="./assets/pen-to-square-CZY7t64W.js" nonce="nonce-JyU+9mzt9o8d1x39tFtCuA==">
    <link rel="modulepreload" crossorigin href="./assets/TextAreaAugment-DEiWzBPO.js" nonce="nonce-JyU+9mzt9o8d1x39tFtCuA==">
    <link rel="modulepreload" crossorigin href="./assets/copy-C63CnUj2.js" nonce="nonce-JyU+9mzt9o8d1x39tFtCuA==">
    <link rel="modulepreload" crossorigin href="./assets/chevron-down-BvquqDa7.js" nonce="nonce-JyU+9mzt9o8d1x39tFtCuA==">
    <link rel="modulepreload" crossorigin href="./assets/index-Bmm_dv1C.js" nonce="nonce-JyU+9mzt9o8d1x39tFtCuA==">
    <link rel="modulepreload" crossorigin href="./assets/index-BPaFHx0a.js" nonce="nonce-JyU+9mzt9o8d1x39tFtCuA==">
    <link rel="modulepreload" crossorigin href="./assets/MarkdownEditor-BtN9HMBx.js" nonce="nonce-JyU+9mzt9o8d1x39tFtCuA==">
    <link rel="modulepreload" crossorigin href="./assets/RulesModeSelector-CLcZ4jbN.js" nonce="nonce-JyU+9mzt9o8d1x39tFtCuA==">
    <link rel="modulepreload" crossorigin href="./assets/ModalAugment-DU_D4O_Y.js" nonce="nonce-JyU+9mzt9o8d1x39tFtCuA==">
    <link rel="stylesheet" crossorigin href="./assets/SpinnerAugment-DoxdFmoV.css" nonce="nonce-JyU+9mzt9o8d1x39tFtCuA==">
    <link rel="stylesheet" crossorigin href="./assets/design-system-init-DgOX1UWm.css" nonce="nonce-JyU+9mzt9o8d1x39tFtCuA==">
    <link rel="stylesheet" crossorigin href="./assets/IconButtonAugment-B4afvB2A.css" nonce="nonce-JyU+9mzt9o8d1x39tFtCuA==">
    <link rel="stylesheet" crossorigin href="./assets/user-dXUx3CYB.css" nonce="nonce-JyU+9mzt9o8d1x39tFtCuA==">
    <link rel="stylesheet" crossorigin href="./assets/index-D9au8v71.css" nonce="nonce-JyU+9mzt9o8d1x39tFtCuA==">
    <link rel="stylesheet" crossorigin href="./assets/download-C1VayJBB.css" nonce="nonce-JyU+9mzt9o8d1x39tFtCuA==">
    <link rel="stylesheet" crossorigin href="./assets/VSCodeCodicon-DVaocTud.css" nonce="nonce-JyU+9mzt9o8d1x39tFtCuA==">
    <link rel="stylesheet" crossorigin href="./assets/CollapseButtonAugment-B1EFpTQV.css" nonce="nonce-JyU+9mzt9o8d1x39tFtCuA==">
    <link rel="stylesheet" crossorigin href="./assets/Drawer-u8LRIFRf.css" nonce="nonce-JyU+9mzt9o8d1x39tFtCuA==">
    <link rel="stylesheet" crossorigin href="./assets/CardAugment-BtrmW4jo.css" nonce="nonce-JyU+9mzt9o8d1x39tFtCuA==">
    <link rel="stylesheet" crossorigin href="./assets/ButtonAugment-DORgvEFm.css" nonce="nonce-JyU+9mzt9o8d1x39tFtCuA==">
    <link rel="stylesheet" crossorigin href="./assets/CalloutAugment-C-z-uXWx.css" nonce="nonce-JyU+9mzt9o8d1x39tFtCuA==">
    <link rel="stylesheet" crossorigin href="./assets/TextAreaAugment-k8sG2hbx.css" nonce="nonce-JyU+9mzt9o8d1x39tFtCuA==">
    <link rel="stylesheet" crossorigin href="./assets/index-BlHvDt2c.css" nonce="nonce-JyU+9mzt9o8d1x39tFtCuA==">
    <link rel="stylesheet" crossorigin href="./assets/MarkdownEditor-B6vv3aGc.css" nonce="nonce-JyU+9mzt9o8d1x39tFtCuA==">
    <link rel="stylesheet" crossorigin href="./assets/RulesModeSelector-Qv_62MPy.css" nonce="nonce-JyU+9mzt9o8d1x39tFtCuA==">
    <link rel="stylesheet" crossorigin href="./assets/ModalAugment-CM3byOYD.css" nonce="nonce-JyU+9mzt9o8d1x39tFtCuA==">
    <link rel="stylesheet" crossorigin href="./assets/BaseTextInput-CEzLOEg8.css" nonce="nonce-JyU+9mzt9o8d1x39tFtCuA==">
    <link rel="stylesheet" crossorigin href="./assets/settings-CGCCAaSg.css" nonce="nonce-JyU+9mzt9o8d1x39tFtCuA==">
  </head>
  <body>
    <div id="app"></div>
  </body>
</html>
